<?php
/**
 * Create Maintenance Schedule View
 * 
 * This file displays the form to create a new maintenance schedule.
 */

// Set page title
$pageTitle = __('schedule_maintenance');
$pageSubtitle = __('schedule_new_maintenance_task');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 text-gradient mb-1"><?php echo __('schedule_maintenance'); ?></h1>
        <p class="text-muted mb-0"><?php echo __('schedule_new_maintenance_task'); ?></p>
    </div>

    <a href="<?php echo getBaseUrl(); ?>/maintenance" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_list'); ?>
    </a>
</div>

<div class="row">
    <div class="col-lg-8 col-md-10 mx-auto">
        <div class="card fade-in-up">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-plus me-2"></i><?php echo __('schedule_information'); ?>
                </h5>
            </div>
            <div class="card-body">
        <form action="<?php echo getBaseUrl(); ?>/maintenance/create_schedule" method="post" id="scheduleForm">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
            
            <?php if (!empty($errors['general'])): ?>
                <div class="alert alert-danger">
                    <?php echo $errors['general']; ?>
                </div>
            <?php endif; ?>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="hospital_id" class="form-label"><?php echo __('hospital'); ?> <span class="text-danger">*</span></label>
                        <select class="form-select <?php echo !empty($errors['hospital_id']) ? 'is-invalid' : ''; ?>" id="hospital_id" name="hospital_id" required>
                            <option value=""><?php echo __('select_hospital'); ?></option>
                            <?php foreach ($hospitals as $hospital): ?>
                                <option value="<?php echo $hospital['id']; ?>" <?php echo ($scheduleData['hospital_id'] ?? '') == $hospital['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($hospital['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <?php if (!empty($errors['hospital_id'])): ?>
                            <div class="invalid-feedback"><?php echo $errors['hospital_id']; ?></div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="device_id" class="form-label"><?php echo __('device'); ?> <span class="text-danger">*</span></label>
                        <select class="form-select <?php echo !empty($errors['device_id']) ? 'is-invalid' : ''; ?>" id="device_id" name="device_id" required>
                            <option value=""><?php echo __('select_device'); ?></option>
                            <?php foreach ($devices as $device): ?>
                                <option value="<?php echo $device['id']; ?>" <?php echo ($scheduleData['device_id'] ?? '') == $device['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($device['name'] . ' (' . $device['serial_number'] . ')'); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <?php if (!empty($errors['device_id'])): ?>
                            <div class="invalid-feedback"><?php echo $errors['device_id']; ?></div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="title" class="form-label"><?php echo __('title'); ?> <span class="text-danger">*</span></label>
                        <input type="text" class="form-control <?php echo !empty($errors['title']) ? 'is-invalid' : ''; ?>" id="title" name="title" value="<?php echo htmlspecialchars($scheduleData['title'] ?? ''); ?>" required>
                        <?php if (!empty($errors['title'])): ?>
                            <div class="invalid-feedback"><?php echo $errors['title']; ?></div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="scheduled_date" class="form-label"><?php echo __('scheduled_date'); ?> <span class="text-danger">*</span></label>
                        <input type="date" class="form-control <?php echo !empty($errors['scheduled_date']) ? 'is-invalid' : ''; ?>" id="scheduled_date" name="scheduled_date" value="<?php echo $scheduleData['scheduled_date'] ?? date('Y-m-d'); ?>" required>
                        <?php if (!empty($errors['scheduled_date'])): ?>
                            <div class="invalid-feedback"><?php echo $errors['scheduled_date']; ?></div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="frequency" class="form-label"><?php echo __('frequency'); ?></label>
                        <select class="form-select" id="frequency" name="frequency">
                            <option value="once" <?php echo ($scheduleData['frequency'] ?? 'once') == 'once' ? 'selected' : ''; ?>><?php echo __('once'); ?></option>
                            <option value="daily" <?php echo ($scheduleData['frequency'] ?? '') == 'daily' ? 'selected' : ''; ?>><?php echo __('daily'); ?></option>
                            <option value="weekly" <?php echo ($scheduleData['frequency'] ?? '') == 'weekly' ? 'selected' : ''; ?>><?php echo __('weekly'); ?></option>
                            <option value="monthly" <?php echo ($scheduleData['frequency'] ?? '') == 'monthly' ? 'selected' : ''; ?>><?php echo __('monthly'); ?></option>
                            <option value="quarterly" <?php echo ($scheduleData['frequency'] ?? '') == 'quarterly' ? 'selected' : ''; ?>><?php echo __('quarterly'); ?></option>
                            <option value="yearly" <?php echo ($scheduleData['frequency'] ?? '') == 'yearly' ? 'selected' : ''; ?>><?php echo __('yearly'); ?></option>
                        </select>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="priority" class="form-label"><?php echo __('priority'); ?></label>
                        <select class="form-select" id="priority" name="priority">
                            <option value="low" <?php echo ($scheduleData['priority'] ?? '') == 'low' ? 'selected' : ''; ?>><?php echo __('low'); ?></option>
                            <option value="medium" <?php echo ($scheduleData['priority'] ?? 'medium') == 'medium' ? 'selected' : ''; ?>><?php echo __('medium'); ?></option>
                            <option value="high" <?php echo ($scheduleData['priority'] ?? '') == 'high' ? 'selected' : ''; ?>><?php echo __('high'); ?></option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="description" class="form-label"><?php echo __('description'); ?></label>
                <textarea class="form-control <?php echo !empty($errors['description']) ? 'is-invalid' : ''; ?>" id="description" name="description" rows="4"><?php echo htmlspecialchars($scheduleData['description'] ?? ''); ?></textarea>
                <?php if (!empty($errors['description'])): ?>
                    <div class="invalid-feedback"><?php echo $errors['description']; ?></div>
                <?php endif; ?>
            </div>
            
            <div class="mb-3">
                <label for="notes" class="form-label"><?php echo __('notes'); ?></label>
                <textarea class="form-control" id="notes" name="notes" rows="3"><?php echo htmlspecialchars($scheduleData['notes'] ?? ''); ?></textarea>
            </div>
            
            <div class="d-flex justify-content-between align-items-center pt-3 border-top">
                <a href="<?php echo getBaseUrl(); ?>/maintenance" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>
                </a>
                <div class="d-flex gap-2">
                    <button type="reset" class="btn btn-outline-secondary">
                        <i class="fas fa-undo me-2"></i><?php echo __('reset'); ?>
                    </button>
                    <button type="submit" class="btn btn-primary" id="submitBtn">
                        <span class="btn-text">
                            <i class="fas fa-calendar-plus me-2"></i><?php echo __('schedule_maintenance'); ?>
                        </span>
                        <span class="loading-spinner d-none"></span>
                    </button>
                </div>
            </div>
        </form>
    </div>
        </div>
    </div>
</div>

<script>
// Load devices when hospital changes
document.getElementById('hospital_id').addEventListener('change', function() {
    const hospitalId = this.value;
    const deviceSelect = document.getElementById('device_id');
    
    // Clear current options
    deviceSelect.innerHTML = '<option value=""><?php echo __('select_device'); ?></option>';
    
    if (hospitalId) {
        // Fetch devices for the selected hospital
        fetch(`<?php echo getBaseUrl(); ?>/api/get_devices?hospital_id=${hospitalId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    data.data.forEach(device => {
                        const option = document.createElement('option');
                        option.value = device.id;
                        option.textContent = device.name + ' (' + device.serial_number + ')';
                        deviceSelect.appendChild(option);
                    });
                }
            })
            .catch(error => {
                console.error('Error loading devices:', error);
            });
    }
});

// Auto-fill title based on device selection
document.getElementById('device_id').addEventListener('change', function() {
    const deviceSelect = this;
    const titleInput = document.getElementById('title');
    
    if (deviceSelect.value && !titleInput.value) {
        const deviceText = deviceSelect.options[deviceSelect.selectedIndex].text;
        const deviceName = deviceText.split(' (')[0];
        titleInput.value = 'Routine Maintenance - ' + deviceName;
    }
});

// Set minimum date to today
document.getElementById('scheduled_date').min = new Date().toISOString().split('T')[0];
</script>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
