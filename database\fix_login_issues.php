<?php
/**
 * Database Migration Script - Fix Login Issues
 * 
 * This script fixes the login issues by:
 * 1. Adding the missing 'permissions' column to the users table
 * 2. Creating the missing 'activity_logs' table
 * 3. Updating existing users with default permissions
 */

// Set default timezone
date_default_timezone_set('UTC');

echo "<h1>Medical Device Management System - Login Issues Fix</h1>";

// Check if config file exists
if (!file_exists('../config/database.php')) {
    echo "<p style='color: red;'>Error: Database configuration file not found!</p>";
    echo "<p>Please create config/database.php with your database settings.</p>";
    exit;
}

// Load database configuration
require_once '../config/database.php';

// Include auth functions for getDefaultPermissions
require_once '../includes/auth.php';

echo "<p>Starting database migration to fix login issues...</p>";

try {
    // Check if permissions column exists
    $stmt = $pdo->prepare("SHOW COLUMNS FROM users LIKE 'permissions'");
    $stmt->execute();
    $permissionsColumnExists = $stmt->fetch() !== false;
    
    if (!$permissionsColumnExists) {
        echo "<p>Adding 'permissions' column to users table...</p>";
        $pdo->exec("ALTER TABLE users ADD COLUMN permissions JSON NULL AFTER role");
        echo "<p style='color: green;'>✓ Added 'permissions' column to users table</p>";
        
        // Update existing users with default permissions
        echo "<p>Updating existing users with default permissions...</p>";
        $stmt = $pdo->prepare("SELECT id, role FROM users WHERE permissions IS NULL");
        $stmt->execute();
        $users = $stmt->fetchAll();
        
        foreach ($users as $user) {
            $permissions = getDefaultPermissions($user['role']);
            $updateStmt = $pdo->prepare("UPDATE users SET permissions = ? WHERE id = ?");
            $updateStmt->execute([json_encode($permissions), $user['id']]);
        }
        echo "<p style='color: green;'>✓ Updated " . count($users) . " users with default permissions</p>";
    } else {
        echo "<p style='color: blue;'>ℹ 'permissions' column already exists in users table</p>";
    }
    
    // Check if activity_logs table exists
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'activity_logs'");
    $stmt->execute();
    $activityLogsTableExists = $stmt->fetch() !== false;
    
    if (!$activityLogsTableExists) {
        echo "<p>Creating 'activity_logs' table...</p>";
        $pdo->exec("
            CREATE TABLE activity_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NULL,
                action VARCHAR(255) NOT NULL,
                details TEXT NULL,
                ip_address VARCHAR(45) NULL,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
                INDEX (user_id),
                INDEX (action),
                INDEX (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<p style='color: green;'>✓ Created 'activity_logs' table</p>";
    } else {
        echo "<p style='color: blue;'>ℹ 'activity_logs' table already exists</p>";
    }
    
    echo "<p style='color: green; font-weight: bold;'>✓ Database migration completed successfully!</p>";
    echo "<p>You can now try logging in again.</p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    exit;
}

echo '<p><a href="../login.php" style="display: inline-block; padding: 10px 15px; background-color: #007bff; color: #fff; text-decoration: none; border-radius: 3px;">Go to Login Page</a></p>';
?>
