<?php
/**
 * View Maintenance Log View
 * 
 * This file displays the details of a maintenance log.
 */

// Set page title
$pageTitle = __('view_maintenance_log');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3"><?php echo __('maintenance_log'); ?></h1>
    
    <div class="btn-group">
        <a href="<?php echo getBaseUrl(); ?>/maintenance/logs" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_logs'); ?>
        </a>
        
        <?php if (hasPermission('manage_maintenance')): ?>
            <a href="<?php echo getBaseUrl(); ?>/maintenance/edit_log/<?php echo $log['id']; ?>" class="btn btn-warning">
                <i class="fas fa-edit me-2"></i><?php echo __('edit_log'); ?>
            </a>
        <?php endif; ?>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <!-- Log Details -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clipboard-list me-2"></i><?php echo __('maintenance_log_details'); ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('device'); ?></label>
                            <div class="d-flex align-items-center">
                                <div class="device-icon me-2">
                                    <i class="fas fa-medical-kit text-primary fa-lg"></i>
                                </div>
                                <div>
                                    <div class="h6 mb-0"><?php echo htmlspecialchars($device['name']); ?></div>
                                    <small class="text-muted"><?php echo htmlspecialchars($device['serial_number']); ?></small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('location'); ?></label>
                            <div class="fw-bold">
                                <?php echo htmlspecialchars($device['hospital_name']); ?> - <?php echo htmlspecialchars($device['department_name']); ?>
                            </div>
                            <?php if ($device['location']): ?>
                                <small class="text-muted"><?php echo htmlspecialchars($device['location']); ?></small>
                            <?php endif; ?>
                        </div>
                        
                        <?php if ($log['maintenance_schedule_id']): ?>
                            <div class="mb-3">
                                <label class="form-label text-muted"><?php echo __('related_schedule'); ?></label>
                                <div>
                                    <a href="<?php echo getBaseUrl(); ?>/maintenance/view_schedule/<?php echo $log['maintenance_schedule_id']; ?>" class="text-decoration-none">
                                        <i class="fas fa-calendar-alt me-1"></i><?php echo htmlspecialchars($log['schedule_title']); ?>
                                    </a>
                                </div>
                                <small class="text-muted"><?php echo __('scheduled_for'); ?> <?php echo date('M d, Y', strtotime($log['scheduled_date'])); ?></small>
                            </div>
                        <?php else: ?>
                            <div class="mb-3">
                                <label class="form-label text-muted"><?php echo __('maintenance_type'); ?></label>
                                <div>
                                    <span class="badge bg-info"><?php echo __('unscheduled_maintenance'); ?></span>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('performed_date'); ?></label>
                            <div class="h5 text-primary"><?php echo date('F d, Y', strtotime($log['performed_date'])); ?></div>
                            <small class="text-muted"><?php echo date('l, H:i', strtotime($log['performed_date'])); ?></small>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('status'); ?></label>
                            <div>
                                <span class="badge bg-<?php echo getMaintenanceStatusColor($log['status']); ?> fs-6">
                                    <i class="fas fa-<?php echo getMaintenanceStatusIcon($log['status']); ?> me-1"></i>
                                    <?php echo __($log['status']); ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('performed_by'); ?></label>
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-2">
                                    <div class="bg-info text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 32px; height: 32px; font-size: 0.8rem;">
                                        <?php echo strtoupper(substr($log['performed_by_name'], 0, 1)); ?>
                                    </div>
                                </div>
                                <div>
                                    <div class="fw-bold"><?php echo htmlspecialchars($log['performed_by_name']); ?></div>
                                    <small class="text-muted"><?php echo __($log['performed_by_role']); ?></small>
                                </div>
                            </div>
                        </div>
                        
                        <?php if ($log['duration']): ?>
                            <div class="mb-3">
                                <label class="form-label text-muted"><?php echo __('duration'); ?></label>
                                <div class="fw-bold">
                                    <i class="fas fa-clock me-1"></i>
                                    <?php echo $log['duration']; ?> <?php echo __('minutes'); ?>
                                    <small class="text-muted">(<?php echo gmdate('H:i', $log['duration'] * 60); ?>)</small>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <?php if ($log['notes']): ?>
                    <div class="mb-3">
                        <label class="form-label text-muted"><?php echo __('maintenance_notes'); ?></label>
                        <div class="border rounded p-3 bg-light">
                            <?php echo nl2br(htmlspecialchars($log['notes'])); ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('log_created'); ?></label>
                            <div class="fw-bold"><?php echo date('M d, Y H:i', strtotime($log['created_at'])); ?></div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('last_updated'); ?></label>
                            <div class="fw-bold"><?php echo date('M d, Y H:i', strtotime($log['updated_at'])); ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Parts and Materials Used (if available) -->
        <?php if (!empty($log['parts_used'])): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cogs me-2"></i><?php echo __('parts_and_materials'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th><?php echo __('part_name'); ?></th>
                                    <th><?php echo __('quantity'); ?></th>
                                    <th><?php echo __('cost'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($log['parts_used'] as $part): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($part['name']); ?></td>
                                        <td><?php echo $part['quantity']; ?></td>
                                        <td><?php echo formatCurrency($part['cost']); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Photos/Attachments (if available) -->
        <?php if (!empty($log['attachments'])): ?>
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-paperclip me-2"></i><?php echo __('attachments'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($log['attachments'] as $attachment): ?>
                            <div class="col-md-3 mb-3">
                                <div class="card">
                                    <?php if (in_array(strtolower(pathinfo($attachment['filename'], PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif'])): ?>
                                        <img src="<?php echo getBaseUrl(); ?>/<?php echo $attachment['path']; ?>" class="card-img-top" alt="<?php echo htmlspecialchars($attachment['filename']); ?>" style="height: 150px; object-fit: cover;">
                                    <?php else: ?>
                                        <div class="card-img-top d-flex align-items-center justify-content-center bg-light" style="height: 150px;">
                                            <i class="fas fa-file fa-3x text-muted"></i>
                                        </div>
                                    <?php endif; ?>
                                    <div class="card-body p-2">
                                        <small class="text-muted"><?php echo htmlspecialchars($attachment['filename']); ?></small>
                                        <div class="mt-1">
                                            <a href="<?php echo getBaseUrl(); ?>/<?php echo $attachment['path']; ?>" class="btn btn-sm btn-outline-primary" target="_blank">
                                                <i class="fas fa-download me-1"></i><?php echo __('download'); ?>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
    
    <div class="col-md-4">
        <!-- Quick Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i><?php echo __('quick_actions'); ?>
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <?php if (hasPermission('manage_maintenance')): ?>
                        <a href="<?php echo getBaseUrl(); ?>/maintenance/edit_log/<?php echo $log['id']; ?>" class="btn btn-warning">
                            <i class="fas fa-edit me-2"></i><?php echo __('edit_log'); ?>
                        </a>
                        
                        <a href="<?php echo getBaseUrl(); ?>/maintenance/create_log?device_id=<?php echo $device['id']; ?>" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i><?php echo __('new_log'); ?>
                        </a>
                    <?php endif; ?>
                    
                    <a href="<?php echo getBaseUrl(); ?>/devices/view/<?php echo $device['id']; ?>" class="btn btn-outline-info">
                        <i class="fas fa-medical-kit me-2"></i><?php echo __('view_device'); ?>
                    </a>
                    
                    <?php if ($log['maintenance_schedule_id']): ?>
                        <a href="<?php echo getBaseUrl(); ?>/maintenance/view_schedule/<?php echo $log['maintenance_schedule_id']; ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-calendar-alt me-2"></i><?php echo __('view_schedule'); ?>
                        </a>
                    <?php endif; ?>
                    
                    <a href="<?php echo getBaseUrl(); ?>/maintenance/logs?device_id=<?php echo $device['id']; ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-list me-2"></i><?php echo __('all_device_logs'); ?>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Log Summary -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i><?php echo __('log_summary'); ?>
                </h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <div class="d-flex justify-content-between mb-2">
                        <span><?php echo __('log_id'); ?>:</span>
                        <span class="fw-bold">#<?php echo str_pad($log['id'], 6, '0', STR_PAD_LEFT); ?></span>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-2">
                        <span><?php echo __('device_id'); ?>:</span>
                        <span class="fw-bold">#<?php echo str_pad($device['id'], 6, '0', STR_PAD_LEFT); ?></span>
                    </div>
                    
                    <?php if ($log['maintenance_schedule_id']): ?>
                        <div class="d-flex justify-content-between mb-2">
                            <span><?php echo __('schedule_id'); ?>:</span>
                            <span class="fw-bold">#<?php echo str_pad($log['maintenance_schedule_id'], 6, '0', STR_PAD_LEFT); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <div class="d-flex justify-content-between mb-2">
                        <span><?php echo __('status'); ?>:</span>
                        <span class="badge bg-<?php echo getMaintenanceStatusColor($log['status']); ?>"><?php echo __($log['status']); ?></span>
                    </div>
                    
                    <?php if ($log['duration']): ?>
                        <div class="d-flex justify-content-between">
                            <span><?php echo __('time_spent'); ?>:</span>
                            <span class="fw-bold"><?php echo $log['duration']; ?> min</span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Device Status -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-medical-kit me-2"></i><?php echo __('device_status'); ?>
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <div class="mb-2">
                        <span class="badge bg-<?php echo getDeviceStatusColor($device['status']); ?> fs-6">
                            <?php echo __($device['status']); ?>
                        </span>
                    </div>
                    
                    <div class="small text-muted">
                        <?php echo __('current_device_status'); ?>
                    </div>
                    
                    <?php if ($device['last_maintenance']): ?>
                        <hr>
                        <div class="small">
                            <div class="text-muted"><?php echo __('last_maintenance'); ?>:</div>
                            <div class="fw-bold"><?php echo date('M d, Y', strtotime($device['last_maintenance'])); ?></div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
