<?php
/**
 * Edit Profile View
 * 
 * This file displays the form to edit user profile information.
 */

// Set page title
$pageTitle = __('edit_profile');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3"><?php echo __('edit_profile'); ?></h1>
    
    <a href="<?php echo getBaseUrl(); ?>/profile" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_profile'); ?>
    </a>
</div>

<?php if (!empty($errors)): ?>
    <div class="alert alert-danger">
        <h6><?php echo __('please_fix_errors'); ?>:</h6>
        <ul class="mb-0">
            <?php foreach ($errors as $field => $error): ?>
                <li><?php echo htmlspecialchars($error); ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-edit me-2"></i><?php echo __('profile_information'); ?>
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?php echo getBaseUrl(); ?>/profile/edit">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="full_name" class="form-label"><?php echo __('full_name'); ?> <span class="text-danger">*</span></label>
                                <input type="text" 
                                       class="form-control <?php echo isset($errors['full_name']) ? 'is-invalid' : ''; ?>" 
                                       id="full_name" 
                                       name="full_name" 
                                       value="<?php echo htmlspecialchars($userData['full_name'] ?? ''); ?>" 
                                       required>
                                <?php if (isset($errors['full_name'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['full_name']; ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label"><?php echo __('email'); ?> <span class="text-danger">*</span></label>
                                <input type="email" 
                                       class="form-control <?php echo isset($errors['email']) ? 'is-invalid' : ''; ?>" 
                                       id="email" 
                                       name="email" 
                                       value="<?php echo htmlspecialchars($userData['email'] ?? ''); ?>" 
                                       required>
                                <?php if (isset($errors['email'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['email']; ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label"><?php echo __('phone'); ?></label>
                                <input type="tel" 
                                       class="form-control <?php echo isset($errors['phone']) ? 'is-invalid' : ''; ?>" 
                                       id="phone" 
                                       name="phone" 
                                       value="<?php echo htmlspecialchars($userData['phone'] ?? ''); ?>">
                                <?php if (isset($errors['phone'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['phone']; ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="language" class="form-label"><?php echo __('language'); ?></label>
                                <select class="form-select <?php echo isset($errors['language']) ? 'is-invalid' : ''; ?>" 
                                        id="language" 
                                        name="language">
                                    <option value="en" <?php echo ($userData['language'] ?? 'en') === 'en' ? 'selected' : ''; ?>>English</option>
                                    <option value="ar" <?php echo ($userData['language'] ?? 'en') === 'ar' ? 'selected' : ''; ?>>العربية</option>
                                </select>
                                <?php if (isset($errors['language'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['language']; ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="bio" class="form-label"><?php echo __('bio'); ?></label>
                        <textarea class="form-control <?php echo isset($errors['bio']) ? 'is-invalid' : ''; ?>" 
                                  id="bio" 
                                  name="bio" 
                                  rows="3" 
                                  placeholder="<?php echo __('tell_us_about_yourself'); ?>"><?php echo htmlspecialchars($userData['bio'] ?? ''); ?></textarea>
                        <?php if (isset($errors['bio'])): ?>
                            <div class="invalid-feedback"><?php echo $errors['bio']; ?></div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="<?php echo getBaseUrl(); ?>/profile" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>
                        </a>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i><?php echo __('save_changes'); ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- Profile Preview -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-eye me-2"></i><?php echo __('profile_preview'); ?>
                </h6>
            </div>
            <div class="card-body text-center">
                <div class="mb-3">
                    <?php if (!empty($user['avatar'])): ?>
                        <img src="<?php echo getBaseUrl(); ?>/<?php echo $user['avatar']; ?>" alt="Avatar" class="rounded-circle" width="80" height="80">
                    <?php else: ?>
                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto" style="width: 80px; height: 80px; font-size: 2rem;">
                            <?php echo strtoupper(substr($userData['full_name'] ?? 'U', 0, 1)); ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <h6 id="preview-name"><?php echo htmlspecialchars($userData['full_name'] ?? ''); ?></h6>
                <p class="text-muted" id="preview-email"><?php echo htmlspecialchars($userData['email'] ?? ''); ?></p>
                
                <span class="badge bg-<?php echo getRoleColor($user['role']); ?>">
                    <?php echo __($user['role']); ?>
                </span>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i><?php echo __('quick_actions'); ?>
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?php echo getBaseUrl(); ?>/profile/change_password" class="btn btn-outline-warning btn-sm">
                        <i class="fas fa-key me-2"></i><?php echo __('change_password'); ?>
                    </a>
                    
                    <a href="<?php echo getBaseUrl(); ?>/profile/avatar" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-camera me-2"></i><?php echo __('change_avatar'); ?>
                    </a>
                    
                    <a href="<?php echo getBaseUrl(); ?>/profile/preferences" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-cog me-2"></i><?php echo __('preferences'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Live preview updates
document.addEventListener('DOMContentLoaded', function() {
    const fullNameInput = document.getElementById('full_name');
    const emailInput = document.getElementById('email');
    const previewName = document.getElementById('preview-name');
    const previewEmail = document.getElementById('preview-email');
    
    fullNameInput.addEventListener('input', function() {
        previewName.textContent = this.value || '<?php echo __('full_name'); ?>';
    });
    
    emailInput.addEventListener('input', function() {
        previewEmail.textContent = this.value || '<?php echo __('email'); ?>';
    });
});
</script>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
