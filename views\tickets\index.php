<?php
/**
 * Tickets List View
 * 
 * This file displays the list of support tickets.
 */

// Set page title
$pageTitle = __('tickets');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 text-gradient mb-1"><?php echo __('tickets_management'); ?></h1>
        <p class="text-muted mb-0"><?php echo __('manage_support_tickets'); ?></p>
    </div>

    <div class="d-flex gap-2">
        <a href="<?php echo getBaseUrl(); ?>/tickets/create" class="btn btn-primary">
            <?php echo __('create_ticket'); ?>
        </a>

        <div class="dropdown">
            <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <?php echo __('export'); ?>
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="<?php echo getBaseUrl(); ?>/tickets/export?format=pdf">
                    PDF Report
                </a></li>
                <li><a class="dropdown-item" href="<?php echo getBaseUrl(); ?>/tickets/export?format=excel">
                    Excel Spreadsheet
                </a></li>
                <li><a class="dropdown-item" href="<?php echo getBaseUrl(); ?>/tickets/export?format=csv">
                    CSV Data
                </a></li>
            </ul>
        </div>
    </div>
</div>

<!-- Enhanced Filters -->
<div class="card card-glass mb-4 fade-in-up">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <?php echo __('filters'); ?>
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" action="<?php echo getBaseUrl(); ?>/tickets" class="row g-3">
            <div class="col-md-3">
                <label for="hospital_id" class="form-label"><?php echo __('hospital'); ?></label>
                <select class="form-select" id="hospital_id" name="hospital_id">
                    <option value=""><?php echo __('all_hospitals'); ?></option>
                    <?php foreach ($hospitals as $hospital): ?>
                        <option value="<?php echo $hospital['id']; ?>" <?php echo ($hospitalId == $hospital['id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($hospital['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="status" class="form-label"><?php echo __('status'); ?></label>
                <select class="form-select" id="status" name="status">
                    <option value=""><?php echo __('all_statuses'); ?></option>
                    <option value="open" <?php echo ($status == 'open') ? 'selected' : ''; ?>><?php echo __('open'); ?></option>
                    <option value="in_progress" <?php echo ($status == 'in_progress') ? 'selected' : ''; ?>><?php echo __('in_progress'); ?></option>
                    <option value="resolved" <?php echo ($status == 'resolved') ? 'selected' : ''; ?>><?php echo __('resolved'); ?></option>
                    <option value="closed" <?php echo ($status == 'closed') ? 'selected' : ''; ?>><?php echo __('closed'); ?></option>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="priority" class="form-label"><?php echo __('priority'); ?></label>
                <select class="form-select" id="priority" name="priority">
                    <option value=""><?php echo __('all_priorities'); ?></option>
                    <option value="low" <?php echo ($priority == 'low') ? 'selected' : ''; ?>><?php echo __('low'); ?></option>
                    <option value="medium" <?php echo ($priority == 'medium') ? 'selected' : ''; ?>><?php echo __('medium'); ?></option>
                    <option value="high" <?php echo ($priority == 'high') ? 'selected' : ''; ?>><?php echo __('high'); ?></option>
                    <option value="urgent" <?php echo ($priority == 'urgent') ? 'selected' : ''; ?>><?php echo __('urgent'); ?></option>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="search" class="form-label"><?php echo __('search'); ?></label>
                <div class="input-group">
                    <input type="text" class="form-control" id="search" name="search" value="<?php echo htmlspecialchars($search ?? ''); ?>" placeholder="<?php echo __('search_tickets'); ?>">
                    <button class="btn btn-primary" type="submit">
                        Search
                    </button>
                </div>
            </div>

            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearFilters()">
                        <?php echo __('clear_filters'); ?>
                    </button>
                    <small class="text-muted">
                        <?php echo count($tickets); ?> <?php echo __('tickets_found'); ?>
                    </small>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Enhanced Tickets Table -->
<div class="card fade-in-up" style="animation-delay: 0.2s;">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <?php echo __('tickets_list'); ?>
            </h5>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="tickets-table">
                <thead>
                    <tr>
                        <th><?php echo __('ticket_id'); ?></th>
                        <th><?php echo __('title'); ?></th>
                        <th><?php echo __('device'); ?></th>
                        <th><?php echo __('priority'); ?></th>
                        <th><?php echo __('status'); ?></th>
                        <th><?php echo __('created_by'); ?></th>
                        <th><?php echo __('created_at'); ?></th>
                        <th><?php echo __('actions'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($tickets)): ?>
                        <tr>
                            <td colspan="8" class="text-center"><?php echo __('no_tickets'); ?></td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($tickets as $ticket): ?>
                            <tr>
                                <td>
                                    <strong>#<?php echo $ticket['id']; ?></strong>
                                </td>
                                <td>
                                    <a href="<?php echo getBaseUrl(); ?>/tickets/view/<?php echo $ticket['id']; ?>">
                                        <?php echo htmlspecialchars($ticket['title']); ?>
                                    </a>
                                </td>
                                <td>
                                    <?php if ($ticket['device_id']): ?>
                                        <a href="<?php echo getBaseUrl(); ?>/devices/view/<?php echo $ticket['device_id']; ?>">
                                            <?php echo htmlspecialchars($ticket['device_name']); ?>
                                        </a>
                                        <br>
                                        <small class="text-muted"><?php echo htmlspecialchars($ticket['serial_number']); ?></small>
                                    <?php else: ?>
                                        <span class="text-muted"><?php echo __('no_device'); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo getPriorityColor($ticket['priority']); ?>">
                                        <?php echo __($ticket['priority']); ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo getTicketStatusColor($ticket['status']); ?>">
                                        <?php echo __($ticket['status']); ?>
                                    </span>
                                </td>
                                <td><?php echo htmlspecialchars($ticket['created_by_name']); ?></td>
                                <td>
                                    <?php 
                                    $createdAt = new DateTime($ticket['created_at']);
                                    echo $createdAt->format('Y-m-d H:i');
                                    ?>
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a href="<?php echo getBaseUrl(); ?>/tickets/view/<?php echo $ticket['id']; ?>" class="btn btn-sm btn-primary">
                                            View
                                        </a>

                                        <?php if (hasPermission('manage_tickets') || $ticket['created_by'] == $_SESSION['user']['id']): ?>
                                        <a href="<?php echo getBaseUrl(); ?>/tickets/edit/<?php echo $ticket['id']; ?>" class="btn btn-sm btn-warning">
                                            Edit
                                        </a>
                                        <?php endif; ?>

                                        <?php if (hasPermission('manage_tickets')): ?>
                                        <button type="button" class="btn btn-sm btn-danger" onclick="confirmDelete(<?php echo $ticket['id']; ?>, '<?php echo htmlspecialchars($ticket['title']); ?>')">
                                            Delete
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo __('confirm_delete'); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p><?php echo __('delete_ticket_confirm'); ?></p>
                <p><strong id="ticketTitle"></strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo __('cancel'); ?></button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <button type="submit" class="btn btn-danger"><?php echo __('delete'); ?></button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(ticketId, ticketTitle) {
    document.getElementById('ticketTitle').textContent = ticketTitle;
    document.getElementById('deleteForm').action = '<?php echo getBaseUrl(); ?>/tickets/delete/' + ticketId;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

function clearFilters() {
    window.location.href = '<?php echo getBaseUrl(); ?>/tickets';
}

// Auto-submit form when filters change
document.getElementById('hospital_id').addEventListener('change', function() {
    this.form.submit();
});

document.getElementById('status').addEventListener('change', function() {
    this.form.submit();
});

document.getElementById('priority').addEventListener('change', function() {
    this.form.submit();
});
</script>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
