<?php
/**
 * Edit Maintenance Schedule View
 * 
 * This file displays the form to edit a maintenance schedule.
 */

// Set page title
$pageTitle = __('edit_schedule');
$pageSubtitle = __('update_maintenance_schedule');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 text-gradient mb-1"><?php echo __('edit_schedule'); ?></h1>
        <p class="text-muted mb-0"><?php echo htmlspecialchars($schedule['title']); ?></p>
    </div>
    
    <div class="btn-group">
        <a href="<?php echo getBaseUrl(); ?>/maintenance" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_list'); ?>
        </a>
        <a href="<?php echo getBaseUrl(); ?>/maintenance/view_schedule/<?php echo $schedule['id']; ?>" class="btn btn-primary">
            <i class="fas fa-eye me-2"></i><?php echo __('view'); ?>
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8 col-md-10 mx-auto">
        <div class="card fade-in-up">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-alt me-2"></i><?php echo __('schedule_information'); ?>
                </h5>
            </div>
            <div class="card-body">
                <form action="<?php echo getBaseUrl(); ?>/maintenance/update_schedule" method="post" id="scheduleForm">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="id" value="<?php echo $schedule['id']; ?>">
                    
                    <?php if (!empty($errors['general'])): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo $errors['general']; ?>
                        </div>
                    <?php endif; ?>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="title" class="form-label"><?php echo __('title'); ?> <span class="text-danger">*</span></label>
                                <input type="text" class="form-control <?php echo isset($errors['title']) ? 'is-invalid' : ''; ?>" 
                                       id="title" name="title" value="<?php echo htmlspecialchars($scheduleData['title'] ?? $schedule['title']); ?>" required>
                                <?php if (isset($errors['title'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['title']; ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="frequency" class="form-label"><?php echo __('frequency'); ?> <span class="text-danger">*</span></label>
                                <select class="form-select <?php echo isset($errors['frequency']) ? 'is-invalid' : ''; ?>" id="frequency" name="frequency" required>
                                    <option value=""><?php echo __('select_frequency'); ?></option>
                                    <option value="once" <?php echo ($scheduleData['frequency'] ?? $schedule['frequency']) === 'once' ? 'selected' : ''; ?>><?php echo __('once'); ?></option>
                                    <option value="daily" <?php echo ($scheduleData['frequency'] ?? $schedule['frequency']) === 'daily' ? 'selected' : ''; ?>><?php echo __('daily'); ?></option>
                                    <option value="weekly" <?php echo ($scheduleData['frequency'] ?? $schedule['frequency']) === 'weekly' ? 'selected' : ''; ?>><?php echo __('weekly'); ?></option>
                                    <option value="monthly" <?php echo ($scheduleData['frequency'] ?? $schedule['frequency']) === 'monthly' ? 'selected' : ''; ?>><?php echo __('monthly'); ?></option>
                                    <option value="quarterly" <?php echo ($scheduleData['frequency'] ?? $schedule['frequency']) === 'quarterly' ? 'selected' : ''; ?>><?php echo __('quarterly'); ?></option>
                                    <option value="yearly" <?php echo ($scheduleData['frequency'] ?? $schedule['frequency']) === 'yearly' ? 'selected' : ''; ?>><?php echo __('yearly'); ?></option>
                                </select>
                                <?php if (isset($errors['frequency'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['frequency']; ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="hospital_id" class="form-label"><?php echo __('hospital'); ?> <span class="text-danger">*</span></label>
                                <select class="form-select <?php echo isset($errors['hospital_id']) ? 'is-invalid' : ''; ?>" id="hospital_id" name="hospital_id" required>
                                    <option value=""><?php echo __('select_hospital'); ?></option>
                                    <?php foreach ($hospitals as $hospital): ?>
                                        <option value="<?php echo $hospital['id']; ?>" 
                                                <?php echo ($scheduleData['hospital_id'] ?? $schedule['hospital_id']) == $hospital['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($hospital['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (isset($errors['hospital_id'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['hospital_id']; ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="device_id" class="form-label"><?php echo __('device'); ?> <span class="text-danger">*</span></label>
                                <select class="form-select <?php echo isset($errors['device_id']) ? 'is-invalid' : ''; ?>" id="device_id" name="device_id" required>
                                    <option value=""><?php echo __('select_device'); ?></option>
                                    <?php foreach ($devices as $device): ?>
                                        <option value="<?php echo $device['id']; ?>" 
                                                <?php echo ($scheduleData['device_id'] ?? $schedule['device_id']) == $device['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($device['name'] . ' (' . $device['serial_number'] . ')'); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (isset($errors['device_id'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['device_id']; ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="scheduled_date" class="form-label"><?php echo __('scheduled_date'); ?> <span class="text-danger">*</span></label>
                                <input type="datetime-local" class="form-control <?php echo isset($errors['scheduled_date']) ? 'is-invalid' : ''; ?>" 
                                       id="scheduled_date" name="scheduled_date" 
                                       value="<?php echo date('Y-m-d\TH:i', strtotime($scheduleData['scheduled_date'] ?? $schedule['scheduled_date'])); ?>" required>
                                <?php if (isset($errors['scheduled_date'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['scheduled_date']; ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="assigned_to" class="form-label"><?php echo __('assigned_to'); ?></label>
                                <select class="form-select" id="assigned_to" name="assigned_to">
                                    <option value=""><?php echo __('unassigned'); ?></option>
                                    <?php foreach ($users as $user): ?>
                                        <option value="<?php echo $user['id']; ?>" 
                                                <?php echo ($scheduleData['assigned_to'] ?? $schedule['assigned_to']) == $user['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($user['full_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label"><?php echo __('description'); ?></label>
                        <textarea class="form-control" id="description" name="description" rows="4"><?php echo htmlspecialchars($scheduleData['description'] ?? $schedule['description']); ?></textarea>
                        <div class="form-text"><?php echo __('maintenance_description_help'); ?></div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label"><?php echo __('notes'); ?></label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"><?php echo htmlspecialchars($scheduleData['notes'] ?? $schedule['notes']); ?></textarea>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center pt-3 border-top">
                        <a href="<?php echo getBaseUrl(); ?>/maintenance" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>
                        </a>
                        <div class="d-flex gap-2">
                            <button type="reset" class="btn btn-outline-secondary">
                                <i class="fas fa-undo me-2"></i><?php echo __('reset'); ?>
                            </button>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <span class="btn-text">
                                    <i class="fas fa-save me-2"></i><?php echo __('update_schedule'); ?>
                                </span>
                                <span class="loading-spinner d-none"></span>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Load devices when hospital changes
document.getElementById('hospital_id').addEventListener('change', function() {
    const hospitalId = this.value;
    const deviceSelect = document.getElementById('device_id');
    
    // Clear current options
    deviceSelect.innerHTML = '<option value=""><?php echo __('select_device'); ?></option>';
    
    if (hospitalId) {
        // Fetch devices for the selected hospital
        fetch(`<?php echo getBaseUrl(); ?>/api/devices?hospital_id=${hospitalId}`)
            .then(response => response.json())
            .then(data => {
                data.forEach(device => {
                    const option = document.createElement('option');
                    option.value = device.id;
                    option.textContent = `${device.name} (${device.serial_number})`;
                    deviceSelect.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Error loading devices:', error);
            });
    }
});

// Form submission
document.getElementById('scheduleForm').addEventListener('submit', function(e) {
    const submitBtn = document.getElementById('submitBtn');
    const btnText = submitBtn.querySelector('.btn-text');
    const spinner = submitBtn.querySelector('.loading-spinner');
    
    // Show loading state
    btnText.classList.add('d-none');
    spinner.classList.remove('d-none');
    submitBtn.disabled = true;
});

// Set minimum date to today
document.getElementById('scheduled_date').min = new Date().toISOString().slice(0, 16);
</script>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
