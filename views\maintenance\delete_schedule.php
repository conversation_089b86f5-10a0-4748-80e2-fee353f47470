<?php
/**
 * Delete Maintenance Schedule View
 * 
 * This file displays the confirmation form to delete a maintenance schedule.
 */

// Set page title
$pageTitle = __('delete_maintenance_schedule');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3"><?php echo __('delete_maintenance_schedule'); ?></h1>
    
    <a href="<?php echo getBaseUrl(); ?>/maintenance" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_maintenance'); ?>
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo __('confirm_deletion'); ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-danger">
                    <h6 class="alert-heading">
                        <i class="fas fa-warning me-2"></i><?php echo __('warning'); ?>
                    </h6>
                    <p class="mb-0"><?php echo __('delete_schedule_warning'); ?></p>
                </div>
                
                <!-- Schedule Details -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i><?php echo __('schedule_details'); ?>
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted"><?php echo __('title'); ?></label>
                                    <div class="fw-bold"><?php echo htmlspecialchars($schedule['title']); ?></div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label text-muted"><?php echo __('device'); ?></label>
                                    <div class="fw-bold"><?php echo htmlspecialchars($schedule['device_name']); ?></div>
                                    <small class="text-muted"><?php echo htmlspecialchars($schedule['serial_number']); ?></small>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label text-muted"><?php echo __('scheduled_date'); ?></label>
                                    <div class="fw-bold"><?php echo date('F d, Y', strtotime($schedule['scheduled_date'])); ?></div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted"><?php echo __('frequency'); ?></label>
                                    <div class="fw-bold"><?php echo __($schedule['frequency']); ?></div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label text-muted"><?php echo __('status'); ?></label>
                                    <div>
                                        <span class="badge bg-<?php echo getMaintenanceStatusColor($schedule['status']); ?>">
                                            <?php echo __($schedule['status']); ?>
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label text-muted"><?php echo __('created_by'); ?></label>
                                    <div class="fw-bold"><?php echo htmlspecialchars($schedule['created_by_name']); ?></div>
                                    <small class="text-muted"><?php echo date('M d, Y', strtotime($schedule['created_at'])); ?></small>
                                </div>
                            </div>
                        </div>
                        
                        <?php if ($schedule['description']): ?>
                            <div class="mb-3">
                                <label class="form-label text-muted"><?php echo __('description'); ?></label>
                                <div class="fw-bold"><?php echo nl2br(htmlspecialchars($schedule['description'])); ?></div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Related Data Warning -->
                <?php
                // Check if there are related maintenance logs
                $relatedLogs = $maintenanceModel->getAllLogs($schedule['id']);
                if (!empty($relatedLogs)):
                ?>
                    <div class="alert alert-warning">
                        <h6 class="alert-heading">
                            <i class="fas fa-exclamation-triangle me-2"></i><?php echo __('related_data'); ?>
                        </h6>
                        <p class="mb-2"><?php echo __('schedule_has_related_logs', [count($relatedLogs)]); ?></p>
                        <ul class="mb-0">
                            <?php foreach (array_slice($relatedLogs, 0, 3) as $log): ?>
                                <li><?php echo htmlspecialchars($log['notes'] ?: __('maintenance_log')); ?> - <?php echo date('M d, Y', strtotime($log['performed_date'])); ?></li>
                            <?php endforeach; ?>
                            <?php if (count($relatedLogs) > 3): ?>
                                <li><em><?php echo __('and_more', [count($relatedLogs) - 3]); ?></em></li>
                            <?php endif; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <!-- Confirmation Form -->
                <form method="POST" action="<?php echo getBaseUrl(); ?>/maintenance/delete_schedule/<?php echo $schedule['id']; ?>">
                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="confirm_deletion" name="confirm_deletion" required>
                            <label class="form-check-label" for="confirm_deletion">
                                <?php echo __('confirm_delete_schedule'); ?>
                            </label>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="<?php echo getBaseUrl(); ?>/maintenance" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>
                        </a>
                        
                        <button type="submit" name="confirm" value="yes" class="btn btn-danger" id="delete_button" disabled>
                            <i class="fas fa-trash me-2"></i><?php echo __('delete_schedule'); ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Additional Information -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i><?php echo __('what_happens_when_deleted'); ?>
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-danger me-2"></i>
                        <?php echo __('schedule_will_be_permanently_deleted'); ?>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-warning me-2"></i>
                        <?php echo __('related_logs_will_remain'); ?>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-info me-2"></i>
                        <?php echo __('device_status_unchanged'); ?>
                    </li>
                    <li>
                        <i class="fas fa-check text-secondary me-2"></i>
                        <?php echo __('action_cannot_be_undone'); ?>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const confirmCheckbox = document.getElementById('confirm_deletion');
    const deleteButton = document.getElementById('delete_button');
    
    confirmCheckbox.addEventListener('change', function() {
        deleteButton.disabled = !this.checked;
    });
    
    // Add confirmation dialog
    deleteButton.addEventListener('click', function(e) {
        if (!confirm('<?php echo __('are_you_sure_delete_schedule'); ?>')) {
            e.preventDefault();
        }
    });
});
</script>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
