<?php
/**
 * Tickets Controller
 * 
 * This file handles ticket-related operations.
 */

// Check if the user is logged in
requireLogin();

// Get the current user
$currentUser = getCurrentUser();
$userHospitalId = $currentUser['hospital_id'];

// Handle actions
$action = isset($url[1]) ? $url[1] : 'index';
$ticketId = isset($url[2]) ? (int)$url[2] : 0;

switch ($action) {
    case 'index':
        // Check if the user has permission to view tickets
        if (!hasPermission('view_tickets') && !hasPermission('manage_tickets') && !hasPermission('create_tickets')) {
            setFlashMessage('error', __('access_denied'));
            redirect('dashboard');
        }
        
        // Get filter parameters
        $hospitalId = isset($_GET['hospital_id']) ? (int)$_GET['hospital_id'] : $userHospitalId;
        $deviceId = isset($_GET['device_id']) ? (int)$_GET['device_id'] : null;
        $status = isset($_GET['status']) ? $_GET['status'] : null;
        $priority = isset($_GET['priority']) ? $_GET['priority'] : null;
        
        // Get tickets based on user role and filters
        if ($currentUser['role'] === 'staff') {
            // Staff can only see their own tickets
            $tickets = $ticketModel->getAll(null, $hospitalId, $currentUser['id']);
        } elseif ($currentUser['role'] === 'technician' || $currentUser['role'] === 'engineer') {
            // Technicians and engineers can see tickets assigned to them and open tickets
            $assignedTickets = $ticketModel->getAll(null, $hospitalId, null, $currentUser['id']);
            $openTickets = $ticketModel->getByStatus('open', $hospitalId);
            $tickets = array_merge($assignedTickets, $openTickets);
            
            // Remove duplicates
            $uniqueTickets = [];
            foreach ($tickets as $ticket) {
                $uniqueTickets[$ticket['id']] = $ticket;
            }
            $tickets = array_values($uniqueTickets);
        } else {
            // Admin can see all tickets
            $tickets = $ticketModel->getAll($deviceId, $hospitalId);
        }
        
        // Filter by status if specified
        if ($status) {
            $tickets = array_filter($tickets, function($ticket) use ($status) {
                return $ticket['status'] === $status;
            });
        }
        
        // Filter by priority if specified
        if ($priority) {
            $tickets = array_filter($tickets, function($ticket) use ($priority) {
                return $ticket['priority'] === $priority;
            });
        }
        
        // Get hospitals and devices for the filters
        if (hasPermission('manage_hospitals')) {
            $hospitals = $hospitalModel->getAll();
        } elseif ($userHospitalId) {
            $hospitals = [$hospitalModel->getById($userHospitalId)];
        } else {
            $hospitals = [];
        }
        
        if ($hospitalId) {
            $devices = $deviceModel->getAll($hospitalId);
        } else {
            $devices = [];
        }
        
        // Include the tickets index view
        include 'views/tickets/index.php';
        break;
        
    case 'create':
        // Check if the user has permission to create tickets
        if (!hasPermission('create_tickets') && !hasPermission('manage_tickets')) {
            setFlashMessage('error', __('access_denied'));
            redirect('dashboard');
        }
        
        // Get device ID from query string if provided
        $deviceId = isset($_GET['device_id']) ? (int)$_GET['device_id'] : null;
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $ticketData = [
                'device_id' => $_POST['device_id'] ?? null,
                'reported_by' => $currentUser['id'],
                'assigned_to' => $_POST['assigned_to'] ?? null,
                'title' => $_POST['title'] ?? '',
                'description' => $_POST['description'] ?? '',
                'priority' => $_POST['priority'] ?? 'medium',
                'status' => 'open'
            ];
            
            // Validate data
            $errors = [];
            
            if (empty($ticketData['device_id'])) {
                $errors['device_id'] = __('required_field');
            } else {
                // Check if the device exists
                $device = $deviceModel->getById($ticketData['device_id']);
                if (!$device) {
                    $errors['device_id'] = __('invalid_device');
                } elseif ($userHospitalId && $device['hospital_id'] != $userHospitalId && !hasPermission('manage_hospitals')) {
                    $errors['device_id'] = __('access_denied');
                }
            }
            
            if (empty($ticketData['title'])) {
                $errors['title'] = __('required_field');
            }
            
            if (empty($ticketData['description'])) {
                $errors['description'] = __('required_field');
            }
            
            // If no errors, create the ticket
            if (empty($errors)) {
                $ticketId = $ticketModel->create($ticketData);
                
                if ($ticketId) {
                    // Log the action
                    logAction('create_ticket', 'Created ticket: ' . $ticketData['title']);
                    
                    // Set flash message
                    setFlashMessage('success', __('created_successfully', [__('ticket')]));
                    
                    // Redirect to tickets index
                    redirect('tickets');
                } else {
                    $errors['general'] = __('create_failed');
                }
            }
        } else {
            // Initialize empty ticket data
            $ticketData = [
                'device_id' => $deviceId,
                'assigned_to' => null,
                'title' => '',
                'description' => '',
                'priority' => 'medium'
            ];
            
            $errors = [];
        }
        
        // Get hospitals for the dropdown
        if (hasPermission('manage_hospitals')) {
            $hospitals = $hospitalModel->getAll();
        } elseif ($userHospitalId) {
            $hospitals = [$hospitalModel->getById($userHospitalId)];
        } else {
            $hospitals = [];
        }
        
        // Get devices for the dropdown
        if ($userHospitalId) {
            $devices = $deviceModel->getAll($userHospitalId);
        } else {
            $devices = $deviceModel->getAll();
        }
        
        // Get engineers and technicians for the dropdown
        $engineers = $userModel->getByRole('engineer', $userHospitalId);
        $technicians = $userModel->getByRole('technician', $userHospitalId);
        $assignees = array_merge($engineers, $technicians);
        
        // Include the tickets create view
        include 'views/tickets/create.php';
        break;
        
    case 'edit':
        // Check if the user has permission to manage tickets
        requirePermission('manage_tickets');
        
        // Get the ticket
        $ticket = $ticketModel->getById($ticketId);
        
        // Check if the ticket exists
        if (!$ticket) {
            setFlashMessage('error', __('not_found'));
            redirect('tickets');
        }
        
        // Get the device
        $device = $deviceModel->getById($ticket['device_id']);
        
        // Check if the user has permission to edit this ticket
        if ($userHospitalId && $device['hospital_id'] != $userHospitalId && !hasPermission('manage_hospitals')) {
            setFlashMessage('error', __('access_denied'));
            redirect('tickets');
        }
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $ticketData = [
                'device_id' => $_POST['device_id'] ?? $ticket['device_id'],
                'assigned_to' => $_POST['assigned_to'] ?? $ticket['assigned_to'],
                'title' => $_POST['title'] ?? $ticket['title'],
                'description' => $_POST['description'] ?? $ticket['description'],
                'priority' => $_POST['priority'] ?? $ticket['priority'],
                'status' => $_POST['status'] ?? $ticket['status'],
                'comment' => $_POST['comment'] ?? ''
            ];
            
            // Validate data
            $errors = [];
            
            if (empty($ticketData['device_id'])) {
                $errors['device_id'] = __('required_field');
            } else {
                // Check if the device exists
                $device = $deviceModel->getById($ticketData['device_id']);
                if (!$device) {
                    $errors['device_id'] = __('invalid_device');
                } elseif ($userHospitalId && $device['hospital_id'] != $userHospitalId && !hasPermission('manage_hospitals')) {
                    $errors['device_id'] = __('access_denied');
                }
            }
            
            if (empty($ticketData['title'])) {
                $errors['title'] = __('required_field');
            }
            
            if (empty($ticketData['description'])) {
                $errors['description'] = __('required_field');
            }
            
            // If no errors, update the ticket
            if (empty($errors)) {
                $result = $ticketModel->update($ticketId, $ticketData, $currentUser['id']);
                
                if ($result) {
                    // Log the action
                    logAction('update_ticket', 'Updated ticket: ' . $ticketData['title']);
                    
                    // Set flash message
                    setFlashMessage('success', __('updated_successfully', [__('ticket')]));
                    
                    // Redirect to tickets index
                    redirect('tickets');
                } else {
                    $errors['general'] = __('update_failed');
                }
            }
        } else {
            $ticketData = $ticket;
            $ticketData['comment'] = '';
            $errors = [];
        }
        
        // Get hospitals for the dropdown
        if (hasPermission('manage_hospitals')) {
            $hospitals = $hospitalModel->getAll();
        } elseif ($userHospitalId) {
            $hospitals = [$hospitalModel->getById($userHospitalId)];
        } else {
            $hospitals = [];
        }
        
        // Get devices for the dropdown
        if ($userHospitalId) {
            $devices = $deviceModel->getAll($userHospitalId);
        } else {
            $devices = $deviceModel->getAll();
        }
        
        // Get engineers and technicians for the dropdown
        $engineers = $userModel->getByRole('engineer', $userHospitalId);
        $technicians = $userModel->getByRole('technician', $userHospitalId);
        $assignees = array_merge($engineers, $technicians);
        
        // Get ticket updates
        $updates = $ticketModel->getUpdates($ticketId);
        
        // Include the tickets edit view
        include 'views/tickets/edit.php';
        break;
        
    case 'view':
        // Check if the user has permission to view tickets
        if (!hasPermission('view_tickets') && !hasPermission('manage_tickets') && !hasPermission('create_tickets')) {
            setFlashMessage('error', __('access_denied'));
            redirect('dashboard');
        }
        
        // Get the ticket
        $ticket = $ticketModel->getById($ticketId);
        
        // Check if the ticket exists
        if (!$ticket) {
            setFlashMessage('error', __('not_found'));
            redirect('tickets');
        }
        
        // Get the device
        $device = $deviceModel->getById($ticket['device_id']);
        
        // Check if the user has permission to view this ticket
        if ($currentUser['role'] === 'staff' && $ticket['reported_by'] != $currentUser['id']) {
            setFlashMessage('error', __('access_denied'));
            redirect('tickets');
        }
        
        if ($userHospitalId && $device['hospital_id'] != $userHospitalId && !hasPermission('manage_hospitals')) {
            setFlashMessage('error', __('access_denied'));
            redirect('tickets');
        }
        
        // Handle comment submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['comment'])) {
            $comment = $_POST['comment'];
            
            if (!empty($comment)) {
                $result = $ticketModel->createUpdate($ticketId, $currentUser['id'], $comment);
                
                if ($result) {
                    // Create notification
                    createTicketNotification($ticketId, 'updated', $currentUser['id']);
                    
                    // Set flash message
                    setFlashMessage('success', __('comment_added'));
                    
                    // Redirect to refresh the page
                    redirect('tickets/view/' . $ticketId);
                } else {
                    setFlashMessage('error', __('comment_failed'));
                }
            } else {
                setFlashMessage('error', __('comment_empty'));
            }
        }
        
        // Get ticket updates
        $updates = $ticketModel->getUpdates($ticketId);
        
        // Include the tickets view
        include 'views/tickets/view.php';
        break;
        
    case 'assign':
        // Check if the user has permission to manage tickets
        requirePermission('manage_tickets');
        
        // Get the ticket
        $ticket = $ticketModel->getById($ticketId);
        
        // Check if the ticket exists
        if (!$ticket) {
            setFlashMessage('error', __('not_found'));
            redirect('tickets');
        }
        
        // Get the device
        $device = $deviceModel->getById($ticket['device_id']);
        
        // Check if the user has permission to assign this ticket
        if ($userHospitalId && $device['hospital_id'] != $userHospitalId && !hasPermission('manage_hospitals')) {
            setFlashMessage('error', __('access_denied'));
            redirect('tickets');
        }
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $assignedTo = $_POST['assigned_to'] ?? null;
            
            if ($assignedTo) {
                $result = $ticketModel->assign($ticketId, $assignedTo, $currentUser['id']);
                
                if ($result) {
                    // Log the action
                    logAction('assign_ticket', 'Assigned ticket ID ' . $ticketId . ' to user ID ' . $assignedTo);
                    
                    // Set flash message
                    setFlashMessage('success', __('ticket_assigned'));
                    
                    // Redirect to tickets index
                    redirect('tickets');
                } else {
                    setFlashMessage('error', __('assign_failed'));
                }
            } else {
                setFlashMessage('error', __('assignee_required'));
            }
        }
        
        // Get engineers and technicians for the dropdown
        $engineers = $userModel->getByRole('engineer', $userHospitalId);
        $technicians = $userModel->getByRole('technician', $userHospitalId);
        $assignees = array_merge($engineers, $technicians);
        
        // Include the tickets assign view
        include 'views/tickets/assign.php';
        break;
        
    case 'status':
        // Check if the user has permission to manage tickets
        requirePermission('manage_tickets');
        
        // Get the ticket
        $ticket = $ticketModel->getById($ticketId);
        
        // Check if the ticket exists
        if (!$ticket) {
            setFlashMessage('error', __('not_found'));
            redirect('tickets');
        }
        
        // Get the device
        $device = $deviceModel->getById($ticket['device_id']);
        
        // Check if the user has permission to change the status of this ticket
        if ($userHospitalId && $device['hospital_id'] != $userHospitalId && !hasPermission('manage_hospitals')) {
            setFlashMessage('error', __('access_denied'));
            redirect('tickets');
        }
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $status = $_POST['status'] ?? null;
            $comment = $_POST['comment'] ?? '';
            
            if ($status) {
                $result = $ticketModel->changeStatus($ticketId, $status, $currentUser['id'], $comment);
                
                if ($result) {
                    // Log the action
                    logAction('change_ticket_status', 'Changed ticket ID ' . $ticketId . ' status to ' . $status);
                    
                    // Set flash message
                    setFlashMessage('success', __('status_changed'));
                    
                    // Redirect to tickets index
                    redirect('tickets');
                } else {
                    setFlashMessage('error', __('status_change_failed'));
                }
            } else {
                setFlashMessage('error', __('status_required'));
            }
        }
        
        // Include the tickets status view
        include 'views/tickets/status.php';
        break;
        
    case 'delete':
        // Check if the user has permission to manage tickets
        requirePermission('manage_tickets');
        
        // Get the ticket
        $ticket = $ticketModel->getById($ticketId);
        
        // Check if the ticket exists
        if (!$ticket) {
            setFlashMessage('error', __('not_found'));
            redirect('tickets');
        }
        
        // Get the device
        $device = $deviceModel->getById($ticket['device_id']);
        
        // Check if the user has permission to delete this ticket
        if ($userHospitalId && $device['hospital_id'] != $userHospitalId && !hasPermission('manage_hospitals')) {
            setFlashMessage('error', __('access_denied'));
            redirect('tickets');
        }
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm']) && $_POST['confirm'] === 'yes') {
            $result = $ticketModel->delete($ticketId);
            
            if ($result) {
                // Log the action
                logAction('delete_ticket', 'Deleted ticket: ' . $ticket['title']);
                
                // Set flash message
                setFlashMessage('success', __('deleted_successfully', [__('ticket')]));
            } else {
                setFlashMessage('error', __('delete_failed'));
            }
            
            // Redirect to tickets index
            redirect('tickets');
        }
        
        // Include the tickets delete view
        include 'views/tickets/delete.php';
        break;
        
    default:
        // 404 Not Found
        header("HTTP/1.0 404 Not Found");
        include 'views/404.php';
        break;
}
