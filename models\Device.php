<?php
/**
 * Device Model
 *
 * This class handles device-related database operations.
 */
class Device {
    private $pdo;

    /**
     * Constructor
     *
     * @param PDO $pdo The PDO instance
     */
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    /**
     * Get all devices
     *
     * @param int $hospitalId Optional hospital ID to filter devices
     * @param int $departmentId Optional department ID to filter devices
     * @return array The devices
     */
    public function getAll($hospitalId = null, $departmentId = null) {
        try {
            $sql = "
                SELECT d.*, h.name AS hospital_name, dp.name AS department_name
                FROM devices d
                JOIN hospitals h ON d.hospital_id = h.id
                JOIN departments dp ON d.department_id = dp.id
            ";
            $params = [];
            $where = [];

            if ($hospitalId) {
                $where[] = "d.hospital_id = ?";
                $params[] = $hospitalId;
            }

            if ($departmentId) {
                $where[] = "d.department_id = ?";
                $params[] = $departmentId;
            }

            if (!empty($where)) {
                $sql .= " WHERE " . implode(" AND ", $where);
            }

            $sql .= " ORDER BY h.name, dp.name, d.name";

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get All Devices Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get a device by ID
     *
     * @param int $id The device ID
     * @return array|bool The device or false if not found
     */
    public function getById($id) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT d.*, h.name AS hospital_name, dp.name AS department_name
                FROM devices d
                JOIN hospitals h ON d.hospital_id = h.id
                JOIN departments dp ON d.department_id = dp.id
                WHERE d.id = ?
            ");

            $stmt->execute([$id]);
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Get Device By ID Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get a device by serial number
     *
     * @param string $serialNumber The serial number
     * @return array|bool The device or false if not found
     */
    public function getBySerialNumber($serialNumber) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT d.*, h.name AS hospital_name, dp.name AS department_name
                FROM devices d
                JOIN hospitals h ON d.hospital_id = h.id
                JOIN departments dp ON d.department_id = dp.id
                WHERE d.serial_number = ?
            ");

            $stmt->execute([$serialNumber]);
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Get Device By Serial Number Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Create a new device
     *
     * @param array $data The device data
     * @return int|bool The device ID if successful, false otherwise
     */
    public function create($data) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO devices (
                    hospital_id, department_id, name, model, serial_number,
                    manufacturer, category, purchase_date, warranty_expiry, status,
                    location, maintenance_interval, notes
                ) VALUES (
                    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
                )
            ");

            $stmt->execute([
                $data['hospital_id'],
                $data['department_id'],
                $data['name'],
                $data['model'],
                $data['serial_number'],
                $data['manufacturer'],
                $data['category'] ?? 'Other',
                $data['purchase_date'],
                $data['warranty_expiry'],
                $data['status'] ?? 'operational',
                $data['location'] ?? null,
                $data['maintenance_interval'] ?? null,
                $data['notes'] ?? null
            ]);

            $deviceId = $this->pdo->lastInsertId();

            // Generate QR code
            $qrCode = generateQRCode($deviceId, $data['serial_number']);

            if ($qrCode) {
                $stmt = $this->pdo->prepare("UPDATE devices SET qr_code = ? WHERE id = ?");
                $stmt->execute([$qrCode, $deviceId]);
            }

            return $deviceId;
        } catch (PDOException $e) {
            error_log("Create Device Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update a device
     *
     * @param int $id The device ID
     * @param array $data The device data
     * @return bool True if successful, false otherwise
     */
    public function update($id, $data) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE devices SET
                    hospital_id = ?,
                    department_id = ?,
                    name = ?,
                    model = ?,
                    serial_number = ?,
                    manufacturer = ?,
                    category = ?,
                    purchase_date = ?,
                    warranty_expiry = ?,
                    status = ?,
                    location = ?,
                    maintenance_interval = ?,
                    notes = ?,
                    updated_at = NOW()
                WHERE id = ?
            ");

            $result = $stmt->execute([
                $data['hospital_id'],
                $data['department_id'],
                $data['name'],
                $data['model'],
                $data['serial_number'],
                $data['manufacturer'],
                $data['category'] ?? 'Other',
                $data['purchase_date'],
                $data['warranty_expiry'],
                $data['status'],
                $data['location'] ?? null,
                $data['maintenance_interval'] ?? null,
                $data['notes'] ?? null,
                $id
            ]);

            // Regenerate QR code if serial number changed
            if ($result && isset($data['regenerate_qr']) && $data['regenerate_qr']) {
                $qrCode = generateQRCode($id, $data['serial_number']);

                if ($qrCode) {
                    $stmt = $this->pdo->prepare("UPDATE devices SET qr_code = ? WHERE id = ?");
                    $stmt->execute([$qrCode, $id]);
                }
            }

            return $result;
        } catch (PDOException $e) {
            error_log("Update Device Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete a device
     *
     * @param int $id The device ID
     * @return bool True if successful, false otherwise
     */
    public function delete($id) {
        try {
            // Check if there are any maintenance schedules for this device
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM maintenance_schedules WHERE device_id = ?");
            $stmt->execute([$id]);
            $maintenanceCount = $stmt->fetchColumn();

            if ($maintenanceCount > 0) {
                // Cannot delete a device with maintenance schedules
                return false;
            }

            // Check if there are any tickets for this device
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM tickets WHERE device_id = ?");
            $stmt->execute([$id]);
            $ticketCount = $stmt->fetchColumn();

            if ($ticketCount > 0) {
                // Cannot delete a device with tickets
                return false;
            }

            // Get the QR code path
            $stmt = $this->pdo->prepare("SELECT qr_code FROM devices WHERE id = ?");
            $stmt->execute([$id]);
            $device = $stmt->fetch();

            // Delete the device
            $stmt = $this->pdo->prepare("DELETE FROM devices WHERE id = ?");
            $result = $stmt->execute([$id]);

            // Delete the QR code file
            if ($result && $device && $device['qr_code']) {
                $qrPath = __DIR__ . '/../' . $device['qr_code'];
                if (file_exists($qrPath)) {
                    unlink($qrPath);
                }
            }

            return $result;
        } catch (PDOException $e) {
            error_log("Delete Device Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update device status
     *
     * @param int $id The device ID
     * @param string $status The new status
     * @return bool True if successful, false otherwise
     */
    public function updateStatus($id, $status) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE devices SET
                    status = ?,
                    updated_at = NOW()
                WHERE id = ?
            ");

            return $stmt->execute([$status, $id]);
        } catch (PDOException $e) {
            error_log("Update Device Status Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get devices by status
     *
     * @param string $status The status
     * @param int $hospitalId Optional hospital ID to filter devices
     * @param int $departmentId Optional department ID to filter devices
     * @return array The devices
     */
    public function getByStatus($status, $hospitalId = null, $departmentId = null) {
        try {
            $sql = "
                SELECT d.*, h.name AS hospital_name, dp.name AS department_name
                FROM devices d
                JOIN hospitals h ON d.hospital_id = h.id
                JOIN departments dp ON d.department_id = dp.id
                WHERE d.status = ?
            ";
            $params = [$status];

            if ($hospitalId) {
                $sql .= " AND d.hospital_id = ?";
                $params[] = $hospitalId;
            }

            if ($departmentId) {
                $sql .= " AND d.department_id = ?";
                $params[] = $departmentId;
            }

            $sql .= " ORDER BY h.name, dp.name, d.name";

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get Devices By Status Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Search devices
     *
     * @param string $query The search query
     * @param int $hospitalId Optional hospital ID to filter devices
     * @return array The devices
     */
    public function search($query, $hospitalId = null) {
        try {
            $sql = "
                SELECT d.*, h.name AS hospital_name, dp.name AS department_name
                FROM devices d
                JOIN hospitals h ON d.hospital_id = h.id
                JOIN departments dp ON d.department_id = dp.id
                WHERE (
                    d.name LIKE ? OR
                    d.model LIKE ? OR
                    d.serial_number LIKE ? OR
                    d.manufacturer LIKE ?
                )
            ";
            $params = [
                "%{$query}%",
                "%{$query}%",
                "%{$query}%",
                "%{$query}%"
            ];

            if ($hospitalId) {
                $sql .= " AND d.hospital_id = ?";
                $params[] = $hospitalId;
            }

            $sql .= " ORDER BY h.name, dp.name, d.name";

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Search Devices Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Count devices
     *
     * @param int $hospitalId Optional hospital ID to filter devices
     * @param int $departmentId Optional department ID to filter devices
     * @return int The number of devices
     */
    public function count($hospitalId = null, $departmentId = null) {
        try {
            $sql = "SELECT COUNT(*) FROM devices";
            $params = [];
            $where = [];

            if ($hospitalId) {
                $where[] = "hospital_id = ?";
                $params[] = $hospitalId;
            }

            if ($departmentId) {
                $where[] = "department_id = ?";
                $params[] = $departmentId;
            }

            if (!empty($where)) {
                $sql .= " WHERE " . implode(" AND ", $where);
            }

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);

            return (int) $stmt->fetchColumn();
        } catch (PDOException $e) {
            error_log("Count Devices Error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Count devices by status
     *
     * @param string $status The status
     * @param int $hospitalId Optional hospital ID to filter devices
     * @param int $departmentId Optional department ID to filter devices
     * @return int The number of devices
     */
    public function countByStatus($status, $hospitalId = null, $departmentId = null) {
        try {
            $sql = "SELECT COUNT(*) FROM devices WHERE status = ?";
            $params = [$status];

            if ($hospitalId) {
                $sql .= " AND hospital_id = ?";
                $params[] = $hospitalId;
            }

            if ($departmentId) {
                $sql .= " AND department_id = ?";
                $params[] = $departmentId;
            }

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);

            return (int) $stmt->fetchColumn();
        } catch (PDOException $e) {
            error_log("Count Devices By Status Error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Count devices by hospital
     *
     * @param int $hospitalId The hospital ID
     * @return int The number of devices
     */
    public function countByHospital($hospitalId) {
        return $this->count($hospitalId);
    }
}
