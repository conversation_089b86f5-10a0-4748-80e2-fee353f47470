<?php
/**
 * User Profile View
 * 
 * This file displays the user's profile information.
 */

// Set page title
$pageTitle = __('profile');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3"><?php echo __('profile'); ?></h1>
    
    <a href="<?php echo getBaseUrl(); ?>/profile/edit" class="btn btn-primary">
        <i class="fas fa-edit me-2"></i><?php echo __('edit_profile'); ?>
    </a>
</div>

<div class="row">
    <div class="col-md-4">
        <!-- Profile Card -->
        <div class="card">
            <div class="card-body text-center">
                <div class="mb-3">
                    <?php if (!empty($user['avatar'])): ?>
                        <img src="<?php echo getBaseUrl(); ?>/<?php echo $user['avatar']; ?>" alt="Avatar" class="rounded-circle" width="120" height="120">
                    <?php else: ?>
                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto" style="width: 120px; height: 120px; font-size: 3rem;">
                            <?php echo strtoupper(substr($user['full_name'], 0, 1)); ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <h4><?php echo htmlspecialchars($user['full_name']); ?></h4>
                <p class="text-muted"><?php echo htmlspecialchars($user['email']); ?></p>
                
                <span class="badge bg-<?php echo getRoleColor($user['role']); ?> mb-2">
                    <?php echo __($user['role']); ?>
                </span>
                
                <?php if ($user['hospital_name']): ?>
                    <p class="text-muted">
                        <?php echo htmlspecialchars($user['hospital_name']); ?>
                    </p>
                <?php endif; ?>
                
                <div class="mt-3">
                    <small class="text-muted">
                        <?php echo __('member_since'); ?>: <?php echo date('F Y', strtotime($user['created_at'])); ?>
                    </small>
                </div>
            </div>
        </div>
        
        <!-- Quick Stats -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0"><?php echo __('quick_stats'); ?></h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary"><?php echo number_format($stats['tickets_created'] ?? 0); ?></h4>
                        <small class="text-muted"><?php echo __('tickets_created'); ?></small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success"><?php echo number_format($stats['maintenance_completed'] ?? 0); ?></h4>
                        <small class="text-muted"><?php echo __('maintenance_done'); ?></small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <!-- Profile Information -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><?php echo __('profile_information'); ?></h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong><?php echo __('full_name'); ?>:</strong></td>
                                <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                            </tr>
                            <tr>
                                <td><strong><?php echo __('username'); ?>:</strong></td>
                                <td><?php echo htmlspecialchars($user['username']); ?></td>
                            </tr>
                            <tr>
                                <td><strong><?php echo __('email'); ?>:</strong></td>
                                <td><?php echo htmlspecialchars($user['email']); ?></td>
                            </tr>
                            <tr>
                                <td><strong><?php echo __('phone'); ?>:</strong></td>
                                <td><?php echo htmlspecialchars($user['phone'] ?? 'N/A'); ?></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong><?php echo __('role'); ?>:</strong></td>
                                <td>
                                    <span class="badge bg-<?php echo getRoleColor($user['role']); ?>">
                                        <?php echo __($user['role']); ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong><?php echo __('hospital'); ?>:</strong></td>
                                <td><?php echo htmlspecialchars($user['hospital_name'] ?? 'N/A'); ?></td>
                            </tr>
                            <tr>
                                <td><strong><?php echo __('language'); ?>:</strong></td>
                                <td><?php echo $user['language'] === 'ar' ? 'العربية' : 'English'; ?></td>
                            </tr>
                            <tr>
                                <td><strong><?php echo __('last_login'); ?>:</strong></td>
                                <td>
                                    <?php if ($user['last_login']): ?>
                                        <?php 
                                        $lastLogin = new DateTime($user['last_login']);
                                        echo $lastLogin->format('Y-m-d H:i');
                                        ?>
                                    <?php else: ?>
                                        <span class="text-muted"><?php echo __('never'); ?></span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Recent Activity -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0"><?php echo __('recent_activity'); ?></h5>
            </div>
            <div class="card-body">
                <?php if (empty($recentActivity)): ?>
                    <p class="text-muted text-center py-3"><?php echo __('no_recent_activity'); ?></p>
                <?php else: ?>
                    <div class="timeline">
                        <?php foreach ($recentActivity as $activity): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker">
                                    <i class="fas fa-<?php echo getActivityIcon($activity['type']); ?> text-<?php echo getActivityColor($activity['type']); ?>"></i>
                                </div>
                                <div class="timeline-content">
                                    <h6 class="mb-1"><?php echo htmlspecialchars($activity['title']); ?></h6>
                                    <p class="mb-1"><?php echo htmlspecialchars($activity['description']); ?></p>
                                    <small class="text-muted">
                                        <?php 
                                        $activityDate = new DateTime($activity['created_at']);
                                        echo $activityDate->format('Y-m-d H:i');
                                        ?>
                                    </small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Security Settings -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0"><?php echo __('security_settings'); ?></h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><?php echo __('password'); ?></h6>
                        <p class="text-muted"><?php echo __('password_last_changed'); ?>: 
                            <?php 
                            if ($user['password_changed_at']) {
                                $passwordChanged = new DateTime($user['password_changed_at']);
                                echo $passwordChanged->format('Y-m-d');
                            } else {
                                echo __('never');
                            }
                            ?>
                        </p>
                        <a href="<?php echo getBaseUrl(); ?>/profile/change_password" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-key me-2"></i><?php echo __('change_password'); ?>
                        </a>
                    </div>
                    
                    <div class="col-md-6">
                        <h6><?php echo __('two_factor_authentication'); ?></h6>
                        <p class="text-muted">
                            <?php if ($user['two_factor_enabled']): ?>
                                <span class="text-success"><?php echo __('enabled'); ?></span>
                            <?php else: ?>
                                <span class="text-danger"><?php echo __('disabled'); ?></span>
                            <?php endif; ?>
                        </p>
                        <a href="<?php echo getBaseUrl(); ?>/profile/two_factor" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-shield-alt me-2"></i>
                            <?php echo $user['two_factor_enabled'] ? __('manage_2fa') : __('enable_2fa'); ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 30px;
    height: 30px;
    background: white;
    border: 2px solid #dee2e6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}
</style>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
