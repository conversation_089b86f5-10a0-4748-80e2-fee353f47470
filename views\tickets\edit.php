<?php
/**
 * Edit Ticket View
 * 
 * This file displays the form to edit a ticket.
 */

// Set page title
$pageTitle = __('edit_ticket');
$pageSubtitle = __('update_ticket_information');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 text-gradient mb-1"><?php echo __('edit_ticket'); ?></h1>
        <p class="text-muted mb-0"><?php echo htmlspecialchars($ticket['title']); ?></p>
    </div>
    
    <div class="btn-group">
        <a href="<?php echo getBaseUrl(); ?>/tickets" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_list'); ?>
        </a>
        <a href="<?php echo getBaseUrl(); ?>/tickets/view/<?php echo $ticket['id']; ?>" class="btn btn-primary">
            <i class="fas fa-eye me-2"></i><?php echo __('view'); ?>
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-10 mx-auto">
        <div class="card fade-in-up">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-ticket-alt me-2"></i><?php echo __('ticket_information'); ?>
                </h5>
            </div>
            <div class="card-body">
                <form action="<?php echo getBaseUrl(); ?>/tickets/update" method="post" id="ticketForm" enctype="multipart/form-data">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="id" value="<?php echo $ticket['id']; ?>">
                    
                    <?php if (!empty($errors['general'])): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo $errors['general']; ?>
                        </div>
                    <?php endif; ?>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="title" class="form-label"><?php echo __('title'); ?> <span class="text-danger">*</span></label>
                                <input type="text" class="form-control <?php echo isset($errors['title']) ? 'is-invalid' : ''; ?>" 
                                       id="title" name="title" value="<?php echo htmlspecialchars($ticketData['title'] ?? $ticket['title']); ?>" required>
                                <?php if (isset($errors['title'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['title']; ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="priority" class="form-label"><?php echo __('priority'); ?> <span class="text-danger">*</span></label>
                                <select class="form-select <?php echo isset($errors['priority']) ? 'is-invalid' : ''; ?>" id="priority" name="priority" required>
                                    <option value=""><?php echo __('select_priority'); ?></option>
                                    <option value="low" <?php echo ($ticketData['priority'] ?? $ticket['priority']) === 'low' ? 'selected' : ''; ?>><?php echo __('low'); ?></option>
                                    <option value="medium" <?php echo ($ticketData['priority'] ?? $ticket['priority']) === 'medium' ? 'selected' : ''; ?>><?php echo __('medium'); ?></option>
                                    <option value="high" <?php echo ($ticketData['priority'] ?? $ticket['priority']) === 'high' ? 'selected' : ''; ?>><?php echo __('high'); ?></option>
                                    <option value="critical" <?php echo ($ticketData['priority'] ?? $ticket['priority']) === 'critical' ? 'selected' : ''; ?>><?php echo __('critical'); ?></option>
                                </select>
                                <?php if (isset($errors['priority'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['priority']; ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="status" class="form-label"><?php echo __('status'); ?> <span class="text-danger">*</span></label>
                                <select class="form-select <?php echo isset($errors['status']) ? 'is-invalid' : ''; ?>" id="status" name="status" required>
                                    <option value=""><?php echo __('select_status'); ?></option>
                                    <option value="open" <?php echo ($ticketData['status'] ?? $ticket['status']) === 'open' ? 'selected' : ''; ?>><?php echo __('open'); ?></option>
                                    <option value="in_progress" <?php echo ($ticketData['status'] ?? $ticket['status']) === 'in_progress' ? 'selected' : ''; ?>><?php echo __('in_progress'); ?></option>
                                    <option value="resolved" <?php echo ($ticketData['status'] ?? $ticket['status']) === 'resolved' ? 'selected' : ''; ?>><?php echo __('resolved'); ?></option>
                                    <option value="closed" <?php echo ($ticketData['status'] ?? $ticket['status']) === 'closed' ? 'selected' : ''; ?>><?php echo __('closed'); ?></option>
                                </select>
                                <?php if (isset($errors['status'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['status']; ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="hospital_id" class="form-label"><?php echo __('hospital'); ?></label>
                                <select class="form-select" id="hospital_id" name="hospital_id">
                                    <option value=""><?php echo __('select_hospital'); ?></option>
                                    <?php foreach ($hospitals as $hospital): ?>
                                        <option value="<?php echo $hospital['id']; ?>" 
                                                <?php echo ($ticketData['hospital_id'] ?? $ticket['hospital_id']) == $hospital['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($hospital['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="device_id" class="form-label"><?php echo __('device'); ?></label>
                                <select class="form-select" id="device_id" name="device_id">
                                    <option value=""><?php echo __('select_device_optional'); ?></option>
                                    <?php foreach ($devices as $device): ?>
                                        <option value="<?php echo $device['id']; ?>" 
                                                <?php echo ($ticketData['device_id'] ?? $ticket['device_id']) == $device['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($device['name'] . ' (' . $device['serial_number'] . ')'); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="assigned_to" class="form-label"><?php echo __('assigned_to'); ?></label>
                                <select class="form-select" id="assigned_to" name="assigned_to">
                                    <option value=""><?php echo __('unassigned'); ?></option>
                                    <?php foreach ($users as $user): ?>
                                        <option value="<?php echo $user['id']; ?>" 
                                                <?php echo ($ticketData['assigned_to'] ?? $ticket['assigned_to']) == $user['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($user['full_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="category" class="form-label"><?php echo __('category'); ?></label>
                                <select class="form-select" id="category" name="category">
                                    <option value=""><?php echo __('select_category'); ?></option>
                                    <option value="technical" <?php echo ($ticketData['category'] ?? $ticket['category']) === 'technical' ? 'selected' : ''; ?>><?php echo __('technical'); ?></option>
                                    <option value="repair" <?php echo ($ticketData['category'] ?? $ticket['category']) === 'repair' ? 'selected' : ''; ?>><?php echo __('repair'); ?></option>
                                    <option value="calibration" <?php echo ($ticketData['category'] ?? $ticket['category']) === 'calibration' ? 'selected' : ''; ?>><?php echo __('calibration'); ?></option>
                                    <option value="training" <?php echo ($ticketData['category'] ?? $ticket['category']) === 'training' ? 'selected' : ''; ?>><?php echo __('training'); ?></option>
                                    <option value="other" <?php echo ($ticketData['category'] ?? $ticket['category']) === 'other' ? 'selected' : ''; ?>><?php echo __('other'); ?></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label"><?php echo __('description'); ?> <span class="text-danger">*</span></label>
                        <textarea class="form-control <?php echo isset($errors['description']) ? 'is-invalid' : ''; ?>" 
                                  id="description" name="description" rows="4" required><?php echo htmlspecialchars($ticketData['description'] ?? $ticket['description']); ?></textarea>
                        <?php if (isset($errors['description'])): ?>
                            <div class="invalid-feedback"><?php echo $errors['description']; ?></div>
                        <?php endif; ?>
                        <div class="form-text"><?php echo __('description_help'); ?></div>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center pt-3 border-top">
                        <a href="<?php echo getBaseUrl(); ?>/tickets" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>
                        </a>
                        <div class="d-flex gap-2">
                            <button type="reset" class="btn btn-outline-secondary">
                                <i class="fas fa-undo me-2"></i><?php echo __('reset'); ?>
                            </button>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <span class="btn-text">
                                    <i class="fas fa-save me-2"></i><?php echo __('update_ticket'); ?>
                                </span>
                                <span class="loading-spinner d-none"></span>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Load devices when hospital changes
document.getElementById('hospital_id').addEventListener('change', function() {
    const hospitalId = this.value;
    const deviceSelect = document.getElementById('device_id');
    
    // Clear current options
    deviceSelect.innerHTML = '<option value=""><?php echo __('select_device_optional'); ?></option>';
    
    if (hospitalId) {
        // Fetch devices for the selected hospital
        fetch(`<?php echo getBaseUrl(); ?>/api/devices?hospital_id=${hospitalId}`)
            .then(response => response.json())
            .then(data => {
                data.forEach(device => {
                    const option = document.createElement('option');
                    option.value = device.id;
                    option.textContent = `${device.name} (${device.serial_number})`;
                    deviceSelect.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Error loading devices:', error);
            });
    }
});

// Form submission
document.getElementById('ticketForm').addEventListener('submit', function(e) {
    const submitBtn = document.getElementById('submitBtn');
    const btnText = submitBtn.querySelector('.btn-text');
    const spinner = submitBtn.querySelector('.loading-spinner');
    
    // Show loading state
    btnText.classList.add('d-none');
    spinner.classList.remove('d-none');
    submitBtn.disabled = true;
});

// Priority color coding
document.getElementById('priority').addEventListener('change', function() {
    const priority = this.value;
    this.className = 'form-select';
    
    switch(priority) {
        case 'low':
            this.classList.add('border-success');
            break;
        case 'medium':
            this.classList.add('border-warning');
            break;
        case 'high':
            this.classList.add('border-danger');
            break;
        case 'critical':
            this.classList.add('border-danger', 'bg-danger', 'text-white');
            break;
    }
});

// Trigger priority color on load
document.getElementById('priority').dispatchEvent(new Event('change'));
</script>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
