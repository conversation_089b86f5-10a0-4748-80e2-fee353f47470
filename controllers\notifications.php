<?php
/**
 * Notifications Controller
 * 
 * This file handles notification-related operations.
 */

// Check if the user is logged in
requireLogin();

// Get the current user
$currentUser = getCurrentUser();
$userId = $currentUser['id'];

// Handle actions
$action = isset($url[1]) ? $url[1] : 'index';
$notificationId = isset($url[2]) ? (int)$url[2] : 0;

switch ($action) {
    case 'index':
        // Get all notifications for the current user
        $notifications = getAllNotifications($userId);
        
        // Include the notifications index view
        include 'views/notifications/index.php';
        break;
        
    case 'mark_as_read':
        // Mark a notification as read
        if ($notificationId) {
            $result = markNotificationAsRead($notificationId);
            
            if ($result) {
                // Set flash message
                setFlashMessage('success', __('notification_marked_as_read'));
            } else {
                setFlashMessage('error', __('notification_mark_failed'));
            }
        }
        
        // Redirect back to notifications
        redirect('notifications');
        break;
        
    case 'mark_all_as_read':
        // Mark all notifications as read
        $result = markAllNotificationsAsRead($userId);
        
        if ($result) {
            // Set flash message
            setFlashMessage('success', __('all_notifications_marked_as_read'));
        } else {
            setFlashMessage('error', __('notification_mark_failed'));
        }
        
        // Redirect back to notifications
        redirect('notifications');
        break;
        
    default:
        // 404 Not Found
        header("HTTP/1.0 404 Not Found");
        include 'views/404.php';
        break;
}
