<?php
/**
 * Reports Dashboard View
 * 
 * This file displays the reports dashboard.
 */

// Set page title
$pageTitle = __('reports');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 text-gradient mb-1"><?php echo __('reports_dashboard'); ?></h1>
        <p class="text-muted mb-0"><?php echo __('comprehensive_system_reports'); ?></p>
    </div>

    <div class="dropdown">
        <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
            <i class="fas fa-download me-2"></i><?php echo __('export'); ?>
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="#" onclick="exportReport('pdf')">
                <i class="fas fa-file-pdf me-2 text-danger"></i><?php echo __('export_pdf'); ?>
            </a></li>
            <li><a class="dropdown-item" href="#" onclick="exportReport('excel')">
                <i class="fas fa-file-excel me-2 text-success"></i><?php echo __('export_excel'); ?>
            </a></li>
            <li><a class="dropdown-item" href="#" onclick="exportReport('csv')">
                <i class="fas fa-file-csv me-2 text-info"></i><?php echo __('export_csv'); ?>
            </a></li>
        </ul>
    </div>
</div>

<!-- Enhanced Report Filters -->
<div class="card card-glass mb-4 fade-in-up">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-filter me-2"></i><?php echo __('report_filters'); ?>
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" action="<?php echo getBaseUrl(); ?>/reports" class="row g-3">
            <div class="col-md-3">
                <label for="hospital_id" class="form-label"><?php echo __('hospital'); ?></label>
                <select class="form-select" id="hospital_id" name="hospital_id">
                    <option value=""><?php echo __('all_hospitals'); ?></option>
                    <?php foreach ($hospitals as $hospital): ?>
                        <option value="<?php echo $hospital['id']; ?>" <?php echo ($hospitalId == $hospital['id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($hospital['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="date_from" class="form-label"><?php echo __('date_from'); ?></label>
                <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo $dateFrom ?? ''; ?>">
            </div>
            
            <div class="col-md-3">
                <label for="date_to" class="form-label"><?php echo __('date_to'); ?></label>
                <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo $dateTo ?? ''; ?>">
            </div>
            
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-chart-bar me-2"></i><?php echo __('generate_report'); ?>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Enhanced Report Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-4">
        <div class="card stats-card primary h-100 fade-in-up" style="animation-delay: 0.1s;">
            <div class="card-body text-center">
                <div class="stats-icon mb-3">
                    <i class="fas fa-laptop-medical fa-2x text-primary"></i>
                </div>
                <div class="stats-number animate" data-target="<?php echo $stats['total_devices'] ?? 0; ?>">0</div>
                <div class="stats-text"><?php echo __('total_devices'); ?></div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card stats-card warning h-100 fade-in-up" style="animation-delay: 0.2s;">
            <div class="card-body text-center">
                <div class="stats-icon mb-3">
                    <i class="fas fa-wrench fa-2x text-warning"></i>
                </div>
                <div class="stats-number animate" data-target="<?php echo $stats['maintenance_due'] ?? 0; ?>">0</div>
                <div class="stats-text"><?php echo __('maintenance_due'); ?></div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card stats-card danger h-100 fade-in-up" style="animation-delay: 0.3s;">
            <div class="card-body text-center">
                <div class="stats-icon mb-3">
                    <i class="fas fa-exclamation-triangle fa-2x text-danger"></i>
                </div>
                <div class="stats-number animate" data-target="<?php echo $stats['open_tickets'] ?? 0; ?>">0</div>
                <div class="stats-text"><?php echo __('open_tickets'); ?></div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card stats-card info h-100 fade-in-up" style="animation-delay: 0.4s;">
            <div class="card-body text-center">
                <div class="stats-icon mb-3">
                    <i class="fas fa-calendar-times fa-2x text-info"></i>
                </div>
                <div class="stats-number animate" data-target="<?php echo $stats['warranty_expiring'] ?? 0; ?>">0</div>
                <div class="stats-text"><?php echo __('warranty_expiring'); ?></div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><?php echo __('devices_by_status'); ?></h5>
            </div>
            <div class="card-body">
                <canvas id="deviceStatusChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><?php echo __('maintenance_trends'); ?></h5>
            </div>
            <div class="card-body">
                <canvas id="maintenanceTrendChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Tables Row -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><?php echo __('recent_maintenance'); ?></h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th><?php echo __('device'); ?></th>
                                <th><?php echo __('date'); ?></th>
                                <th><?php echo __('status'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($recentMaintenance)): ?>
                                <tr>
                                    <td colspan="3" class="text-center text-muted"><?php echo __('no_recent_maintenance'); ?></td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($recentMaintenance as $maintenance): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($maintenance['device_name']); ?></td>
                                        <td><?php echo date('Y-m-d', strtotime($maintenance['completed_date'])); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo getMaintenanceStatusColor($maintenance['status']); ?>">
                                                <?php echo __($maintenance['status']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><?php echo __('urgent_tickets'); ?></h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th><?php echo __('ticket'); ?></th>
                                <th><?php echo __('device'); ?></th>
                                <th><?php echo __('priority'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($urgentTickets)): ?>
                                <tr>
                                    <td colspan="3" class="text-center text-muted"><?php echo __('no_urgent_tickets'); ?></td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($urgentTickets as $ticket): ?>
                                    <tr>
                                        <td>
                                            <a href="<?php echo getBaseUrl(); ?>/tickets/view/<?php echo $ticket['id']; ?>">
                                                #<?php echo $ticket['id']; ?>
                                            </a>
                                        </td>
                                        <td><?php echo htmlspecialchars($ticket['device_name'] ?? 'N/A'); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo getPriorityColor($ticket['priority']); ?>">
                                                <?php echo __($ticket['priority']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Device Status Chart
const deviceStatusCtx = document.getElementById('deviceStatusChart').getContext('2d');
const deviceStatusChart = new Chart(deviceStatusCtx, {
    type: 'doughnut',
    data: {
        labels: ['<?php echo __('operational'); ?>', '<?php echo __('under_maintenance'); ?>', '<?php echo __('out_of_order'); ?>', '<?php echo __('retired'); ?>'],
        datasets: [{
            data: [
                <?php echo $chartData['device_status']['operational'] ?? 0; ?>,
                <?php echo $chartData['device_status']['under_maintenance'] ?? 0; ?>,
                <?php echo $chartData['device_status']['out_of_order'] ?? 0; ?>,
                <?php echo $chartData['device_status']['retired'] ?? 0; ?>
            ],
            backgroundColor: [
                '#28a745',
                '#ffc107',
                '#dc3545',
                '#6c757d'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});

// Maintenance Trend Chart
const maintenanceTrendCtx = document.getElementById('maintenanceTrendChart').getContext('2d');
const maintenanceTrendChart = new Chart(maintenanceTrendCtx, {
    type: 'line',
    data: {
        labels: <?php echo json_encode($chartData['maintenance_trend']['labels'] ?? []); ?>,
        datasets: [{
            label: '<?php echo __('maintenance_completed'); ?>',
            data: <?php echo json_encode($chartData['maintenance_trend']['data'] ?? []); ?>,
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Export function
function exportReport(format) {
    const params = new URLSearchParams(window.location.search);
    params.set('export', format);
    
    window.open('<?php echo getBaseUrl(); ?>/reports/export?' + params.toString(), '_blank');
}

// Animate counters
function animateCounters() {
    const counters = document.querySelectorAll(".stats-number.animate");

    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute("data-target"));
        const increment = target / 100;
        let current = 0;

        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            counter.textContent = Math.floor(current);
        }, 20);
    });
}

// Initialize animations when page loads
document.addEventListener("DOMContentLoaded", function() {
    // Animate counters after a short delay
    setTimeout(animateCounters, 500);

    // Add staggered animation to cards
    const cards = document.querySelectorAll(".fade-in-up");
    cards.forEach((card, index) => {
        card.style.animationDelay = (index * 0.1) + "s";
    });
});

// Auto-submit form when filters change
document.getElementById('hospital_id').addEventListener('change', function() {
    this.form.submit();
});
</script>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
