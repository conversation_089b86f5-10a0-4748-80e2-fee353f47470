<?php
/**
 * Roles View
 *
 * This view displays detailed information about a role.
 */

// Set page title
$pageTitle = __('role_details');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 text-gradient mb-1"><?php echo htmlspecialchars($role['display_name']); ?></h1>
        <p class="text-muted mb-0">
            <code><?php echo htmlspecialchars($role['name']); ?></code>
            <?php if ($role['is_system']): ?>
                <span class="badge bg-secondary ms-2"><?php echo __('system'); ?></span>
            <?php else: ?>
                <span class="badge bg-primary ms-2"><?php echo __('custom'); ?></span>
            <?php endif; ?>
        </p>
    </div>

    <div class="btn-group" role="group">
        <a href="<?php echo getBaseUrl(); ?>/roles" class="btn btn-outline-secondary">
            <?php echo __('back_to_list'); ?>
        </a>
        
        <?php if (hasPermission('manage_roles') && !$role['is_system']): ?>
            <a href="<?php echo getBaseUrl(); ?>/roles/edit/<?php echo $role['id']; ?>" class="btn btn-primary">
                <?php echo __('edit'); ?>
            </a>
        <?php endif; ?>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card fade-in-up mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <?php echo __('role_information'); ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('role_name'); ?></label>
                            <div><code><?php echo htmlspecialchars($role['name']); ?></code></div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('display_name'); ?></label>
                            <div><?php echo htmlspecialchars($role['display_name']); ?></div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('display_order'); ?></label>
                            <div><?php echo $role['display_order']; ?></div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('type'); ?></label>
                            <div>
                                <?php if ($role['is_system']): ?>
                                    <span class="badge bg-secondary"><?php echo __('system'); ?></span>
                                <?php else: ?>
                                    <span class="badge bg-primary"><?php echo __('custom'); ?></span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <?php if (!empty($role['description'])): ?>
                <div class="mb-3">
                    <label class="form-label text-muted"><?php echo __('description'); ?></label>
                    <div><?php echo nl2br(htmlspecialchars($role['description'])); ?></div>
                </div>
                <?php endif; ?>
                
                <div class="mb-3">
                    <label class="form-label text-muted"><?php echo __('created_at'); ?></label>
                    <div><?php echo date('Y-m-d H:i:s', strtotime($role['created_at'])); ?></div>
                </div>
                
                <?php if ($role['updated_at'] !== $role['created_at']): ?>
                <div class="mb-3">
                    <label class="form-label text-muted"><?php echo __('updated_at'); ?></label>
                    <div><?php echo date('Y-m-d H:i:s', strtotime($role['updated_at'])); ?></div>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="card fade-in-up">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <?php echo __('permissions'); ?>
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($rolePermissions)): ?>
                    <div class="row">
                        <?php 
                        $permissionGroups = [
                            'Dashboard' => ['view_dashboard'],
                            'Users' => ['view_users', 'manage_users'],
                            'Hospitals' => ['view_hospitals', 'manage_hospitals'],
                            'Departments' => ['view_departments', 'manage_departments'],
                            'Devices' => ['view_devices', 'manage_devices'],
                            'Maintenance' => ['view_maintenance', 'manage_maintenance'],
                            'Tickets' => ['view_tickets', 'manage_tickets'],
                            'Reports' => ['view_reports', 'export_data'],
                            'Roles' => ['view_roles', 'manage_roles']
                        ];
                        
                        foreach ($permissionGroups as $groupName => $groupPermissions): 
                            $hasGroupPermissions = array_intersect($groupPermissions, $rolePermissions);
                            if (!empty($hasGroupPermissions)): ?>
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card">
                                        <div class="card-header py-2">
                                            <h6 class="mb-0"><?php echo __($groupName); ?></h6>
                                        </div>
                                        <div class="card-body py-2">
                                            <?php foreach ($groupPermissions as $permission): ?>
                                                <?php if (in_array($permission, $rolePermissions) && isset($availablePermissions[$permission])): ?>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" checked disabled>
                                                        <label class="form-check-label">
                                                            <?php echo $availablePermissions[$permission]; ?>
                                                        </label>
                                                    </div>
                                                <?php endif; ?>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <p class="text-muted mb-0"><?php echo __('no_permissions_assigned'); ?></p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card fade-in-up">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <?php echo __('statistics'); ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-12 mb-3">
                        <div class="stat-card">
                            <div class="stat-number text-primary"><?php echo $stats['total_users']; ?></div>
                            <div class="stat-label"><?php echo __('total_users'); ?></div>
                        </div>
                    </div>
                    
                    <div class="col-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-number text-success"><?php echo $stats['active_users']; ?></div>
                            <div class="stat-label"><?php echo __('active'); ?></div>
                        </div>
                    </div>
                    
                    <div class="col-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-number text-warning"><?php echo $stats['inactive_users']; ?></div>
                            <div class="stat-label"><?php echo __('inactive'); ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Add scripts
$scripts = '
<script>
    $(document).ready(function() {
        // Add any specific scripts for the role view page
    });
</script>
';

// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
