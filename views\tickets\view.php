<?php
/**
 * Ticket View
 * 
 * This file displays the details of a ticket.
 */

// Set page title
$pageTitle = __('ticket_details');
$pageSubtitle = htmlspecialchars($ticket['title']);

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 text-gradient mb-1"><?php echo __('ticket_details'); ?></h1>
        <p class="text-muted mb-0">#<?php echo $ticket['id']; ?> - <?php echo htmlspecialchars($ticket['title']); ?></p>
    </div>
    
    <div class="btn-group">
        <a href="<?php echo getBaseUrl(); ?>/tickets" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_list'); ?>
        </a>
        
        <?php if (hasPermission('manage_tickets') || $ticket['reported_by'] == $_SESSION['user']['id']): ?>
            <a href="<?php echo getBaseUrl(); ?>/tickets/edit/<?php echo $ticket['id']; ?>" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i><?php echo __('edit'); ?>
            </a>
        <?php endif; ?>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card fade-in-up">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-ticket-alt me-2"></i><?php echo __('ticket_information'); ?>
                    </h5>
                    <div class="d-flex gap-2">
                        <span class="badge bg-<?php 
                            echo $ticket['priority'] === 'low' ? 'success' : 
                                ($ticket['priority'] === 'medium' ? 'warning' : 
                                    ($ticket['priority'] === 'high' ? 'danger' : 'dark')); 
                        ?>">
                            <?php echo __(strtoupper($ticket['priority'])); ?>
                        </span>
                        <span class="badge bg-<?php 
                            echo $ticket['status'] === 'open' ? 'danger' : 
                                ($ticket['status'] === 'in_progress' ? 'warning' : 
                                    ($ticket['status'] === 'resolved' ? 'success' : 'secondary')); 
                        ?>">
                            <?php echo __(strtoupper($ticket['status'])); ?>
                        </span>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="40%"><?php echo __('title'); ?></th>
                                <td><?php echo htmlspecialchars($ticket['title']); ?></td>
                            </tr>
                            <tr>
                                <th><?php echo __('reported_by'); ?></th>
                                <td>
                                    <?php if (hasPermission('view_users')): ?>
                                        <a href="<?php echo getBaseUrl(); ?>/users/view/<?php echo $ticket['reported_by']; ?>">
                                            <?php echo htmlspecialchars($ticket['reporter_name']); ?>
                                        </a>
                                    <?php else: ?>
                                        <?php echo htmlspecialchars($ticket['reporter_name']); ?>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <th><?php echo __('assigned_to'); ?></th>
                                <td>
                                    <?php if ($ticket['assigned_to']): ?>
                                        <?php if (hasPermission('view_users')): ?>
                                            <a href="<?php echo getBaseUrl(); ?>/users/view/<?php echo $ticket['assigned_to']; ?>">
                                                <?php echo htmlspecialchars($ticket['assignee_name']); ?>
                                            </a>
                                        <?php else: ?>
                                            <?php echo htmlspecialchars($ticket['assignee_name']); ?>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="text-muted"><?php echo __('unassigned'); ?></span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <th><?php echo __('category'); ?></th>
                                <td>
                                    <?php if ($ticket['category']): ?>
                                        <span class="badge bg-info"><?php echo __(strtolower($ticket['category'])); ?></span>
                                    <?php else: ?>
                                        <span class="text-muted"><?php echo __('uncategorized'); ?></span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="40%"><?php echo __('hospital'); ?></th>
                                <td>
                                    <?php if ($ticket['hospital_id'] && hasPermission('view_hospitals')): ?>
                                        <a href="<?php echo getBaseUrl(); ?>/hospitals/view/<?php echo $ticket['hospital_id']; ?>">
                                            <?php echo htmlspecialchars($ticket['hospital_name']); ?>
                                        </a>
                                    <?php elseif ($ticket['hospital_id']): ?>
                                        <?php echo htmlspecialchars($ticket['hospital_name']); ?>
                                    <?php else: ?>
                                        <span class="text-muted"><?php echo __('no_hospital'); ?></span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <th><?php echo __('device'); ?></th>
                                <td>
                                    <?php if ($ticket['device_id'] && hasPermission('view_devices')): ?>
                                        <a href="<?php echo getBaseUrl(); ?>/devices/view/<?php echo $ticket['device_id']; ?>">
                                            <?php echo htmlspecialchars($ticket['device_name']); ?>
                                        </a>
                                    <?php elseif ($ticket['device_id']): ?>
                                        <?php echo htmlspecialchars($ticket['device_name']); ?>
                                    <?php else: ?>
                                        <span class="text-muted"><?php echo __('no_device'); ?></span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <th><?php echo __('created_at'); ?></th>
                                <td><?php echo date('Y-m-d H:i', strtotime($ticket['created_at'])); ?></td>
                            </tr>
                            <tr>
                                <th><?php echo __('updated_at'); ?></th>
                                <td><?php echo date('Y-m-d H:i', strtotime($ticket['updated_at'])); ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <div class="mb-4">
                    <h6><?php echo __('description'); ?></h6>
                    <div class="bg-light p-3 rounded">
                        <?php echo nl2br(htmlspecialchars($ticket['description'])); ?>
                    </div>
                </div>
                
                <?php if (!empty($ticket['attachments'])): ?>
                <div class="mb-4">
                    <h6><?php echo __('attachments'); ?></h6>
                    <div class="row">
                        <?php foreach ($ticket['attachments'] as $attachment): ?>
                            <div class="col-md-4 mb-2">
                                <div class="card">
                                    <div class="card-body p-2">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-file me-2"></i>
                                            <div class="flex-grow-1">
                                                <small class="d-block"><?php echo htmlspecialchars($attachment['original_name']); ?></small>
                                                <small class="text-muted"><?php echo formatFileSize($attachment['file_size']); ?></small>
                                            </div>
                                            <a href="<?php echo getBaseUrl(); ?>/uploads/tickets/<?php echo $attachment['file_name']; ?>" 
                                               class="btn btn-sm btn-outline-primary" target="_blank">
                                                <i class="fas fa-download"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Comments Section -->
        <div class="card mt-4 fade-in-up" style="animation-delay: 0.2s;">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-comments me-2"></i><?php echo __('comments'); ?>
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($comments)): ?>
                    <div class="timeline">
                        <?php foreach ($comments as $comment): ?>
                            <div class="timeline-item mb-4">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <div class="avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                            <?php echo strtoupper(substr($comment['commenter_name'], 0, 1)); ?>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <div class="bg-light p-3 rounded">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <strong><?php echo htmlspecialchars($comment['commenter_name']); ?></strong>
                                                <small class="text-muted"><?php echo date('M d, Y H:i', strtotime($comment['created_at'])); ?></small>
                                            </div>
                                            <p class="mb-0"><?php echo nl2br(htmlspecialchars($comment['comment'])); ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <p class="text-muted text-center"><?php echo __('no_comments'); ?></p>
                <?php endif; ?>
                
                <!-- Add Comment Form -->
                <?php if (hasPermission('manage_tickets') || $ticket['reported_by'] == $_SESSION['user']['id'] || $ticket['assigned_to'] == $_SESSION['user']['id']): ?>
                <form action="<?php echo getBaseUrl(); ?>/tickets/add_comment" method="post" class="mt-4">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="ticket_id" value="<?php echo $ticket['id']; ?>">
                    
                    <div class="mb-3">
                        <label for="comment" class="form-label"><?php echo __('add_comment'); ?></label>
                        <textarea class="form-control" id="comment" name="comment" rows="3" required></textarea>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-comment me-2"></i><?php echo __('add_comment'); ?>
                    </button>
                </form>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card fade-in-up" style="animation-delay: 0.3s;">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cogs me-2"></i><?php echo __('quick_actions'); ?>
                </h5>
            </div>
            <div class="card-body">
                <?php if (hasPermission('manage_tickets')): ?>
                    <div class="d-grid gap-2">
                        <?php if ($ticket['status'] !== 'closed'): ?>
                            <form action="<?php echo getBaseUrl(); ?>/tickets/update_status" method="post" class="d-inline">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                <input type="hidden" name="ticket_id" value="<?php echo $ticket['id']; ?>">
                                <input type="hidden" name="status" value="<?php echo $ticket['status'] === 'open' ? 'in_progress' : 'resolved'; ?>">
                                <button type="submit" class="btn btn-success w-100">
                                    <i class="fas fa-<?php echo $ticket['status'] === 'open' ? 'play' : 'check'; ?> me-2"></i>
                                    <?php echo $ticket['status'] === 'open' ? __('start_progress') : __('mark_resolved'); ?>
                                </button>
                            </form>
                        <?php endif; ?>
                        
                        <?php if ($ticket['status'] === 'resolved'): ?>
                            <form action="<?php echo getBaseUrl(); ?>/tickets/update_status" method="post" class="d-inline">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                <input type="hidden" name="ticket_id" value="<?php echo $ticket['id']; ?>">
                                <input type="hidden" name="status" value="closed">
                                <button type="submit" class="btn btn-secondary w-100">
                                    <i class="fas fa-times me-2"></i><?php echo __('close_ticket'); ?>
                                </button>
                            </form>
                        <?php endif; ?>
                        
                        <a href="<?php echo getBaseUrl(); ?>/tickets/edit/<?php echo $ticket['id']; ?>" class="btn btn-outline-primary">
                            <i class="fas fa-edit me-2"></i><?php echo __('edit_ticket'); ?>
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="card mt-4 fade-in-up" style="animation-delay: 0.4s;">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i><?php echo __('ticket_timeline'); ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="timeline-sm">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <small class="text-muted"><?php echo date('M d, Y H:i', strtotime($ticket['created_at'])); ?></small>
                            <p class="mb-0"><?php echo __('ticket_created'); ?></p>
                        </div>
                    </div>
                    
                    <?php if ($ticket['assigned_to']): ?>
                    <div class="timeline-item">
                        <div class="timeline-marker bg-info"></div>
                        <div class="timeline-content">
                            <small class="text-muted"><?php echo date('M d, Y H:i', strtotime($ticket['updated_at'])); ?></small>
                            <p class="mb-0"><?php echo __('ticket_assigned'); ?></p>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($ticket['status'] !== 'open'): ?>
                    <div class="timeline-item">
                        <div class="timeline-marker bg-<?php echo $ticket['status'] === 'resolved' ? 'success' : 'warning'; ?>"></div>
                        <div class="timeline-content">
                            <small class="text-muted"><?php echo date('M d, Y H:i', strtotime($ticket['updated_at'])); ?></small>
                            <p class="mb-0"><?php echo __('status_changed_to') . ' ' . __($ticket['status']); ?></p>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline-sm .timeline-item {
    position: relative;
    padding-left: 2rem;
    padding-bottom: 1rem;
}

.timeline-sm .timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: 0.5rem;
    top: 1.5rem;
    bottom: -1rem;
    width: 2px;
    background: #dee2e6;
}

.timeline-sm .timeline-marker {
    position: absolute;
    left: 0;
    top: 0.25rem;
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
}

.avatar {
    font-size: 0.875rem;
    font-weight: 600;
}
</style>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
