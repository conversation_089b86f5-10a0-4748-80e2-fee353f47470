<?php
/**
 * Ticket Model
 * 
 * This class handles ticket-related database operations.
 */
class Ticket {
    private $pdo;
    
    /**
     * Constructor
     * 
     * @param PDO $pdo The PDO instance
     */
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * Get all tickets
     * 
     * @param int $deviceId Optional device ID to filter tickets
     * @param int $hospitalId Optional hospital ID to filter tickets
     * @param int $reportedBy Optional user ID who reported the tickets
     * @param int $assignedTo Optional user ID who is assigned to the tickets
     * @return array The tickets
     */
    public function getAll($deviceId = null, $hospitalId = null, $reportedBy = null, $assignedTo = null) {
        try {
            $sql = "
                SELECT t.*, d.name AS device_name, d.serial_number,
                       h.name AS hospital_name, dp.name AS department_name,
                       r.full_name AS reported_by_name, a.full_name AS assigned_to_name
                FROM tickets t
                LEFT JOIN devices d ON t.device_id = d.id
                LEFT JOIN hospitals h ON d.hospital_id = h.id
                LEFT JOIN departments dp ON d.department_id = dp.id
                JOIN users r ON t.reported_by = r.id
                LEFT JOIN users a ON t.assigned_to = a.id
            ";
            $params = [];
            $where = [];
            
            if ($deviceId) {
                $where[] = "t.device_id = ?";
                $params[] = $deviceId;
            }
            
            if ($hospitalId) {
                $where[] = "d.hospital_id = ?";
                $params[] = $hospitalId;
            }
            
            if ($reportedBy) {
                $where[] = "t.reported_by = ?";
                $params[] = $reportedBy;
            }
            
            if ($assignedTo) {
                $where[] = "t.assigned_to = ?";
                $params[] = $assignedTo;
            }
            
            if (!empty($where)) {
                $sql .= " WHERE " . implode(" AND ", $where);
            }
            
            $sql .= " ORDER BY t.created_at DESC";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get All Tickets Error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get a ticket by ID
     * 
     * @param int $id The ticket ID
     * @return array|bool The ticket or false if not found
     */
    public function getById($id) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT t.*, d.name AS device_name, d.serial_number,
                       h.name AS hospital_name, dp.name AS department_name,
                       r.full_name AS reported_by_name, a.full_name AS assigned_to_name
                FROM tickets t
                LEFT JOIN devices d ON t.device_id = d.id
                LEFT JOIN hospitals h ON d.hospital_id = h.id
                LEFT JOIN departments dp ON d.department_id = dp.id
                JOIN users r ON t.reported_by = r.id
                LEFT JOIN users a ON t.assigned_to = a.id
                WHERE t.id = ?
            ");
            
            $stmt->execute([$id]);
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Get Ticket By ID Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create a new ticket
     * 
     * @param array $data The ticket data
     * @return int|bool The ticket ID if successful, false otherwise
     */
    public function create($data) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO tickets (
                    device_id, reported_by, assigned_to, title,
                    description, priority, status
                ) VALUES (
                    ?, ?, ?, ?, ?, ?, ?
                )
            ");
            
            $stmt->execute([
                $data['device_id'] ?? null,
                $data['reported_by'],
                $data['assigned_to'] ?? null,
                $data['title'],
                $data['description'],
                $data['priority'],
                $data['status'] ?? 'open'
            ]);

            $ticketId = $this->pdo->lastInsertId();

            // Create notification
            createTicketNotification($ticketId, 'created', $data['reported_by']);

            // Update device status if ticket is created and device is specified
            if (!empty($data['device_id'])) {
                $deviceModel = new Device($this->pdo);
                $deviceModel->updateStatus($data['device_id'], 'out_of_order');
            }
            
            return $ticketId;
        } catch (PDOException $e) {
            error_log("Create Ticket Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update a ticket
     * 
     * @param int $id The ticket ID
     * @param array $data The ticket data
     * @param int $userId The user ID who is updating the ticket
     * @return bool True if successful, false otherwise
     */
    public function update($id, $data, $userId) {
        try {
            $oldTicket = $this->getById($id);
            
            $stmt = $this->pdo->prepare("
                UPDATE tickets SET
                    device_id = ?,
                    assigned_to = ?,
                    title = ?,
                    description = ?,
                    priority = ?,
                    status = ?,
                    updated_at = NOW()
                WHERE id = ?
            ");
            
            $result = $stmt->execute([
                $data['device_id'] ?? null,
                $data['assigned_to'] ?? null,
                $data['title'],
                $data['description'],
                $data['priority'],
                $data['status'],
                $id
            ]);

            if ($result) {
                // Create ticket update
                $this->createUpdate($id, $userId, $data['comment'] ?? '', $oldTicket['status'], $data['status']);

                // Create notification
                if ($oldTicket['assigned_to'] != $data['assigned_to'] && $data['assigned_to']) {
                    createTicketNotification($id, 'assigned', $userId);
                } elseif ($oldTicket['status'] != $data['status']) {
                    if ($data['status'] === 'resolved') {
                        createTicketNotification($id, 'resolved', $userId);
                    } elseif ($data['status'] === 'closed') {
                        createTicketNotification($id, 'closed', $userId);
                    } else {
                        createTicketNotification($id, 'updated', $userId);
                    }
                } else {
                    createTicketNotification($id, 'updated', $userId);
                }

                // Update device status based on ticket status (only if device is specified)
                if (!empty($data['device_id'])) {
                    $deviceModel = new Device($this->pdo);

                    if ($data['status'] === 'resolved' || $data['status'] === 'closed') {
                        $deviceModel->updateStatus($data['device_id'], 'operational');
                    } elseif ($data['status'] === 'in_progress') {
                        $deviceModel->updateStatus($data['device_id'], 'under_maintenance');
                    }
                }
            }
            
            return $result;
        } catch (PDOException $e) {
            error_log("Update Ticket Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete a ticket
     * 
     * @param int $id The ticket ID
     * @return bool True if successful, false otherwise
     */
    public function delete($id) {
        try {
            // Delete ticket updates first
            $stmt = $this->pdo->prepare("DELETE FROM ticket_updates WHERE ticket_id = ?");
            $stmt->execute([$id]);
            
            // Delete the ticket
            $stmt = $this->pdo->prepare("DELETE FROM tickets WHERE id = ?");
            return $stmt->execute([$id]);
        } catch (PDOException $e) {
            error_log("Delete Ticket Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get ticket updates
     * 
     * @param int $ticketId The ticket ID
     * @return array The ticket updates
     */
    public function getUpdates($ticketId) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT tu.*, u.full_name AS user_name
                FROM ticket_updates tu
                JOIN users u ON tu.user_id = u.id
                WHERE tu.ticket_id = ?
                ORDER BY tu.created_at DESC
            ");
            
            $stmt->execute([$ticketId]);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get Ticket Updates Error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Create a ticket update
     * 
     * @param int $ticketId The ticket ID
     * @param int $userId The user ID
     * @param string $comment The comment
     * @param string $oldStatus The old status
     * @param string $newStatus The new status
     * @return int|bool The update ID if successful, false otherwise
     */
    public function createUpdate($ticketId, $userId, $comment, $oldStatus = null, $newStatus = null) {
        try {
            $statusChange = '';
            
            if ($oldStatus && $newStatus && $oldStatus !== $newStatus) {
                $statusChange = $oldStatus . ' -> ' . $newStatus;
            }
            
            $stmt = $this->pdo->prepare("
                INSERT INTO ticket_updates (
                    ticket_id, user_id, comment, status_change
                ) VALUES (
                    ?, ?, ?, ?
                )
            ");
            
            $stmt->execute([
                $ticketId,
                $userId,
                $comment,
                $statusChange
            ]);
            
            return $this->pdo->lastInsertId();
        } catch (PDOException $e) {
            error_log("Create Ticket Update Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Assign a ticket
     * 
     * @param int $id The ticket ID
     * @param int $assignedTo The user ID to assign the ticket to
     * @param int $userId The user ID who is assigning the ticket
     * @return bool True if successful, false otherwise
     */
    public function assign($id, $assignedTo, $userId) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE tickets SET
                    assigned_to = ?,
                    status = CASE WHEN status = 'open' THEN 'in_progress' ELSE status END,
                    updated_at = NOW()
                WHERE id = ?
            ");
            
            $result = $stmt->execute([$assignedTo, $id]);
            
            if ($result) {
                // Create ticket update
                $this->createUpdate(
                    $id,
                    $userId,
                    __('ticket_assigned_to') . ' ' . $this->getUserName($assignedTo)
                );
                
                // Create notification
                createTicketNotification($id, 'assigned', $userId);
                
                // Update device status
                $ticket = $this->getById($id);
                $deviceModel = new Device($this->pdo);
                $deviceModel->updateStatus($ticket['device_id'], 'under_maintenance');
            }
            
            return $result;
        } catch (PDOException $e) {
            error_log("Assign Ticket Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Change ticket status
     * 
     * @param int $id The ticket ID
     * @param string $status The new status
     * @param int $userId The user ID who is changing the status
     * @param string $comment Optional comment
     * @return bool True if successful, false otherwise
     */
    public function changeStatus($id, $status, $userId, $comment = '') {
        try {
            $oldTicket = $this->getById($id);
            
            $stmt = $this->pdo->prepare("
                UPDATE tickets SET
                    status = ?,
                    updated_at = NOW()
                WHERE id = ?
            ");
            
            $result = $stmt->execute([$status, $id]);
            
            if ($result) {
                // Create ticket update
                $this->createUpdate($id, $userId, $comment, $oldTicket['status'], $status);
                
                // Create notification
                if ($status === 'resolved') {
                    createTicketNotification($id, 'resolved', $userId);
                } elseif ($status === 'closed') {
                    createTicketNotification($id, 'closed', $userId);
                } else {
                    createTicketNotification($id, 'updated', $userId);
                }
                
                // Update device status based on ticket status
                $deviceModel = new Device($this->pdo);
                
                if ($status === 'resolved' || $status === 'closed') {
                    $deviceModel->updateStatus($oldTicket['device_id'], 'operational');
                } elseif ($status === 'in_progress') {
                    $deviceModel->updateStatus($oldTicket['device_id'], 'under_maintenance');
                } elseif ($status === 'open') {
                    $deviceModel->updateStatus($oldTicket['device_id'], 'out_of_order');
                }
            }
            
            return $result;
        } catch (PDOException $e) {
            error_log("Change Ticket Status Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get tickets by status
     * 
     * @param string $status The status
     * @param int $hospitalId Optional hospital ID to filter tickets
     * @return array The tickets
     */
    public function getByStatus($status, $hospitalId = null) {
        try {
            $sql = "
                SELECT t.*, d.name AS device_name, d.serial_number,
                       h.name AS hospital_name, dp.name AS department_name,
                       r.full_name AS reported_by_name, a.full_name AS assigned_to_name
                FROM tickets t
                JOIN devices d ON t.device_id = d.id
                JOIN hospitals h ON d.hospital_id = h.id
                JOIN departments dp ON d.department_id = dp.id
                JOIN users r ON t.reported_by = r.id
                LEFT JOIN users a ON t.assigned_to = a.id
                WHERE t.status = ?
            ";
            $params = [$status];
            
            if ($hospitalId) {
                $sql .= " AND d.hospital_id = ?";
                $params[] = $hospitalId;
            }
            
            $sql .= " ORDER BY t.created_at DESC";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get Tickets By Status Error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get tickets by priority
     * 
     * @param string $priority The priority
     * @param int $hospitalId Optional hospital ID to filter tickets
     * @return array The tickets
     */
    public function getByPriority($priority, $hospitalId = null) {
        try {
            $sql = "
                SELECT t.*, d.name AS device_name, d.serial_number,
                       h.name AS hospital_name, dp.name AS department_name,
                       r.full_name AS reported_by_name, a.full_name AS assigned_to_name
                FROM tickets t
                JOIN devices d ON t.device_id = d.id
                JOIN hospitals h ON d.hospital_id = h.id
                JOIN departments dp ON d.department_id = dp.id
                JOIN users r ON t.reported_by = r.id
                LEFT JOIN users a ON t.assigned_to = a.id
                WHERE t.priority = ?
            ";
            $params = [$priority];
            
            if ($hospitalId) {
                $sql .= " AND d.hospital_id = ?";
                $params[] = $hospitalId;
            }
            
            $sql .= " ORDER BY t.created_at DESC";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get Tickets By Priority Error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Count tickets
     * 
     * @param int $hospitalId Optional hospital ID to filter tickets
     * @return int The number of tickets
     */
    public function count($hospitalId = null) {
        try {
            $sql = "
                SELECT COUNT(*) FROM tickets t
                JOIN devices d ON t.device_id = d.id
            ";
            $params = [];
            
            if ($hospitalId) {
                $sql .= " WHERE d.hospital_id = ?";
                $params[] = $hospitalId;
            }
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            return (int) $stmt->fetchColumn();
        } catch (PDOException $e) {
            error_log("Count Tickets Error: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Count tickets by status
     * 
     * @param string $status The status
     * @param int $hospitalId Optional hospital ID to filter tickets
     * @return int The number of tickets
     */
    public function countByStatus($status, $hospitalId = null) {
        try {
            $sql = "
                SELECT COUNT(*) FROM tickets t
                JOIN devices d ON t.device_id = d.id
                WHERE t.status = ?
            ";
            $params = [$status];
            
            if ($hospitalId) {
                $sql .= " AND d.hospital_id = ?";
                $params[] = $hospitalId;
            }
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            return (int) $stmt->fetchColumn();
        } catch (PDOException $e) {
            error_log("Count Tickets By Status Error: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Count tickets by user
     *
     * @param int $userId The user ID
     * @param string $type Type of count: 'created' or 'assigned'
     * @return int The number of tickets
     */
    public function countByUser($userId, $type = 'created') {
        try {
            if ($type === 'assigned') {
                $sql = "SELECT COUNT(*) FROM tickets WHERE assigned_to = ?";
            } else {
                $sql = "SELECT COUNT(*) FROM tickets WHERE reported_by = ?";
            }

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$userId]);

            return (int) $stmt->fetchColumn();
        } catch (PDOException $e) {
            error_log("Count Tickets By User Error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get user name by ID
     *
     * @param int $userId The user ID
     * @return string The user name
     */
    private function getUserName($userId) {
        try {
            $stmt = $this->pdo->prepare("SELECT full_name FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch();

            return $user ? $user['full_name'] : 'Unknown';
        } catch (PDOException $e) {
            error_log("Get User Name Error: " . $e->getMessage());
            return 'Unknown';
        }
    }
}
