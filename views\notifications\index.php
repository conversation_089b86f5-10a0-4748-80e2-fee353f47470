<?php
/**
 * Notifications View
 * 
 * This file displays the user's notifications.
 */

// Set page title
$pageTitle = __('notifications');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 text-gradient mb-1"><?php echo __('notifications_center'); ?></h1>
        <p class="text-muted mb-0"><?php echo __('manage_your_notifications'); ?></p>
    </div>

    <div class="btn-group">
        <button type="button" class="btn btn-outline-primary" onclick="markAllAsRead()">
            <i class="fas fa-check-double me-2"></i><?php echo __('mark_all_read'); ?>
        </button>

        <button type="button" class="btn btn-outline-danger" onclick="clearAllNotifications()">
            <i class="fas fa-trash me-2"></i><?php echo __('clear_all'); ?>
        </button>
    </div>
</div>

<!-- Enhanced Notification Filters -->
<div class="card card-glass mb-4 fade-in-up">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-filter me-2"></i><?php echo __('filters'); ?>
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" action="<?php echo getBaseUrl(); ?>/notifications" class="row g-3">
            <div class="col-md-3">
                <label for="type" class="form-label"><?php echo __('type'); ?></label>
                <select class="form-select" id="type" name="type">
                    <option value=""><?php echo __('all_types'); ?></option>
                    <option value="maintenance" <?php echo ($type == 'maintenance') ? 'selected' : ''; ?>><?php echo __('maintenance'); ?></option>
                    <option value="ticket" <?php echo ($type == 'ticket') ? 'selected' : ''; ?>><?php echo __('ticket'); ?></option>
                    <option value="system" <?php echo ($type == 'system') ? 'selected' : ''; ?>><?php echo __('system'); ?></option>
                    <option value="warning" <?php echo ($type == 'warning') ? 'selected' : ''; ?>><?php echo __('warning'); ?></option>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="status" class="form-label"><?php echo __('status'); ?></label>
                <select class="form-select" id="status" name="status">
                    <option value=""><?php echo __('all_notifications'); ?></option>
                    <option value="unread" <?php echo ($status == 'unread') ? 'selected' : ''; ?>><?php echo __('unread'); ?></option>
                    <option value="read" <?php echo ($status == 'read') ? 'selected' : ''; ?>><?php echo __('read'); ?></option>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="date_from" class="form-label"><?php echo __('date_from'); ?></label>
                <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo $dateFrom ?? ''; ?>">
            </div>
            
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-2"></i><?php echo __('filter'); ?>
                    </button>
                </div>
            </div>

            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearFilters()">
                        <i class="fas fa-times me-1"></i><?php echo __('clear_filters'); ?>
                    </button>
                    <small class="text-muted">
                        <?php echo count($notifications); ?> <?php echo __('notifications_found'); ?>
                    </small>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Enhanced Notifications List -->
<div class="card fade-in-up" style="animation-delay: 0.2s;">
    <div class="card-body">
        <?php if (empty($notifications)): ?>
            <div class="text-center py-5">
                <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                <h5 class="text-muted"><?php echo __('no_notifications'); ?></h5>
                <p class="text-muted"><?php echo __('no_notifications_message'); ?></p>
            </div>
        <?php else: ?>
            <div class="list-group list-group-flush">
                <?php foreach ($notifications as $notification): ?>
                    <div class="list-group-item <?php echo $notification['is_read'] ? '' : 'list-group-item-primary'; ?>" 
                         data-notification-id="<?php echo $notification['id']; ?>">
                        <div class="d-flex w-100 justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-<?php echo getNotificationIcon($notification['type']); ?> me-2 text-<?php echo getNotificationColor($notification['type']); ?>"></i>
                                    <h6 class="mb-0"><?php echo htmlspecialchars($notification['title']); ?></h6>
                                    <?php if (!$notification['is_read']): ?>
                                        <span class="badge bg-primary ms-2"><?php echo __('new'); ?></span>
                                    <?php endif; ?>
                                </div>
                                
                                <p class="mb-2"><?php echo htmlspecialchars($notification['message']); ?></p>
                                
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    <?php 
                                    $createdAt = new DateTime($notification['created_at']);
                                    echo $createdAt->format('Y-m-d H:i');
                                    ?>
                                </small>
                            </div>
                            
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <?php if (!$notification['is_read']): ?>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="markAsRead(<?php echo $notification['id']; ?>)">
                                                <i class="fas fa-check me-2"></i><?php echo __('mark_as_read'); ?>
                                            </a>
                                        </li>
                                    <?php else: ?>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="markAsUnread(<?php echo $notification['id']; ?>)">
                                                <i class="fas fa-undo me-2"></i><?php echo __('mark_as_unread'); ?>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                    
                                    <?php if (!empty($notification['action_url'])): ?>
                                        <li>
                                            <a class="dropdown-item" href="<?php echo $notification['action_url']; ?>">
                                                <i class="fas fa-external-link-alt me-2"></i><?php echo __('view_details'); ?>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                    
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item text-danger" href="#" onclick="deleteNotification(<?php echo $notification['id']; ?>)">
                                            <i class="fas fa-trash me-2"></i><?php echo __('delete'); ?>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <nav class="mt-4">
                    <ul class="pagination justify-content-center">
                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                            <li class="page-item <?php echo $i == $currentPage ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&<?php echo http_build_query($_GET); ?>"><?php echo $i; ?></a>
                            </li>
                        <?php endfor; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<script>
function markAsRead(notificationId) {
    fetch('<?php echo getBaseUrl(); ?>/api/mark_notification_read', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            notification_id: notificationId,
            csrf_token: '<?php echo generateCSRFToken(); ?>'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('<?php echo __('error_occurred'); ?>');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('<?php echo __('error_occurred'); ?>');
    });
}

function markAsUnread(notificationId) {
    fetch('<?php echo getBaseUrl(); ?>/api/mark_notification_unread', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            notification_id: notificationId,
            csrf_token: '<?php echo generateCSRFToken(); ?>'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('<?php echo __('error_occurred'); ?>');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('<?php echo __('error_occurred'); ?>');
    });
}

function deleteNotification(notificationId) {
    if (confirm('<?php echo __('delete_notification_confirm'); ?>')) {
        fetch('<?php echo getBaseUrl(); ?>/api/delete_notification', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                notification_id: notificationId,
                csrf_token: '<?php echo generateCSRFToken(); ?>'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('<?php echo __('error_occurred'); ?>');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('<?php echo __('error_occurred'); ?>');
        });
    }
}

function markAllAsRead() {
    if (confirm('<?php echo __('mark_all_read_confirm'); ?>')) {
        fetch('<?php echo getBaseUrl(); ?>/api/mark_all_notifications_read', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                csrf_token: '<?php echo generateCSRFToken(); ?>'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('<?php echo __('error_occurred'); ?>');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('<?php echo __('error_occurred'); ?>');
        });
    }
}

function clearAllNotifications() {
    if (confirm('<?php echo __('clear_all_notifications_confirm'); ?>')) {
        fetch('<?php echo getBaseUrl(); ?>/api/clear_all_notifications', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                csrf_token: '<?php echo generateCSRFToken(); ?>'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('<?php echo __('error_occurred'); ?>');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('<?php echo __('error_occurred'); ?>');
        });
    }
}

function clearFilters() {
    window.location.href = '<?php echo getBaseUrl(); ?>/notifications';
}

// Auto-submit form when filters change
document.getElementById('type').addEventListener('change', function() {
    this.form.submit();
});

document.getElementById('status').addEventListener('change', function() {
    this.form.submit();
});
</script>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
