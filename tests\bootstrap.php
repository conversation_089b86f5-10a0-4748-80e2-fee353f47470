<?php
/**
 * Test Bootstrap
 * 
 * This file sets up the environment for running tests.
 */

// Define test environment
define('TESTING', true);

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include configuration
require_once __DIR__ . '/../config/database.php';

// Include functions
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/notifications.php';
require_once __DIR__ . '/../includes/security.php';
require_once __DIR__ . '/../includes/export.php';
require_once __DIR__ . '/../includes/email.php';

// Include models
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../models/Hospital.php';
require_once __DIR__ . '/../models/Department.php';
require_once __DIR__ . '/../models/Device.php';
require_once __DIR__ . '/../models/Maintenance.php';
require_once __DIR__ . '/../models/Ticket.php';

// Simple test framework
class TestFramework {
    private $tests = [];
    private $results = [
        'passed' => 0,
        'failed' => 0,
        'total' => 0
    ];
    
    /**
     * Add a test
     * 
     * @param string $name The test name
     * @param callable $callback The test callback
     * @return void
     */
    public function addTest($name, $callback) {
        $this->tests[] = [
            'name' => $name,
            'callback' => $callback
        ];
    }
    
    /**
     * Run all tests
     * 
     * @return array The test results
     */
    public function runTests() {
        echo "Running tests...\n\n";
        
        foreach ($this->tests as $test) {
            echo "Test: {$test['name']}... ";
            
            try {
                $result = call_user_func($test['callback']);
                
                if ($result === true) {
                    echo "PASSED\n";
                    $this->results['passed']++;
                } else {
                    echo "FAILED\n";
                    $this->results['failed']++;
                    
                    if (is_string($result)) {
                        echo "  Error: $result\n";
                    }
                }
            } catch (Exception $e) {
                echo "FAILED (Exception)\n";
                echo "  Error: {$e->getMessage()}\n";
                $this->results['failed']++;
            }
            
            $this->results['total']++;
        }
        
        echo "\nTest Results:\n";
        echo "  Total: {$this->results['total']}\n";
        echo "  Passed: {$this->results['passed']}\n";
        echo "  Failed: {$this->results['failed']}\n";
        
        return $this->results;
    }
    
    /**
     * Assert that a condition is true
     * 
     * @param bool $condition The condition to check
     * @param string $message The error message
     * @return bool|string True if passed, error message if failed
     */
    public function assertTrue($condition, $message = 'Assertion failed') {
        if ($condition === true) {
            return true;
        }
        
        return $message;
    }
    
    /**
     * Assert that a condition is false
     * 
     * @param bool $condition The condition to check
     * @param string $message The error message
     * @return bool|string True if passed, error message if failed
     */
    public function assertFalse($condition, $message = 'Assertion failed') {
        if ($condition === false) {
            return true;
        }
        
        return $message;
    }
    
    /**
     * Assert that two values are equal
     * 
     * @param mixed $expected The expected value
     * @param mixed $actual The actual value
     * @param string $message The error message
     * @return bool|string True if passed, error message if failed
     */
    public function assertEquals($expected, $actual, $message = 'Values are not equal') {
        if ($expected == $actual) {
            return true;
        }
        
        return $message . ": Expected '$expected', got '$actual'";
    }
    
    /**
     * Assert that two values are identical
     * 
     * @param mixed $expected The expected value
     * @param mixed $actual The actual value
     * @param string $message The error message
     * @return bool|string True if passed, error message if failed
     */
    public function assertSame($expected, $actual, $message = 'Values are not identical') {
        if ($expected === $actual) {
            return true;
        }
        
        return $message . ": Expected '$expected', got '$actual'";
    }
    
    /**
     * Assert that a value is not null
     * 
     * @param mixed $value The value to check
     * @param string $message The error message
     * @return bool|string True if passed, error message if failed
     */
    public function assertNotNull($value, $message = 'Value is null') {
        if ($value !== null) {
            return true;
        }
        
        return $message;
    }
    
    /**
     * Assert that a value is null
     * 
     * @param mixed $value The value to check
     * @param string $message The error message
     * @return bool|string True if passed, error message if failed
     */
    public function assertNull($value, $message = 'Value is not null') {
        if ($value === null) {
            return true;
        }
        
        return $message;
    }
    
    /**
     * Assert that a value is empty
     * 
     * @param mixed $value The value to check
     * @param string $message The error message
     * @return bool|string True if passed, error message if failed
     */
    public function assertEmpty($value, $message = 'Value is not empty') {
        if (empty($value)) {
            return true;
        }
        
        return $message;
    }
    
    /**
     * Assert that a value is not empty
     * 
     * @param mixed $value The value to check
     * @param string $message The error message
     * @return bool|string True if passed, error message if failed
     */
    public function assertNotEmpty($value, $message = 'Value is empty') {
        if (!empty($value)) {
            return true;
        }
        
        return $message;
    }
    
    /**
     * Assert that a value contains a substring
     * 
     * @param string $needle The substring to find
     * @param string $haystack The string to search in
     * @param string $message The error message
     * @return bool|string True if passed, error message if failed
     */
    public function assertContains($needle, $haystack, $message = 'String does not contain substring') {
        if (strpos($haystack, $needle) !== false) {
            return true;
        }
        
        return $message . ": '$needle' not found in '$haystack'";
    }
    
    /**
     * Assert that a value is an instance of a class
     * 
     * @param string $class The class name
     * @param object $object The object to check
     * @param string $message The error message
     * @return bool|string True if passed, error message if failed
     */
    public function assertInstanceOf($class, $object, $message = 'Object is not an instance of class') {
        if ($object instanceof $class) {
            return true;
        }
        
        return $message . ": Expected instance of '$class'";
    }
    
    /**
     * Assert that a file exists
     * 
     * @param string $path The file path
     * @param string $message The error message
     * @return bool|string True if passed, error message if failed
     */
    public function assertFileExists($path, $message = 'File does not exist') {
        if (file_exists($path)) {
            return true;
        }
        
        return $message . ": '$path'";
    }
}

// Create test framework instance
$testFramework = new TestFramework();

// Create a test database connection
try {
    // Use a test database if available
    $testDbname = $dbname . '_test';
    $testPdo = new PDO("mysql:host=$host;dbname=$testDbname;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false
    ]);
    
    // Use the test database connection
    $pdo = $testPdo;
    
    echo "Using test database: $testDbname\n\n";
} catch (PDOException $e) {
    // If test database doesn't exist, use the regular database
    echo "Test database not available, using regular database: $dbname\n\n";
}

// Initialize models with the database connection
$userModel = new User($pdo);
$hospitalModel = new Hospital($pdo);
$departmentModel = new Department($pdo);
$deviceModel = new Device($pdo);
$maintenanceModel = new Maintenance($pdo);
$ticketModel = new Ticket($pdo);
