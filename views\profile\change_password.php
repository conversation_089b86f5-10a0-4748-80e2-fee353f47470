<?php
/**
 * Change Password View
 * 
 * This file displays the form to change user password.
 */

// Set page title
$pageTitle = __('change_password');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3"><?php echo __('change_password'); ?></h1>
    
    <a href="<?php echo getBaseUrl(); ?>/profile" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_profile'); ?>
    </a>
</div>

<?php if (!empty($errors)): ?>
    <div class="alert alert-danger">
        <h6><?php echo __('please_fix_errors'); ?>:</h6>
        <ul class="mb-0">
            <?php foreach ($errors as $field => $error): ?>
                <li><?php echo htmlspecialchars($error); ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-key me-2"></i><?php echo __('change_password'); ?>
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?php echo getBaseUrl(); ?>/profile/change_password" id="changePasswordForm">
                    <div class="mb-3">
                        <label for="current_password" class="form-label"><?php echo __('current_password'); ?> <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="password" 
                                   class="form-control <?php echo isset($errors['current_password']) ? 'is-invalid' : ''; ?>" 
                                   id="current_password" 
                                   name="current_password" 
                                   required>
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('current_password')">
                                <i class="fas fa-eye" id="current_password_icon"></i>
                            </button>
                            <?php if (isset($errors['current_password'])): ?>
                                <div class="invalid-feedback"><?php echo $errors['current_password']; ?></div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="new_password" class="form-label"><?php echo __('new_password'); ?> <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="password" 
                                   class="form-control <?php echo isset($errors['new_password']) ? 'is-invalid' : ''; ?>" 
                                   id="new_password" 
                                   name="new_password" 
                                   minlength="6"
                                   required>
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('new_password')">
                                <i class="fas fa-eye" id="new_password_icon"></i>
                            </button>
                            <?php if (isset($errors['new_password'])): ?>
                                <div class="invalid-feedback"><?php echo $errors['new_password']; ?></div>
                            <?php endif; ?>
                        </div>
                        <div class="form-text"><?php echo __('password_requirements'); ?></div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label"><?php echo __('confirm_new_password'); ?> <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="password" 
                                   class="form-control <?php echo isset($errors['confirm_password']) ? 'is-invalid' : ''; ?>" 
                                   id="confirm_password" 
                                   name="confirm_password" 
                                   required>
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
                                <i class="fas fa-eye" id="confirm_password_icon"></i>
                            </button>
                            <?php if (isset($errors['confirm_password'])): ?>
                                <div class="invalid-feedback"><?php echo $errors['confirm_password']; ?></div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Password Strength Indicator -->
                    <div class="mb-3">
                        <label class="form-label"><?php echo __('password_strength'); ?></label>
                        <div class="progress" style="height: 5px;">
                            <div class="progress-bar" id="password-strength-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <small class="form-text" id="password-strength-text"><?php echo __('enter_password_to_check_strength'); ?></small>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="<?php echo getBaseUrl(); ?>/profile" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>
                        </a>
                        
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-key me-2"></i><?php echo __('change_password'); ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Security Tips -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-shield-alt me-2"></i><?php echo __('security_tips'); ?>
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <?php echo __('use_strong_password'); ?>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <?php echo __('include_numbers_symbols'); ?>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <?php echo __('avoid_common_passwords'); ?>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <?php echo __('dont_reuse_passwords'); ?>
                    </li>
                    <li>
                        <i class="fas fa-check text-success me-2"></i>
                        <?php echo __('change_password_regularly'); ?>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
// Toggle password visibility
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '_icon');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// Password strength checker
document.addEventListener('DOMContentLoaded', function() {
    const newPasswordField = document.getElementById('new_password');
    const confirmPasswordField = document.getElementById('confirm_password');
    const strengthBar = document.getElementById('password-strength-bar');
    const strengthText = document.getElementById('password-strength-text');
    
    newPasswordField.addEventListener('input', function() {
        const password = this.value;
        const strength = calculatePasswordStrength(password);
        
        updatePasswordStrengthDisplay(strength);
        validatePasswordMatch();
    });
    
    confirmPasswordField.addEventListener('input', function() {
        validatePasswordMatch();
    });
    
    function calculatePasswordStrength(password) {
        let score = 0;
        
        if (password.length >= 8) score += 25;
        if (password.match(/[a-z]/)) score += 25;
        if (password.match(/[A-Z]/)) score += 25;
        if (password.match(/[0-9]/)) score += 25;
        if (password.match(/[^a-zA-Z0-9]/)) score += 25;
        
        return Math.min(score, 100);
    }
    
    function updatePasswordStrengthDisplay(strength) {
        strengthBar.style.width = strength + '%';
        
        if (strength < 25) {
            strengthBar.className = 'progress-bar bg-danger';
            strengthText.textContent = '<?php echo __('weak_password'); ?>';
            strengthText.className = 'form-text text-danger';
        } else if (strength < 50) {
            strengthBar.className = 'progress-bar bg-warning';
            strengthText.textContent = '<?php echo __('fair_password'); ?>';
            strengthText.className = 'form-text text-warning';
        } else if (strength < 75) {
            strengthBar.className = 'progress-bar bg-info';
            strengthText.textContent = '<?php echo __('good_password'); ?>';
            strengthText.className = 'form-text text-info';
        } else {
            strengthBar.className = 'progress-bar bg-success';
            strengthText.textContent = '<?php echo __('strong_password'); ?>';
            strengthText.className = 'form-text text-success';
        }
    }
    
    function validatePasswordMatch() {
        const newPassword = newPasswordField.value;
        const confirmPassword = confirmPasswordField.value;
        
        if (confirmPassword && newPassword !== confirmPassword) {
            confirmPasswordField.classList.add('is-invalid');
            confirmPasswordField.classList.remove('is-valid');
        } else if (confirmPassword) {
            confirmPasswordField.classList.remove('is-invalid');
            confirmPasswordField.classList.add('is-valid');
        } else {
            confirmPasswordField.classList.remove('is-invalid', 'is-valid');
        }
    }
});
</script>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
