<?php
/**
 * Assign Ticket View
 * 
 * This file displays the form to assign a ticket to a user.
 */

// Set page title
$pageTitle = __('assign_ticket');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3"><?php echo __('assign_ticket'); ?></h1>
    
    <a href="<?php echo getBaseUrl(); ?>/tickets" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_tickets'); ?>
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <!-- Ticket Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-ticket-alt me-2"></i><?php echo __('ticket_information'); ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('ticket_id'); ?></label>
                            <div class="h5">#<?php echo str_pad($ticket['id'], 6, '0', STR_PAD_LEFT); ?></div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('title'); ?></label>
                            <div class="fw-bold"><?php echo htmlspecialchars($ticket['title']); ?></div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('device'); ?></label>
                            <div class="d-flex align-items-center">
                                <div class="device-icon me-2">
                                    <i class="fas fa-medical-kit text-primary"></i>
                                </div>
                                <div>
                                    <div class="fw-bold"><?php echo htmlspecialchars($ticket['device_name']); ?></div>
                                    <small class="text-muted"><?php echo htmlspecialchars($ticket['serial_number']); ?></small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('priority'); ?></label>
                            <div>
                                <span class="badge bg-<?php echo getTicketPriorityColor($ticket['priority']); ?> fs-6">
                                    <?php echo __($ticket['priority']); ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('current_status'); ?></label>
                            <div>
                                <span class="badge bg-<?php echo getTicketStatusColor($ticket['status']); ?> fs-6">
                                    <?php echo __($ticket['status']); ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('reported_by'); ?></label>
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-2">
                                    <div class="bg-info text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 32px; height: 32px; font-size: 0.8rem;">
                                        <?php echo strtoupper(substr($ticket['reported_by_name'], 0, 1)); ?>
                                    </div>
                                </div>
                                <div>
                                    <div class="fw-bold"><?php echo htmlspecialchars($ticket['reported_by_name']); ?></div>
                                    <small class="text-muted"><?php echo date('M d, Y', strtotime($ticket['created_at'])); ?></small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label text-muted"><?php echo __('description'); ?></label>
                    <div class="border rounded p-3 bg-light">
                        <?php echo nl2br(htmlspecialchars($ticket['description'])); ?>
                    </div>
                </div>
                
                <?php if ($ticket['assigned_to_name']): ?>
                    <div class="alert alert-info">
                        <h6 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i><?php echo __('currently_assigned'); ?>
                        </h6>
                        <p class="mb-0">
                            <?php echo __('ticket_currently_assigned_to'); ?> <strong><?php echo htmlspecialchars($ticket['assigned_to_name']); ?></strong>
                        </p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Assignment Form -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-plus me-2"></i><?php echo __('assign_to_technician'); ?>
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?php echo getBaseUrl(); ?>/tickets/assign/<?php echo $ticket['id']; ?>">
                    <div class="mb-4">
                        <label for="assigned_to" class="form-label"><?php echo __('select_assignee'); ?> <span class="text-danger">*</span></label>
                        <select class="form-select" id="assigned_to" name="assigned_to" required>
                            <option value=""><?php echo __('select_technician_or_engineer'); ?></option>
                            
                            <?php if (!empty($assignees)): ?>
                                <?php
                                // Group assignees by role
                                $engineers = array_filter($assignees, function($user) { return $user['role'] === 'engineer'; });
                                $technicians = array_filter($assignees, function($user) { return $user['role'] === 'technician'; });
                                ?>
                                
                                <?php if (!empty($engineers)): ?>
                                    <optgroup label="<?php echo __('engineers'); ?>">
                                        <?php foreach ($engineers as $engineer): ?>
                                            <option value="<?php echo $engineer['id']; ?>" <?php echo ($ticket['assigned_to'] == $engineer['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($engineer['full_name']); ?>
                                                <?php if ($engineer['email']): ?>
                                                    (<?php echo htmlspecialchars($engineer['email']); ?>)
                                                <?php endif; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </optgroup>
                                <?php endif; ?>
                                
                                <?php if (!empty($technicians)): ?>
                                    <optgroup label="<?php echo __('technicians'); ?>">
                                        <?php foreach ($technicians as $technician): ?>
                                            <option value="<?php echo $technician['id']; ?>" <?php echo ($ticket['assigned_to'] == $technician['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($technician['full_name']); ?>
                                                <?php if ($technician['email']): ?>
                                                    (<?php echo htmlspecialchars($technician['email']); ?>)
                                                <?php endif; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </optgroup>
                                <?php endif; ?>
                            <?php else: ?>
                                <option value="" disabled><?php echo __('no_assignees_available'); ?></option>
                            <?php endif; ?>
                        </select>
                        <div class="form-text"><?php echo __('select_qualified_person'); ?></div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="assignment_notes" class="form-label"><?php echo __('assignment_notes'); ?></label>
                        <textarea class="form-control" 
                                  id="assignment_notes" 
                                  name="assignment_notes" 
                                  rows="3" 
                                  placeholder="<?php echo __('optional_notes_for_assignee'); ?>"></textarea>
                        <div class="form-text"><?php echo __('provide_additional_context'); ?></div>
                    </div>
                    
                    <div class="form-check mb-4">
                        <input class="form-check-input" type="checkbox" id="notify_assignee" name="notify_assignee" checked>
                        <label class="form-check-label" for="notify_assignee">
                            <?php echo __('send_notification_to_assignee'); ?>
                        </label>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="<?php echo getBaseUrl(); ?>/tickets/view/<?php echo $ticket['id']; ?>" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>
                        </a>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-user-plus me-2"></i><?php echo __('assign_ticket'); ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Assignment History -->
        <?php if (!empty($assignmentHistory ?? [])): ?>
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i><?php echo __('assignment_history'); ?>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <?php foreach ($assignmentHistory as $assignment): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-info">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="timeline-content">
                                    <div class="d-flex justify-content-between align-items-start mb-1">
                                        <div>
                                            <strong><?php echo htmlspecialchars($assignment['assigned_to_name']); ?></strong>
                                            <small class="text-muted">
                                                <?php echo __('assigned_by'); ?> <?php echo htmlspecialchars($assignment['assigned_by_name']); ?>
                                            </small>
                                        </div>
                                        <small class="text-muted"><?php echo date('M d, Y H:i', strtotime($assignment['created_at'])); ?></small>
                                    </div>
                                    
                                    <?php if ($assignment['notes']): ?>
                                        <div class="small text-muted">
                                            <?php echo nl2br(htmlspecialchars($assignment['notes'])); ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -19px;
    top: 30px;
    width: 2px;
    height: calc(100% + 10px);
    background-color: #dee2e6;
}

.timeline-marker {
    position: absolute;
    left: -30px;
    top: 0;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
}

.timeline-content {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border-left: 3px solid #dee2e6;
}
</style>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
