<?php
/**
 * Edit User View
 * 
 * This file displays the form to edit a user.
 */

// Set page title
$pageTitle = __('edit_user');
$pageSubtitle = __('update_user_information');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 text-gradient mb-1"><?php echo __('edit_user'); ?></h1>
        <p class="text-muted mb-0"><?php echo htmlspecialchars($user['full_name']); ?></p>
    </div>
    
    <div class="btn-group">
        <a href="<?php echo getBaseUrl(); ?>/users" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_list'); ?>
        </a>
        <a href="<?php echo getBaseUrl(); ?>/users/view/<?php echo $user['id']; ?>" class="btn btn-primary">
            <i class="fas fa-eye me-2"></i><?php echo __('view'); ?>
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8 col-md-10 mx-auto">
        <div class="card fade-in-up">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-edit me-2"></i><?php echo __('user_information'); ?>
                </h5>
            </div>
            <div class="card-body">
                <form action="<?php echo getBaseUrl(); ?>/users/update" method="post" id="userForm">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="id" value="<?php echo $user['id']; ?>">
                    
                    <?php if (!empty($errors['general'])): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo $errors['general']; ?>
                        </div>
                    <?php endif; ?>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="full_name" class="form-label"><?php echo __('full_name'); ?> <span class="text-danger">*</span></label>
                                <input type="text" class="form-control <?php echo isset($errors['full_name']) ? 'is-invalid' : ''; ?>" 
                                       id="full_name" name="full_name" value="<?php echo htmlspecialchars($userData['full_name'] ?? $user['full_name']); ?>" required>
                                <?php if (isset($errors['full_name'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['full_name']; ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label"><?php echo __('username'); ?> <span class="text-danger">*</span></label>
                                <input type="text" class="form-control <?php echo isset($errors['username']) ? 'is-invalid' : ''; ?>" 
                                       id="username" name="username" value="<?php echo htmlspecialchars($userData['username'] ?? $user['username']); ?>" required>
                                <?php if (isset($errors['username'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['username']; ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label"><?php echo __('email'); ?> <span class="text-danger">*</span></label>
                                <input type="email" class="form-control <?php echo isset($errors['email']) ? 'is-invalid' : ''; ?>" 
                                       id="email" name="email" value="<?php echo htmlspecialchars($userData['email'] ?? $user['email']); ?>" required>
                                <?php if (isset($errors['email'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['email']; ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label"><?php echo __('new_password'); ?></label>
                                <div class="input-group">
                                    <input type="password" class="form-control <?php echo isset($errors['password']) ? 'is-invalid' : ''; ?>" 
                                           id="password" name="password" placeholder="<?php echo __('leave_blank_to_keep_current'); ?>">
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                                        <i class="fas fa-eye" id="passwordToggle"></i>
                                    </button>
                                </div>
                                <?php if (isset($errors['password'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['password']; ?></div>
                                <?php endif; ?>
                                <div class="form-text"><?php echo __('leave_blank_to_keep_current_password'); ?></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="role" class="form-label"><?php echo __('role'); ?> <span class="text-danger">*</span></label>
                                <select class="form-select <?php echo isset($errors['role']) ? 'is-invalid' : ''; ?>" id="role" name="role" required>
                                    <option value=""><?php echo __('select_role'); ?></option>
                                    <option value="admin" <?php echo ($userData['role'] ?? $user['role']) === 'admin' ? 'selected' : ''; ?>><?php echo __('admin'); ?></option>
                                    <option value="manager" <?php echo ($userData['role'] ?? $user['role']) === 'manager' ? 'selected' : ''; ?>><?php echo __('manager'); ?></option>
                                    <option value="technician" <?php echo ($userData['role'] ?? $user['role']) === 'technician' ? 'selected' : ''; ?>><?php echo __('technician'); ?></option>
                                    <option value="user" <?php echo ($userData['role'] ?? $user['role']) === 'user' ? 'selected' : ''; ?>><?php echo __('user'); ?></option>
                                </select>
                                <?php if (isset($errors['role'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['role']; ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="hospital_id" class="form-label"><?php echo __('hospital'); ?></label>
                                <select class="form-select" id="hospital_id" name="hospital_id">
                                    <option value=""><?php echo __('select_hospital'); ?></option>
                                    <?php foreach ($hospitals as $hospital): ?>
                                        <option value="<?php echo $hospital['id']; ?>" 
                                                <?php echo ($userData['hospital_id'] ?? $user['hospital_id']) == $hospital['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($hospital['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="language" class="form-label"><?php echo __('language'); ?></label>
                                <select class="form-select" id="language" name="language">
                                    <option value="en" <?php echo ($userData['language'] ?? $user['language']) === 'en' ? 'selected' : ''; ?>>English</option>
                                    <option value="ar" <?php echo ($userData['language'] ?? $user['language']) === 'ar' ? 'selected' : ''; ?>>العربية</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status" class="form-label"><?php echo __('status'); ?></label>
                                <select class="form-select" id="status" name="status">
                                    <option value="active" <?php echo ($userData['status'] ?? $user['status']) === 'active' ? 'selected' : ''; ?>><?php echo __('active'); ?></option>
                                    <option value="inactive" <?php echo ($userData['status'] ?? $user['status']) === 'inactive' ? 'selected' : ''; ?>><?php echo __('inactive'); ?></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center pt-3 border-top">
                        <a href="<?php echo getBaseUrl(); ?>/users" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>
                        </a>
                        <div class="d-flex gap-2">
                            <button type="reset" class="btn btn-outline-secondary">
                                <i class="fas fa-undo me-2"></i><?php echo __('reset'); ?>
                            </button>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <span class="btn-text">
                                    <i class="fas fa-save me-2"></i><?php echo __('update_user'); ?>
                                </span>
                                <span class="loading-spinner d-none"></span>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Toggle password visibility
function togglePassword(inputId) {
    const passwordInput = document.getElementById(inputId);
    const passwordToggle = document.getElementById(inputId + 'Toggle');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        passwordToggle.classList.remove('fa-eye');
        passwordToggle.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        passwordToggle.classList.remove('fa-eye-slash');
        passwordToggle.classList.add('fa-eye');
    }
}

// Form validation and submission
document.getElementById('userForm').addEventListener('submit', function(e) {
    const submitBtn = document.getElementById('submitBtn');
    const btnText = submitBtn.querySelector('.btn-text');
    const spinner = submitBtn.querySelector('.loading-spinner');
    
    // Show loading state
    btnText.classList.add('d-none');
    spinner.classList.remove('d-none');
    submitBtn.disabled = true;
});
</script>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
