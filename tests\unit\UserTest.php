<?php
/**
 * User Model Test
 * 
 * This file contains tests for the User model.
 */

// Include bootstrap
require_once __DIR__ . '/../bootstrap.php';

// Test user data
$testUser = [
    'username' => 'testuser_' . time(),
    'password' => 'Test@123',
    'email' => 'test_' . time() . '@example.com',
    'full_name' => 'Test User',
    'role' => 'staff',
    'hospital_id' => 1,
    'language' => 'en'
];

// Test: Create User
$testFramework->addTest('Create User', function() use ($testFramework, $userModel, $testUser) {
    // Create the user
    $userId = $userModel->create($testUser);
    
    // Check if user was created
    if (!$testFramework->assertTrue($userId > 0, 'Failed to create user')) {
        return false;
    }
    
    // Store user ID for other tests
    $GLOBALS['test_user_id'] = $userId;
    
    return true;
});

// Test: Get User by ID
$testFramework->addTest('Get User by ID', function() use ($testFramework, $userModel, $testUser) {
    // Get the user ID from the previous test
    $userId = $GLOBALS['test_user_id'] ?? null;
    
    // Skip if user ID is not available
    if (!$userId) {
        return 'User ID not available';
    }
    
    // Get the user
    $user = $userModel->getById($userId);
    
    // Check if user was found
    if (!$testFramework->assertNotEmpty($user, 'User not found')) {
        return false;
    }
    
    // Check user data
    if (!$testFramework->assertEquals($testUser['username'], $user['username'], 'Username does not match')) {
        return false;
    }
    
    if (!$testFramework->assertEquals($testUser['email'], $user['email'], 'Email does not match')) {
        return false;
    }
    
    if (!$testFramework->assertEquals($testUser['full_name'], $user['full_name'], 'Full name does not match')) {
        return false;
    }
    
    if (!$testFramework->assertEquals($testUser['role'], $user['role'], 'Role does not match')) {
        return false;
    }
    
    return true;
});

// Test: Get User by Username
$testFramework->addTest('Get User by Username', function() use ($testFramework, $userModel, $testUser) {
    // Get the user
    $user = $userModel->getByUsername($testUser['username']);
    
    // Check if user was found
    if (!$testFramework->assertNotEmpty($user, 'User not found')) {
        return false;
    }
    
    // Check user data
    if (!$testFramework->assertEquals($testUser['email'], $user['email'], 'Email does not match')) {
        return false;
    }
    
    return true;
});

// Test: Get User by Email
$testFramework->addTest('Get User by Email', function() use ($testFramework, $userModel, $testUser) {
    // Get the user
    $user = $userModel->getByEmail($testUser['email']);
    
    // Check if user was found
    if (!$testFramework->assertNotEmpty($user, 'User not found')) {
        return false;
    }
    
    // Check user data
    if (!$testFramework->assertEquals($testUser['username'], $user['username'], 'Username does not match')) {
        return false;
    }
    
    return true;
});

// Test: Update User
$testFramework->addTest('Update User', function() use ($testFramework, $userModel, $testUser) {
    // Get the user ID from the previous test
    $userId = $GLOBALS['test_user_id'] ?? null;
    
    // Skip if user ID is not available
    if (!$userId) {
        return 'User ID not available';
    }
    
    // Update user data
    $updatedData = [
        'email' => 'updated_' . time() . '@example.com',
        'full_name' => 'Updated User',
        'role' => 'staff',
        'hospital_id' => 1,
        'language' => 'en'
    ];
    
    // Update the user
    $result = $userModel->update($userId, $updatedData);
    
    // Check if update was successful
    if (!$testFramework->assertTrue($result, 'Failed to update user')) {
        return false;
    }
    
    // Get the updated user
    $user = $userModel->getById($userId);
    
    // Check if user was found
    if (!$testFramework->assertNotEmpty($user, 'User not found')) {
        return false;
    }
    
    // Check updated user data
    if (!$testFramework->assertEquals($updatedData['email'], $user['email'], 'Email does not match')) {
        return false;
    }
    
    if (!$testFramework->assertEquals($updatedData['full_name'], $user['full_name'], 'Full name does not match')) {
        return false;
    }
    
    return true;
});

// Test: Get Users by Role
$testFramework->addTest('Get Users by Role', function() use ($testFramework, $userModel, $testUser) {
    // Get users by role
    $users = $userModel->getByRole('staff');
    
    // Check if users were found
    if (!$testFramework->assertNotEmpty($users, 'No users found')) {
        return false;
    }
    
    // Check if the test user is in the list
    $found = false;
    foreach ($users as $user) {
        if ($user['username'] === $testUser['username']) {
            $found = true;
            break;
        }
    }
    
    if (!$testFramework->assertTrue($found, 'Test user not found in users by role')) {
        return false;
    }
    
    return true;
});

// Test: Count Users
$testFramework->addTest('Count Users', function() use ($testFramework, $userModel) {
    // Count all users
    $count = $userModel->count();
    
    // Check if count is greater than 0
    if (!$testFramework->assertTrue($count > 0, 'User count is 0')) {
        return false;
    }
    
    return true;
});

// Test: Delete User
$testFramework->addTest('Delete User', function() use ($testFramework, $userModel) {
    // Get the user ID from the previous test
    $userId = $GLOBALS['test_user_id'] ?? null;
    
    // Skip if user ID is not available
    if (!$userId) {
        return 'User ID not available';
    }
    
    // Delete the user
    $result = $userModel->delete($userId);
    
    // Check if delete was successful
    if (!$testFramework->assertTrue($result, 'Failed to delete user')) {
        return false;
    }
    
    // Try to get the deleted user
    $user = $userModel->getById($userId);
    
    // Check if user was not found
    if (!$testFramework->assertEmpty($user, 'User still exists after deletion')) {
        return false;
    }
    
    return true;
});

// Run the tests
$testFramework->runTests();
