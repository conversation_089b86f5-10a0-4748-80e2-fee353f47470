<?php
/**
 * View Maintenance Schedule View
 * 
 * This file displays the details of a maintenance schedule.
 */

// Set page title
$pageTitle = __('view_maintenance_schedule');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3"><?php echo __('maintenance_schedule'); ?></h1>
    
    <div class="btn-group">
        <a href="<?php echo getBaseUrl(); ?>/maintenance" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_maintenance'); ?>
        </a>
        
        <?php if (hasPermission('manage_maintenance')): ?>
            <a href="<?php echo getBaseUrl(); ?>/maintenance/edit_schedule/<?php echo $schedule['id']; ?>" class="btn btn-warning">
                <i class="fas fa-edit me-2"></i><?php echo __('edit_schedule'); ?>
            </a>
            
            <a href="<?php echo getBaseUrl(); ?>/maintenance/create_log?schedule_id=<?php echo $schedule['id']; ?>" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i><?php echo __('create_log'); ?>
            </a>
        <?php endif; ?>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <!-- Schedule Details -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-alt me-2"></i><?php echo __('schedule_details'); ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('title'); ?></label>
                            <div class="h5"><?php echo htmlspecialchars($schedule['title']); ?></div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('device'); ?></label>
                            <div class="d-flex align-items-center">
                                <div class="device-icon me-2">
                                    <i class="fas fa-medical-kit text-primary fa-lg"></i>
                                </div>
                                <div>
                                    <div class="fw-bold"><?php echo htmlspecialchars($device['name']); ?></div>
                                    <small class="text-muted"><?php echo htmlspecialchars($device['serial_number']); ?></small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('location'); ?></label>
                            <div class="fw-bold">
                                <?php echo htmlspecialchars($device['hospital_name']); ?> - <?php echo htmlspecialchars($device['department_name']); ?>
                            </div>
                            <?php if ($device['location']): ?>
                                <small class="text-muted"><?php echo htmlspecialchars($device['location']); ?></small>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('scheduled_date'); ?></label>
                            <div class="h5 text-primary"><?php echo date('F d, Y', strtotime($schedule['scheduled_date'])); ?></div>
                            <small class="text-muted">
                                <?php
                                $daysUntil = (strtotime($schedule['scheduled_date']) - time()) / (60 * 60 * 24);
                                if ($daysUntil > 0) {
                                    echo __('in_days', [ceil($daysUntil)]);
                                } elseif ($daysUntil < 0) {
                                    echo __('overdue_by_days', [abs(floor($daysUntil))]);
                                } else {
                                    echo __('due_today');
                                }
                                ?>
                            </small>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('frequency'); ?></label>
                            <div class="fw-bold">
                                <span class="badge bg-info"><?php echo __($schedule['frequency']); ?></span>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('status'); ?></label>
                            <div>
                                <span class="badge bg-<?php echo getMaintenanceStatusColor($schedule['status']); ?> fs-6">
                                    <?php echo __($schedule['status']); ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <?php if ($schedule['description']): ?>
                    <div class="mb-3">
                        <label class="form-label text-muted"><?php echo __('description'); ?></label>
                        <div class="border rounded p-3 bg-light">
                            <?php echo nl2br(htmlspecialchars($schedule['description'])); ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('created_by'); ?></label>
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-2">
                                    <div class="bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 32px; height: 32px; font-size: 0.8rem;">
                                        <?php echo strtoupper(substr($schedule['created_by_name'], 0, 1)); ?>
                                    </div>
                                </div>
                                <div>
                                    <div class="fw-bold"><?php echo htmlspecialchars($schedule['created_by_name']); ?></div>
                                    <small class="text-muted"><?php echo date('M d, Y', strtotime($schedule['created_at'])); ?></small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('last_updated'); ?></label>
                            <div class="fw-bold"><?php echo date('M d, Y H:i', strtotime($schedule['updated_at'])); ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Maintenance Logs -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clipboard-list me-2"></i><?php echo __('maintenance_logs'); ?>
                    <span class="badge bg-secondary ms-2"><?php echo count($logs); ?></span>
                </h5>
                
                <?php if (hasPermission('manage_maintenance')): ?>
                    <a href="<?php echo getBaseUrl(); ?>/maintenance/create_log?schedule_id=<?php echo $schedule['id']; ?>" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus me-1"></i><?php echo __('add_log'); ?>
                    </a>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <?php if (empty($logs)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted"><?php echo __('no_logs_yet'); ?></h6>
                        <p class="text-muted"><?php echo __('no_maintenance_performed'); ?></p>
                        
                        <?php if (hasPermission('manage_maintenance')): ?>
                            <a href="<?php echo getBaseUrl(); ?>/maintenance/create_log?schedule_id=<?php echo $schedule['id']; ?>" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i><?php echo __('create_first_log'); ?>
                            </a>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="timeline">
                        <?php foreach ($logs as $index => $log): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-<?php echo getMaintenanceStatusColor($log['status']); ?>">
                                    <i class="fas fa-<?php echo getMaintenanceStatusIcon($log['status']); ?>"></i>
                                </div>
                                <div class="timeline-content">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <div>
                                            <h6 class="mb-1">
                                                <span class="badge bg-<?php echo getMaintenanceStatusColor($log['status']); ?>">
                                                    <?php echo __($log['status']); ?>
                                                </span>
                                                <?php echo date('F d, Y', strtotime($log['performed_date'])); ?>
                                            </h6>
                                            <small class="text-muted">
                                                <?php echo __('performed_by'); ?> <?php echo htmlspecialchars($log['performed_by_name']); ?>
                                                • <?php echo date('H:i', strtotime($log['created_at'])); ?>
                                            </small>
                                        </div>
                                        
                                        <?php if (hasPermission('manage_maintenance')): ?>
                                            <div class="btn-group btn-group-sm">
                                                <a href="<?php echo getBaseUrl(); ?>/maintenance/view_log/<?php echo $log['id']; ?>" 
                                                   class="btn btn-outline-primary" 
                                                   title="<?php echo __('view_log'); ?>">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?php echo getBaseUrl(); ?>/maintenance/edit_log/<?php echo $log['id']; ?>" 
                                                   class="btn btn-outline-warning" 
                                                   title="<?php echo __('edit_log'); ?>">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <?php if ($log['notes']): ?>
                                        <div class="border rounded p-2 bg-light">
                                            <?php echo nl2br(htmlspecialchars($log['notes'])); ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- Quick Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i><?php echo __('quick_actions'); ?>
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <?php if (hasPermission('manage_maintenance')): ?>
                        <a href="<?php echo getBaseUrl(); ?>/maintenance/create_log?schedule_id=<?php echo $schedule['id']; ?>" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i><?php echo __('record_maintenance'); ?>
                        </a>
                        
                        <a href="<?php echo getBaseUrl(); ?>/maintenance/edit_schedule/<?php echo $schedule['id']; ?>" class="btn btn-warning">
                            <i class="fas fa-edit me-2"></i><?php echo __('edit_schedule'); ?>
                        </a>
                        
                        <a href="<?php echo getBaseUrl(); ?>/maintenance/delete_schedule/<?php echo $schedule['id']; ?>" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i><?php echo __('delete_schedule'); ?>
                        </a>
                    <?php endif; ?>
                    
                    <a href="<?php echo getBaseUrl(); ?>/devices/view/<?php echo $device['id']; ?>" class="btn btn-outline-info">
                        <i class="fas fa-medical-kit me-2"></i><?php echo __('view_device'); ?>
                    </a>
                    
                    <a href="<?php echo getBaseUrl(); ?>/maintenance?device_id=<?php echo $device['id']; ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-list me-2"></i><?php echo __('all_device_schedules'); ?>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Schedule Statistics -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i><?php echo __('statistics'); ?>
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <div class="h4 text-primary mb-0"><?php echo count($logs); ?></div>
                            <small class="text-muted"><?php echo __('total_logs'); ?></small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="h4 text-success mb-0">
                            <?php echo count(array_filter($logs, function($log) { return $log['status'] === 'completed'; })); ?>
                        </div>
                        <small class="text-muted"><?php echo __('completed'); ?></small>
                    </div>
                </div>
                
                <hr>
                
                <div class="small">
                    <div class="d-flex justify-content-between mb-1">
                        <span><?php echo __('next_due'); ?>:</span>
                        <span class="fw-bold">
                            <?php
                            if ($schedule['frequency'] !== 'once') {
                                // Calculate next due date based on frequency
                                $nextDue = calculateNextMaintenanceDate($schedule['scheduled_date'], $schedule['frequency']);
                                echo date('M d, Y', strtotime($nextDue));
                            } else {
                                echo __('one_time_only');
                            }
                            ?>
                        </span>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <span><?php echo __('last_performed'); ?>:</span>
                        <span class="fw-bold">
                            <?php
                            if (!empty($logs)) {
                                $lastLog = $logs[0]; // Assuming logs are ordered by date desc
                                echo date('M d, Y', strtotime($lastLog['performed_date']));
                            } else {
                                echo __('never');
                            }
                            ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -19px;
    top: 30px;
    width: 2px;
    height: calc(100% + 10px);
    background-color: #dee2e6;
}

.timeline-marker {
    position: absolute;
    left: -30px;
    top: 0;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
}

.timeline-content {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border-left: 3px solid #dee2e6;
}
</style>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
