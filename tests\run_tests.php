<?php
/**
 * Run Tests
 * 
 * This script runs all tests in the tests directory.
 */

// Include bootstrap
require_once __DIR__ . '/bootstrap.php';

// Get test type from command line arguments
$testType = $argv[1] ?? 'all';

// Get specific test file from command line arguments
$testFile = $argv[2] ?? null;

// Define test directories
$testDirs = [
    'unit' => __DIR__ . '/unit',
    'integration' => __DIR__ . '/integration'
];

// Function to run tests in a directory
function runTestsInDirectory($dir, $testFile = null) {
    echo "Running tests in $dir...\n\n";
    
    // Get all PHP files in the directory
    $files = glob("$dir/*.php");
    
    // Filter by specific test file if provided
    if ($testFile) {
        $files = array_filter($files, function($file) use ($testFile) {
            return basename($file) === $testFile || basename($file, '.php') === $testFile;
        });
        
        if (empty($files)) {
            echo "Test file not found: $testFile\n";
            return;
        }
    }
    
    // Run each test file
    foreach ($files as $file) {
        echo "Running test file: " . basename($file) . "\n";
        echo "----------------------------------------\n";
        
        // Include the test file
        include $file;
        
        echo "\n";
    }
}

// Run tests based on type
switch ($testType) {
    case 'unit':
        runTestsInDirectory($testDirs['unit'], $testFile);
        break;
    
    case 'integration':
        runTestsInDirectory($testDirs['integration'], $testFile);
        break;
    
    case 'all':
    default:
        // Run unit tests
        runTestsInDirectory($testDirs['unit'], $testFile);
        
        echo "\n";
        
        // Run integration tests
        runTestsInDirectory($testDirs['integration'], $testFile);
        break;
}

echo "All tests completed.\n";
