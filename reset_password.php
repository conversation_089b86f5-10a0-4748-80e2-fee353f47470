<?php
/**
 * Reset Password Page
 * 
 * This file handles password reset functionality.
 */

// Start session
session_start();

// Set default timezone
date_default_timezone_set('UTC');

// Load configuration
require_once 'config/database.php';

// Load includes
require_once 'includes/functions.php';
require_once 'includes/auth.php';
require_once 'includes/security.php';
require_once 'includes/email.php';

// Check if user is already logged in
if (isLoggedIn()) {
    redirect('index.php');
}

// Handle language switching
if (isset($_GET['lang']) && in_array($_GET['lang'], ['en', 'ar'])) {
    setLanguage($_GET['lang']);
    redirect('reset_password.php');
}

$error = '';
$success = '';
$step = $_GET['step'] ?? 'request';
$token = $_GET['token'] ?? '';

// Handle password reset request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $step === 'request') {
    // Validate CSRF token
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = __('invalid_csrf_token');
    } else {
        $email = $_POST['email'] ?? '';
        
        if (empty($email)) {
            $error = __('required_field');
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error = __('invalid_email');
        } else {
            // Check if user exists
            $stmt = $pdo->prepare("SELECT id, username, full_name FROM users WHERE email = ?");
            $stmt->execute([$email]);
            $user = $stmt->fetch();
            
            if ($user) {
                // Generate reset token
                $resetToken = bin2hex(random_bytes(32));
                $expires = date('Y-m-d H:i:s', time() + 3600); // 1 hour
                
                // Store reset token
                $stmt = $pdo->prepare("
                    INSERT INTO password_resets (user_id, token, expires_at)
                    VALUES (?, ?, ?)
                    ON DUPLICATE KEY UPDATE
                    token = VALUES(token),
                    expires_at = VALUES(expires_at),
                    created_at = NOW()
                ");
                
                $stmt->execute([$user['id'], $resetToken, $expires]);
                
                // Send reset email
                $resetLink = getBaseUrl() . '/reset_password.php?step=reset&token=' . $resetToken;
                
                $emailSent = sendPasswordResetEmail(
                    $email,
                    $user['full_name'],
                    $resetLink
                );
                
                if ($emailSent) {
                    $success = __('reset_email_sent');
                } else {
                    $error = __('email_send_failed');
                }
            } else {
                // Don't reveal if email exists or not
                $success = __('reset_email_sent');
            }
        }
    }
}

// Handle password reset
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $step === 'reset') {
    // Validate CSRF token
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = __('invalid_csrf_token');
    } else {
        $password = $_POST['password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        
        if (empty($password) || empty($confirmPassword)) {
            $error = __('required_field');
        } elseif ($password !== $confirmPassword) {
            $error = __('passwords_not_match');
        } elseif (strlen($password) < 8) {
            $error = __('password_too_short');
        } else {
            // Verify reset token
            $stmt = $pdo->prepare("
                SELECT pr.user_id, u.username
                FROM password_resets pr
                JOIN users u ON pr.user_id = u.id
                WHERE pr.token = ? AND pr.expires_at > NOW()
            ");
            
            $stmt->execute([$token]);
            $resetData = $stmt->fetch();
            
            if ($resetData) {
                // Update password
                $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                
                $stmt = $pdo->prepare("UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?");
                $result = $stmt->execute([$hashedPassword, $resetData['user_id']]);
                
                if ($result) {
                    // Delete reset token
                    $stmt = $pdo->prepare("DELETE FROM password_resets WHERE user_id = ?");
                    $stmt->execute([$resetData['user_id']]);
                    
                    // Log the action
                    logSecurityEvent('password_reset', $resetData['username'], getClientIp());
                    
                    $success = __('password_reset_success');
                    $step = 'success';
                } else {
                    $error = __('password_reset_failed');
                }
            } else {
                $error = __('invalid_reset_token');
            }
        }
    }
}

// Verify token for reset step
if ($step === 'reset' && !empty($token)) {
    $stmt = $pdo->prepare("
        SELECT user_id
        FROM password_resets
        WHERE token = ? AND expires_at > NOW()
    ");
    
    $stmt->execute([$token]);
    $validToken = $stmt->fetch();
    
    if (!$validToken) {
        $error = __('invalid_reset_token');
        $step = 'request';
    }
}

// Get the current language
$currentLanguage = getCurrentLanguage();
?>

<!DOCTYPE html>
<html lang="<?php echo $currentLanguage; ?>" dir="<?php echo $currentLanguage === 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo __('reset_password'); ?> - <?php echo __('medical_device_management'); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="<?php echo getBaseUrl(); ?>/assets/css/style.css" rel="stylesheet">
    
    <?php if ($currentLanguage === 'ar'): ?>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
    </style>
    <?php endif; ?>
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="card shadow-sm mt-5">
                    <div class="card-header bg-primary text-white text-center">
                        <h4 class="mb-0">
                            <i class="fas fa-key me-2"></i>
                            <?php echo __('reset_password'); ?>
                        </h4>
                    </div>
                    <div class="card-body">
                        <!-- Language Switcher -->
                        <div class="text-center mb-3">
                            <div class="btn-group btn-group-sm">
                                <a href="?lang=en" class="btn btn-outline-secondary <?php echo $currentLanguage === 'en' ? 'active' : ''; ?>">English</a>
                                <a href="?lang=ar" class="btn btn-outline-secondary <?php echo $currentLanguage === 'ar' ? 'active' : ''; ?>">العربية</a>
                            </div>
                        </div>
                        
                        <?php if (!empty($error)): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo $error; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($success)): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo $success; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($step === 'request'): ?>
                            <!-- Request Reset Form -->
                            <form method="POST" action="reset_password.php?step=request">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                
                                <div class="mb-3">
                                    <label for="email" class="form-label"><?php echo __('email'); ?></label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-envelope"></i>
                                        </span>
                                        <input type="email" class="form-control" id="email" name="email" required>
                                    </div>
                                    <div class="form-text"><?php echo __('reset_email_help'); ?></div>
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane me-2"></i>
                                        <?php echo __('send_reset_link'); ?>
                                    </button>
                                </div>
                            </form>
                            
                        <?php elseif ($step === 'reset'): ?>
                            <!-- Reset Password Form -->
                            <form method="POST" action="reset_password.php?step=reset&token=<?php echo htmlspecialchars($token); ?>">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label"><?php echo __('new_password'); ?></label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-lock"></i>
                                        </span>
                                        <input type="password" class="form-control" id="password" name="password" required minlength="8">
                                    </div>
                                    <div class="form-text"><?php echo __('password_requirements'); ?></div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label"><?php echo __('confirm_password'); ?></label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-lock"></i>
                                        </span>
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required minlength="8">
                                    </div>
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-key me-2"></i>
                                        <?php echo __('reset_password'); ?>
                                    </button>
                                </div>
                            </form>
                            
                        <?php elseif ($step === 'success'): ?>
                            <!-- Success Message -->
                            <div class="text-center">
                                <i class="fas fa-check-circle text-success" style="font-size: 3rem;"></i>
                                <h5 class="mt-3"><?php echo __('password_reset_complete'); ?></h5>
                                <p class="text-muted"><?php echo __('password_reset_complete_message'); ?></p>
                                
                                <a href="login.php" class="btn btn-primary">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    <?php echo __('login'); ?>
                                </a>
                            </div>
                        <?php endif; ?>
                        
                        <hr>
                        
                        <div class="text-center">
                            <a href="login.php" class="text-decoration-none">
                                <i class="fas fa-arrow-left me-2"></i>
                                <?php echo __('back_to_login'); ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
