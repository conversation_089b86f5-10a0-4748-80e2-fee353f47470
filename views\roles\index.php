<?php
/**
 * Roles Index View
 *
 * This view displays the list of roles.
 */

// Set page title
$pageTitle = __('roles');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 text-gradient mb-1"><?php echo __('roles'); ?></h1>
        <p class="text-muted mb-0"><?php echo __('manage_system_roles'); ?></p>
    </div>

    <?php if (hasPermission('manage_roles')): ?>
    <a href="<?php echo getBaseUrl(); ?>/roles/create" class="btn btn-primary">
        <?php echo __('add_role'); ?>
    </a>
    <?php endif; ?>
</div>

<div class="card fade-in-up">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <?php echo __('all_roles'); ?>
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($roles)): ?>
            <div class="text-center py-4">
                <p class="text-muted mb-0"><?php echo __('no_roles_found'); ?></p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th><?php echo __('role_name'); ?></th>
                            <th><?php echo __('display_name'); ?></th>
                            <th><?php echo __('description'); ?></th>
                            <th><?php echo __('users'); ?></th>
                            <th><?php echo __('type'); ?></th>
                            <th><?php echo __('actions'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($roles as $role): ?>
                            <tr>
                                <td>
                                    <code><?php echo htmlspecialchars($role['name']); ?></code>
                                </td>
                                <td>
                                    <strong><?php echo htmlspecialchars($role['display_name']); ?></strong>
                                </td>
                                <td>
                                    <?php if (!empty($role['description'])): ?>
                                        <?php echo htmlspecialchars($role['description']); ?>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?php echo $role['user_count']; ?></span>
                                </td>
                                <td>
                                    <?php if ($role['is_system']): ?>
                                        <span class="badge bg-secondary"><?php echo __('system'); ?></span>
                                    <?php else: ?>
                                        <span class="badge bg-primary"><?php echo __('custom'); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo getBaseUrl(); ?>/roles/view/<?php echo $role['id']; ?>" 
                                           class="btn btn-sm btn-outline-primary" title="<?php echo __('view'); ?>">
                                            <?php echo __('view'); ?>
                                        </a>
                                        
                                        <?php if (hasPermission('manage_roles') && !$role['is_system']): ?>
                                            <a href="<?php echo getBaseUrl(); ?>/roles/edit/<?php echo $role['id']; ?>" 
                                               class="btn btn-sm btn-outline-secondary" title="<?php echo __('edit'); ?>">
                                                <?php echo __('edit'); ?>
                                            </a>
                                            
                                            <?php if ($role['user_count'] == 0): ?>
                                                <a href="<?php echo getBaseUrl(); ?>/roles/delete/<?php echo $role['id']; ?>" 
                                                   class="btn btn-sm btn-outline-danger" title="<?php echo __('delete'); ?>">
                                                    <?php echo __('delete'); ?>
                                                </a>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
// Add scripts
$scripts = '
<script>
    $(document).ready(function() {
        // Initialize DataTable if there are roles
        if ($("table tbody tr").length > 0) {
            $("table").DataTable({
                responsive: true,
                pageLength: 25,
                order: [[0, "asc"]],
                language: {
                    search: "' . __('search') . ':",
                    lengthMenu: "' . __('show_entries') . '",
                    info: "' . __('showing_entries') . '",
                    infoEmpty: "' . __('no_entries') . '",
                    infoFiltered: "' . __('filtered_from') . '",
                    paginate: {
                        first: "' . __('first') . '",
                        last: "' . __('last') . '",
                        next: "' . __('next') . '",
                        previous: "' . __('previous') . '"
                    }
                }
            });
        }
    });
</script>
';

// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
