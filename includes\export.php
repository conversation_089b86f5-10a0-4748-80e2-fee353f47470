<?php
/**
 * Export Functions
 * 
 * This file contains functions for exporting data to different formats.
 */

/**
 * Export data to PDF
 * 
 * @param array $data The data to export
 * @param array $columns The columns to include
 * @param string $title The title of the PDF
 * @param string $filename The filename of the PDF
 * @return void
 */
function exportToPDF($data, $columns, $title, $filename = 'export.pdf') {
    // Check if TCPDF is available
    if (!class_exists('TCPDF')) {
        // If not, include a simple PDF generation using mPDF
        require_once 'vendor/mpdf/mpdf.php';
        
        // Create new mPDF document
        $mpdf = new mPDF('utf-8', 'A4-L');
        
        // Set document information
        $mpdf->SetTitle($title);
        $mpdf->SetAuthor('Medical Device Management System');
        
        // Set header and footer
        $mpdf->SetHeader('{DATE j-m-Y} | ' . $title . ' | {PAGENO}/{nb}');
        $mpdf->SetFooter('Medical Device Management System | ' . __('generated_on') . ' {DATE j-m-Y H:i:s} | {PAGENO}/{nb}');
        
        // Start HTML content
        $html = '
        <style>
            table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
            }
            th, td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
            }
            th {
                background-color: #f2f2f2;
                font-weight: bold;
            }
            tr:nth-child(even) {
                background-color: #f9f9f9;
            }
            h1 {
                font-size: 18px;
                text-align: center;
                margin-bottom: 20px;
            }
        </style>
        <h1>' . $title . '</h1>
        <table>
            <thead>
                <tr>';
        
        // Add table headers
        foreach ($columns as $key => $label) {
            $html .= '<th>' . $label . '</th>';
        }
        
        $html .= '</tr>
            </thead>
            <tbody>';
        
        // Add table rows
        foreach ($data as $row) {
            $html .= '<tr>';
            foreach ($columns as $key => $label) {
                $value = isset($row[$key]) ? $row[$key] : '';
                $html .= '<td>' . $value . '</td>';
            }
            $html .= '</tr>';
        }
        
        $html .= '</tbody>
        </table>';
        
        // Write HTML to PDF
        $mpdf->WriteHTML($html);
        
        // Output PDF
        $mpdf->Output($filename, 'D');
        exit;
    } else {
        // Use TCPDF if available
        require_once 'vendor/tcpdf/tcpdf.php';
        
        // Create new PDF document
        $pdf = new TCPDF('L', 'mm', 'A4', true, 'UTF-8', false);
        
        // Set document information
        $pdf->SetCreator('Medical Device Management System');
        $pdf->SetAuthor('Medical Device Management System');
        $pdf->SetTitle($title);
        
        // Set header and footer
        $pdf->setHeaderFont(Array('helvetica', '', 10));
        $pdf->setFooterFont(Array('helvetica', '', 8));
        $pdf->SetHeaderData('', 0, $title, date('Y-m-d H:i:s'));
        $pdf->setHeaderMargin(10);
        $pdf->setFooterMargin(10);
        
        // Set default monospaced font
        $pdf->SetDefaultMonospacedFont('courier');
        
        // Set margins
        $pdf->SetMargins(10, 20, 10);
        
        // Set auto page breaks
        $pdf->SetAutoPageBreak(TRUE, 15);
        
        // Set image scale factor
        $pdf->setImageScale(1.25);
        
        // Set font
        $pdf->SetFont('helvetica', '', 10);
        
        // Add a page
        $pdf->AddPage();
        
        // Start HTML content
        $html = '
        <style>
            table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
            }
            th, td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
            }
            th {
                background-color: #f2f2f2;
                font-weight: bold;
            }
            tr:nth-child(even) {
                background-color: #f9f9f9;
            }
            h1 {
                font-size: 18px;
                text-align: center;
                margin-bottom: 20px;
            }
        </style>
        <h1>' . $title . '</h1>
        <table>
            <thead>
                <tr>';
        
        // Add table headers
        foreach ($columns as $key => $label) {
            $html .= '<th>' . $label . '</th>';
        }
        
        $html .= '</tr>
            </thead>
            <tbody>';
        
        // Add table rows
        foreach ($data as $row) {
            $html .= '<tr>';
            foreach ($columns as $key => $label) {
                $value = isset($row[$key]) ? $row[$key] : '';
                $html .= '<td>' . $value . '</td>';
            }
            $html .= '</tr>';
        }
        
        $html .= '</tbody>
        </table>';
        
        // Write HTML to PDF
        $pdf->writeHTML($html, true, false, true, false, '');
        
        // Close and output PDF document
        $pdf->Output($filename, 'D');
        exit;
    }
}

/**
 * Export data to Excel
 * 
 * @param array $data The data to export
 * @param array $columns The columns to include
 * @param string $title The title of the Excel sheet
 * @param string $filename The filename of the Excel file
 * @return void
 */
function exportToExcel($data, $columns, $title, $filename = 'export.xlsx') {
    // Check if PhpSpreadsheet is available
    if (!class_exists('PhpOffice\PhpSpreadsheet\Spreadsheet')) {
        // If not, use a simple CSV export
        exportToCSV($data, $columns, $filename);
        exit;
    }
    
    // Use PhpSpreadsheet
    require_once 'vendor/autoload.php';
    
    // Create new Spreadsheet object
    $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
    
    // Set document properties
    $spreadsheet->getProperties()
        ->setCreator('Medical Device Management System')
        ->setLastModifiedBy('Medical Device Management System')
        ->setTitle($title)
        ->setSubject($title)
        ->setDescription('Export from Medical Device Management System')
        ->setKeywords('office, excel, export')
        ->setCategory('Export');
    
    // Add header row
    $sheet = $spreadsheet->getActiveSheet();
    $sheet->setTitle($title);
    
    // Set column headers
    $col = 1;
    foreach ($columns as $key => $label) {
        $sheet->setCellValueByColumnAndRow($col, 1, $label);
        $col++;
    }
    
    // Add data rows
    $row = 2;
    foreach ($data as $item) {
        $col = 1;
        foreach ($columns as $key => $label) {
            $value = isset($item[$key]) ? $item[$key] : '';
            $sheet->setCellValueByColumnAndRow($col, $row, $value);
            $col++;
        }
        $row++;
    }
    
    // Auto size columns
    foreach (range(1, count($columns)) as $col) {
        $sheet->getColumnDimensionByColumn($col)->setAutoSize(true);
    }
    
    // Style header row
    $headerStyle = [
        'font' => [
            'bold' => true,
        ],
        'fill' => [
            'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
            'startColor' => [
                'rgb' => 'E0E0E0',
            ],
        ],
    ];
    $sheet->getStyle('A1:' . \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex(count($columns)) . '1')->applyFromArray($headerStyle);
    
    // Create writer
    $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
    
    // Set headers for download
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment;filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    
    // Save file to output
    $writer->save('php://output');
    exit;
}

/**
 * Export data to CSV
 * 
 * @param array $data The data to export
 * @param array $columns The columns to include
 * @param string $filename The filename of the CSV file
 * @return void
 */
function exportToCSV($data, $columns, $filename = 'export.csv') {
    // Set headers for download
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    
    // Create a file pointer connected to the output stream
    $output = fopen('php://output', 'w');
    
    // Add UTF-8 BOM
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // Add column headers
    fputcsv($output, array_values($columns));
    
    // Add rows
    foreach ($data as $row) {
        $csvRow = [];
        foreach ($columns as $key => $label) {
            $csvRow[] = isset($row[$key]) ? $row[$key] : '';
        }
        fputcsv($output, $csvRow);
    }
    
    // Close the file pointer
    fclose($output);
    exit;
}

/**
 * Export data based on format
 * 
 * @param string $format The export format (pdf, excel, csv)
 * @param array $data The data to export
 * @param array $columns The columns to include
 * @param string $title The title of the export
 * @param string $filename The filename of the export
 * @return void
 */
function exportData($format, $data, $columns, $title, $filename = 'export') {
    // Sanitize data for export
    $sanitizedData = [];
    foreach ($data as $row) {
        $sanitizedRow = [];
        foreach ($row as $key => $value) {
            $sanitizedRow[$key] = htmlspecialchars_decode(strip_tags($value));
        }
        $sanitizedData[] = $sanitizedRow;
    }
    
    // Export based on format
    switch ($format) {
        case 'pdf':
            exportToPDF($sanitizedData, $columns, $title, $filename . '.pdf');
            break;
        case 'excel':
            exportToExcel($sanitizedData, $columns, $title, $filename . '.xlsx');
            break;
        case 'csv':
            exportToCSV($sanitizedData, $columns, $filename . '.csv');
            break;
        default:
            // Default to CSV
            exportToCSV($sanitizedData, $columns, $filename . '.csv');
            break;
    }
}
