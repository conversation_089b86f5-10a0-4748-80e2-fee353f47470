<?php
/**
 * Change Ticket Status View
 * 
 * This file displays the form to change a ticket's status.
 */

// Set page title
$pageTitle = __('change_ticket_status');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3"><?php echo __('change_ticket_status'); ?></h1>
    
    <a href="<?php echo getBaseUrl(); ?>/tickets" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_tickets'); ?>
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <!-- Ticket Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-ticket-alt me-2"></i><?php echo __('ticket_information'); ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('ticket_id'); ?></label>
                            <div class="h5">#<?php echo str_pad($ticket['id'], 6, '0', STR_PAD_LEFT); ?></div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('title'); ?></label>
                            <div class="fw-bold"><?php echo htmlspecialchars($ticket['title']); ?></div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('device'); ?></label>
                            <div class="d-flex align-items-center">
                                <div class="device-icon me-2">
                                    <i class="fas fa-medical-kit text-primary"></i>
                                </div>
                                <div>
                                    <div class="fw-bold"><?php echo htmlspecialchars($ticket['device_name']); ?></div>
                                    <small class="text-muted"><?php echo htmlspecialchars($ticket['serial_number']); ?></small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('current_status'); ?></label>
                            <div>
                                <span class="badge bg-<?php echo getTicketStatusColor($ticket['status']); ?> fs-6">
                                    <i class="fas fa-<?php echo getTicketStatusIcon($ticket['status']); ?> me-1"></i>
                                    <?php echo __($ticket['status']); ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('priority'); ?></label>
                            <div>
                                <span class="badge bg-<?php echo getTicketPriorityColor($ticket['priority']); ?> fs-6">
                                    <?php echo __($ticket['priority']); ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('assigned_to'); ?></label>
                            <div>
                                <?php if ($ticket['assigned_to_name']): ?>
                                    <div class="d-flex align-items-center">
                                        <div class="user-avatar me-2">
                                            <div class="bg-info text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 32px; height: 32px; font-size: 0.8rem;">
                                                <?php echo strtoupper(substr($ticket['assigned_to_name'], 0, 1)); ?>
                                            </div>
                                        </div>
                                        <div>
                                            <div class="fw-bold"><?php echo htmlspecialchars($ticket['assigned_to_name']); ?></div>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <span class="text-muted"><?php echo __('unassigned'); ?></span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label text-muted"><?php echo __('description'); ?></label>
                    <div class="border rounded p-3 bg-light">
                        <?php echo nl2br(htmlspecialchars($ticket['description'])); ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Status Change Form -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exchange-alt me-2"></i><?php echo __('change_status'); ?>
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?php echo getBaseUrl(); ?>/tickets/status/<?php echo $ticket['id']; ?>">
                    <div class="mb-4">
                        <label for="status" class="form-label"><?php echo __('new_status'); ?> <span class="text-danger">*</span></label>
                        <select class="form-select" id="status" name="status" required onchange="updateStatusInfo()">
                            <option value=""><?php echo __('select_new_status'); ?></option>
                            
                            <?php
                            $statusOptions = [
                                'open' => ['color' => 'primary', 'icon' => 'folder-open', 'description' => __('ticket_is_open_awaiting_assignment')],
                                'in_progress' => ['color' => 'warning', 'icon' => 'cog', 'description' => __('ticket_is_being_worked_on')],
                                'pending' => ['color' => 'info', 'icon' => 'clock', 'description' => __('ticket_is_waiting_for_parts_or_approval')],
                                'resolved' => ['color' => 'success', 'icon' => 'check-circle', 'description' => __('issue_has_been_fixed')],
                                'closed' => ['color' => 'secondary', 'icon' => 'times-circle', 'description' => __('ticket_is_closed_and_complete')],
                                'cancelled' => ['color' => 'dark', 'icon' => 'ban', 'description' => __('ticket_has_been_cancelled')]
                            ];
                            ?>
                            
                            <?php foreach ($statusOptions as $statusValue => $statusInfo): ?>
                                <?php if ($statusValue !== $ticket['status']): // Don't show current status ?>
                                    <option value="<?php echo $statusValue; ?>" 
                                            data-color="<?php echo $statusInfo['color']; ?>"
                                            data-icon="<?php echo $statusInfo['icon']; ?>"
                                            data-description="<?php echo htmlspecialchars($statusInfo['description']); ?>">
                                        <?php echo __($statusValue); ?>
                                    </option>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </select>
                        <div class="form-text"><?php echo __('select_appropriate_status'); ?></div>
                    </div>
                    
                    <!-- Status Information -->
                    <div id="status_info" class="alert alert-info d-none mb-4">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i id="status_icon" class="fas fa-info-circle fa-2x"></i>
                            </div>
                            <div>
                                <h6 class="alert-heading mb-1" id="status_title"><?php echo __('status_information'); ?></h6>
                                <p class="mb-0" id="status_description"></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="comment" class="form-label"><?php echo __('status_change_comment'); ?></label>
                        <textarea class="form-control" 
                                  id="comment" 
                                  name="comment" 
                                  rows="4" 
                                  placeholder="<?php echo __('explain_reason_for_status_change'); ?>"></textarea>
                        <div class="form-text"><?php echo __('provide_details_about_status_change'); ?></div>
                    </div>
                    
                    <!-- Resolution Details (for resolved status) -->
                    <div id="resolution_details" class="d-none mb-4">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-check-circle me-2"></i><?php echo __('resolution_details'); ?>
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="resolution_method" class="form-label"><?php echo __('resolution_method'); ?></label>
                                    <select class="form-select" id="resolution_method" name="resolution_method">
                                        <option value=""><?php echo __('select_resolution_method'); ?></option>
                                        <option value="repair"><?php echo __('repaired'); ?></option>
                                        <option value="replacement"><?php echo __('replaced_part'); ?></option>
                                        <option value="maintenance"><?php echo __('maintenance_performed'); ?></option>
                                        <option value="calibration"><?php echo __('calibrated'); ?></option>
                                        <option value="software_update"><?php echo __('software_updated'); ?></option>
                                        <option value="user_error"><?php echo __('user_error_corrected'); ?></option>
                                        <option value="other"><?php echo __('other'); ?></option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="parts_used" class="form-label"><?php echo __('parts_used'); ?></label>
                                    <textarea class="form-control" 
                                              id="parts_used" 
                                              name="parts_used" 
                                              rows="2" 
                                              placeholder="<?php echo __('list_parts_or_materials_used'); ?>"></textarea>
                                </div>
                                
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="notify_reporter" name="notify_reporter" checked>
                                    <label class="form-check-label" for="notify_reporter">
                                        <?php echo __('notify_ticket_reporter'); ?>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Closure Details (for closed status) -->
                    <div id="closure_details" class="d-none mb-4">
                        <div class="card border-secondary">
                            <div class="card-header bg-secondary text-white">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-times-circle me-2"></i><?php echo __('closure_details'); ?>
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="confirm_resolution" name="confirm_resolution" required>
                                    <label class="form-check-label" for="confirm_resolution">
                                        <?php echo __('confirm_issue_resolved'); ?>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="<?php echo getBaseUrl(); ?>/tickets/view/<?php echo $ticket['id']; ?>" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>
                        </a>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-exchange-alt me-2"></i><?php echo __('change_status'); ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function updateStatusInfo() {
    const statusSelect = document.getElementById('status');
    const statusInfo = document.getElementById('status_info');
    const statusIcon = document.getElementById('status_icon');
    const statusTitle = document.getElementById('status_title');
    const statusDescription = document.getElementById('status_description');
    const resolutionDetails = document.getElementById('resolution_details');
    const closureDetails = document.getElementById('closure_details');
    
    const selectedOption = statusSelect.options[statusSelect.selectedIndex];
    
    if (selectedOption.value) {
        const color = selectedOption.dataset.color;
        const icon = selectedOption.dataset.icon;
        const description = selectedOption.dataset.description;
        
        // Update status info
        statusInfo.className = `alert alert-${color}`;
        statusIcon.className = `fas fa-${icon} fa-2x`;
        statusTitle.textContent = selectedOption.text;
        statusDescription.textContent = description;
        statusInfo.classList.remove('d-none');
        
        // Show/hide additional details
        resolutionDetails.classList.toggle('d-none', selectedOption.value !== 'resolved');
        closureDetails.classList.toggle('d-none', selectedOption.value !== 'closed');
    } else {
        statusInfo.classList.add('d-none');
        resolutionDetails.classList.add('d-none');
        closureDetails.classList.add('d-none');
    }
}
</script>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
