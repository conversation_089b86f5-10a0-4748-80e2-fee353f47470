<?php
/**
 * User View
 * 
 * This file displays the details of a user.
 */

// Set page title
$pageTitle = __('user_details');
$pageSubtitle = htmlspecialchars($user['full_name']);

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 text-gradient mb-1"><?php echo __('user_details'); ?></h1>
        <p class="text-muted mb-0"><?php echo htmlspecialchars($user['full_name']); ?></p>
    </div>
    
    <div class="btn-group">
        <a href="<?php echo getBaseUrl(); ?>/users" class="btn btn-outline-secondary">
            <?php echo __('back_to_list'); ?>
        </a>

        <?php if (hasPermission('manage_users')): ?>
            <a href="<?php echo getBaseUrl(); ?>/users/edit/<?php echo $user['id']; ?>" class="btn btn-primary">
                <?php echo __('edit'); ?>
            </a>
        <?php endif; ?>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card fade-in-up">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <?php echo __('user_information'); ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="40%"><?php echo __('full_name'); ?></th>
                                <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                            </tr>
                            <tr>
                                <th><?php echo __('username'); ?></th>
                                <td><?php echo htmlspecialchars($user['username']); ?></td>
                            </tr>
                            <tr>
                                <th><?php echo __('email'); ?></th>
                                <td><?php echo htmlspecialchars($user['email']); ?></td>
                            </tr>
                            <tr>
                                <th><?php echo __('role'); ?></th>
                                <td>
                                    <span class="badge bg-<?php 
                                        echo $user['role'] === 'admin' ? 'danger' : 
                                            ($user['role'] === 'manager' ? 'warning' : 
                                                ($user['role'] === 'technician' ? 'info' : 'secondary')); 
                                    ?>">
                                        <?php echo __(strtolower($user['role'])); ?>
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="40%"><?php echo __('hospital'); ?></th>
                                <td>
                                    <?php if ($user['hospital_id'] && hasPermission('view_hospitals')): ?>
                                        <a href="<?php echo getBaseUrl(); ?>/hospitals/view/<?php echo $user['hospital_id']; ?>">
                                            <?php echo htmlspecialchars($user['hospital_name'] ?? __('unknown')); ?>
                                        </a>
                                    <?php elseif ($user['hospital_id']): ?>
                                        <?php echo htmlspecialchars($user['hospital_name'] ?? __('unknown')); ?>
                                    <?php else: ?>
                                        <span class="text-muted"><?php echo __('no_hospital'); ?></span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <th><?php echo __('language'); ?></th>
                                <td><?php echo $user['language'] === 'ar' ? 'العربية' : 'English'; ?></td>
                            </tr>
                            <tr>
                                <th><?php echo __('status'); ?></th>
                                <td>
                                    <span class="badge bg-<?php echo $user['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                        <?php echo __($user['status']); ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <th><?php echo __('last_login'); ?></th>
                                <td>
                                    <?php if ($user['last_login']): ?>
                                        <?php echo date('Y-m-d H:i', strtotime($user['last_login'])); ?>
                                    <?php else: ?>
                                        <span class="text-muted"><?php echo __('never'); ?></span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card fade-in-up" style="animation-delay: 0.2s;">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <?php echo __('activity_summary'); ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border-end">
                            <h4 class="text-primary mb-1"><?php echo $stats['tickets_created'] ?? 0; ?></h4>
                            <small class="text-muted"><?php echo __('tickets_created'); ?></small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-success mb-1"><?php echo $stats['tickets_resolved'] ?? 0; ?></h4>
                        <small class="text-muted"><?php echo __('tickets_resolved'); ?></small>
                    </div>
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-warning mb-1"><?php echo $stats['maintenance_performed'] ?? 0; ?></h4>
                            <small class="text-muted"><?php echo __('maintenance_performed'); ?></small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info mb-1"><?php echo $stats['devices_managed'] ?? 0; ?></h4>
                        <small class="text-muted"><?php echo __('devices_managed'); ?></small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card fade-in-up mt-4" style="animation-delay: 0.3s;">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <?php echo __('account_info'); ?>
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless table-sm">
                    <tr>
                        <th><?php echo __('created_at'); ?></th>
                        <td><?php echo date('Y-m-d', strtotime($user['created_at'])); ?></td>
                    </tr>
                    <tr>
                        <th><?php echo __('updated_at'); ?></th>
                        <td><?php echo date('Y-m-d', strtotime($user['updated_at'])); ?></td>
                    </tr>
                    <?php if (isset($user['created_by_name'])): ?>
                    <tr>
                        <th><?php echo __('created_by'); ?></th>
                        <td><?php echo htmlspecialchars($user['created_by_name']); ?></td>
                    </tr>
                    <?php endif; ?>
                </table>
            </div>
        </div>
    </div>
</div>

<?php if (hasPermission('view_tickets') || hasPermission('view_maintenance')): ?>
<div class="row mt-4">
    <?php if (hasPermission('view_tickets')): ?>
    <div class="col-md-6">
        <div class="card fade-in-up" style="animation-delay: 0.4s;">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <?php echo __('recent_tickets'); ?>
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($recentTickets)): ?>
                    <div class="list-group list-group-flush">
                        <?php foreach (array_slice($recentTickets, 0, 5) as $ticket): ?>
                            <div class="list-group-item border-0 px-0">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">
                                            <a href="<?php echo getBaseUrl(); ?>/tickets/view/<?php echo $ticket['id']; ?>">
                                                <?php echo htmlspecialchars($ticket['title']); ?>
                                            </a>
                                        </h6>
                                        <small class="text-muted"><?php echo date('M d, Y', strtotime($ticket['created_at'])); ?></small>
                                    </div>
                                    <span class="badge bg-<?php 
                                        echo $ticket['status'] === 'open' ? 'danger' : 
                                            ($ticket['status'] === 'in_progress' ? 'warning' : 
                                                ($ticket['status'] === 'resolved' ? 'success' : 'secondary')); 
                                    ?>">
                                        <?php echo __($ticket['status']); ?>
                                    </span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <p class="text-muted text-center"><?php echo __('no_recent_tickets'); ?></p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <?php if (hasPermission('view_maintenance')): ?>
    <div class="col-md-6">
        <div class="card fade-in-up" style="animation-delay: 0.5s;">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <?php echo __('recent_maintenance'); ?>
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($recentMaintenance)): ?>
                    <div class="list-group list-group-flush">
                        <?php foreach (array_slice($recentMaintenance, 0, 5) as $maintenance): ?>
                            <div class="list-group-item border-0 px-0">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">
                                            <a href="<?php echo getBaseUrl(); ?>/maintenance/view/<?php echo $maintenance['id']; ?>">
                                                <?php echo htmlspecialchars($maintenance['title']); ?>
                                            </a>
                                        </h6>
                                        <small class="text-muted"><?php echo date('M d, Y', strtotime($maintenance['performed_date'])); ?></small>
                                    </div>
                                    <span class="badge bg-success"><?php echo __('completed'); ?></span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <p class="text-muted text-center"><?php echo __('no_recent_maintenance'); ?></p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>
<?php endif; ?>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
