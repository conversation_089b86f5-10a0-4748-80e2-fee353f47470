<?php
/**
 * PHP QR Code implementation for Medical Device Management System
 * 
 * This is a simplified version of the PHP QR Code library for demonstration purposes.
 * In a production environment, you should use a full-featured QR code library.
 */

class QRcode {
    /**
     * Generate a QR code PNG image
     * 
     * @param string $text The text to encode in the QR code
     * @param string $outfile The output file path
     * @param string $level Error correction level (L, M, Q, H)
     * @param int $size Pixel size
     * @param int $margin Margin size
     * @return bool True if successful, false otherwise
     */
    public static function png($text, $outfile = false, $level = 'L', $size = 3, $margin = 4) {
        // Check if GD extension is available
        if (!extension_loaded('gd')) {
            error_log("GD extension is not available. QR code generation failed.");
            return false;
        }
        
        // Create a simple QR code-like image (this is just a placeholder)
        // In a real implementation, this would generate an actual QR code
        
        // Set image dimensions
        $width = ($size * 25) + ($margin * 2);
        $height = $width;
        
        // Create image
        $image = imagecreatetruecolor($width, $height);
        
        // Define colors
        $white = imagecolorallocate($image, 255, 255, 255);
        $black = imagecolorallocate($image, 0, 0, 0);
        
        // Fill background
        imagefill($image, 0, 0, $white);
        
        // Draw border
        imagerectangle($image, $margin, $margin, $width - $margin - 1, $height - $margin - 1, $black);
        
        // Draw position detection patterns (corners)
        self::drawPositionPattern($image, $margin, $margin, $size);
        self::drawPositionPattern($image, $width - $margin - ($size * 7), $margin, $size);
        self::drawPositionPattern($image, $margin, $height - $margin - ($size * 7), $size);
        
        // Draw some random data pattern (this is not a real QR code)
        $hash = md5($text);
        for ($i = 0; $i < strlen($hash); $i++) {
            $val = hexdec($hash[$i]);
            $x = ($margin + $size * 7) + ($i % 10) * $size;
            $y = ($margin + $size * 7) + floor($i / 10) * $size;
            
            if ($val > 7) {
                imagefilledrectangle($image, $x, $y, $x + $size - 1, $y + $size - 1, $black);
            }
        }
        
        // Add text label at the bottom
        $font = 1; // Built-in font
        $text_width = imagefontwidth($font) * strlen($text);
        $text_x = ($width - $text_width) / 2;
        $text_y = $height - $margin;
        imagestring($image, $font, $text_x, $text_y, substr($text, 0, 20), $black);
        
        // Output image
        if ($outfile !== false) {
            imagepng($image, $outfile);
            imagedestroy($image);
            return file_exists($outfile);
        } else {
            header("Content-type: image/png");
            imagepng($image);
            imagedestroy($image);
            return true;
        }
    }
    
    /**
     * Draw a position detection pattern
     * 
     * @param resource $image The image resource
     * @param int $x X coordinate
     * @param int $y Y coordinate
     * @param int $size Pixel size
     */
    private static function drawPositionPattern($image, $x, $y, $size) {
        $black = imagecolorallocate($image, 0, 0, 0);
        $white = imagecolorallocate($image, 255, 255, 255);
        
        // Outer square
        imagefilledrectangle($image, $x, $y, $x + $size * 7 - 1, $y + $size * 7 - 1, $black);
        
        // Middle square
        imagefilledrectangle($image, $x + $size, $y + $size, $x + $size * 6 - 1, $y + $size * 6 - 1, $white);
        
        // Inner square
        imagefilledrectangle($image, $x + $size * 2, $y + $size * 2, $x + $size * 5 - 1, $y + $size * 5 - 1, $black);
    }
}
