<?php
/**
 * Roles Edit View
 *
 * This view displays the form to edit a role.
 */

// Set page title
$pageTitle = __('edit_role');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 text-gradient mb-1"><?php echo __('edit_role'); ?></h1>
        <p class="text-muted mb-0"><?php echo __('edit_role_information'); ?></p>
    </div>

    <a href="<?php echo getBaseUrl(); ?>/roles" class="btn btn-outline-secondary">
        <?php echo __('back_to_list'); ?>
    </a>
</div>

<div class="row">
    <div class="col-lg-8 col-md-10 mx-auto">
        <div class="card fade-in-up">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <?php echo __('role_information'); ?>
                </h5>
            </div>
            <div class="card-body">
                <form action="<?php echo getBaseUrl(); ?>/roles/update/<?php echo $role['id']; ?>" method="post" id="roleForm">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="id" value="<?php echo $role['id']; ?>">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label"><?php echo __('role_name'); ?> <span class="text-danger">*</span></label>
                                <input type="text" class="form-control <?php echo isset($errors['name']) ? 'is-invalid' : ''; ?>" 
                                       id="name" name="name" value="<?php echo htmlspecialchars($roleData['name'] ?? ''); ?>" 
                                       pattern="[a-z_]+" title="<?php echo __('role_name_pattern'); ?>" required>
                                <div class="form-text"><?php echo __('role_name_help'); ?></div>
                                <?php if (isset($errors['name'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['name']; ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="display_name" class="form-label"><?php echo __('display_name'); ?> <span class="text-danger">*</span></label>
                                <input type="text" class="form-control <?php echo isset($errors['display_name']) ? 'is-invalid' : ''; ?>" 
                                       id="display_name" name="display_name" value="<?php echo htmlspecialchars($roleData['display_name'] ?? ''); ?>" required>
                                <?php if (isset($errors['display_name'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['display_name']; ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label"><?php echo __('description'); ?></label>
                        <textarea class="form-control" id="description" name="description" rows="3"><?php echo htmlspecialchars($roleData['description'] ?? ''); ?></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="display_order" class="form-label"><?php echo __('display_order'); ?></label>
                        <input type="number" class="form-control" id="display_order" name="display_order" 
                               value="<?php echo htmlspecialchars($roleData['display_order'] ?? '0'); ?>" min="0">
                        <div class="form-text"><?php echo __('display_order_help'); ?></div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label"><?php echo __('permissions'); ?></label>
                        <div class="row">
                            <?php 
                            $selectedPermissions = $roleData['permissions'] ?? [];
                            $permissionGroups = [
                                'Dashboard' => ['view_dashboard'],
                                'Users' => ['view_users', 'manage_users'],
                                'Hospitals' => ['view_hospitals', 'manage_hospitals'],
                                'Departments' => ['view_departments', 'manage_departments'],
                                'Devices' => ['view_devices', 'manage_devices'],
                                'Maintenance' => ['view_maintenance', 'manage_maintenance'],
                                'Tickets' => ['view_tickets', 'manage_tickets'],
                                'Reports' => ['view_reports', 'export_data'],
                                'Roles' => ['view_roles', 'manage_roles']
                            ];
                            
                            foreach ($permissionGroups as $groupName => $groupPermissions): ?>
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card">
                                        <div class="card-header py-2">
                                            <h6 class="mb-0"><?php echo __($groupName); ?></h6>
                                        </div>
                                        <div class="card-body py-2">
                                            <?php foreach ($groupPermissions as $permission): ?>
                                                <?php if (isset($availablePermissions[$permission])): ?>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" 
                                                               id="permission_<?php echo $permission; ?>" 
                                                               name="permissions[]" value="<?php echo $permission; ?>"
                                                               <?php echo in_array($permission, $selectedPermissions) ? 'checked' : ''; ?>>
                                                        <label class="form-check-label" for="permission_<?php echo $permission; ?>">
                                                            <?php echo $availablePermissions[$permission]; ?>
                                                        </label>
                                                    </div>
                                                <?php endif; ?>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <?php if (isset($errors['general'])): ?>
                        <div class="alert alert-danger" role="alert">
                            <?php echo $errors['general']; ?>
                        </div>
                    <?php endif; ?>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="reset" class="btn btn-secondary me-md-2">
                            <?php echo __('reset'); ?>
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <?php echo __('save'); ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
// Add scripts
$scripts = '
<script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>
<script>
    $(document).ready(function() {
        // Initialize form validation
        $("#roleForm").validate({
            rules: {
                name: {
                    required: true,
                    pattern: /^[a-z_]+$/,
                    minlength: 2,
                    maxlength: 50
                },
                display_name: {
                    required: true,
                    minlength: 2,
                    maxlength: 100
                },
                display_order: {
                    number: true,
                    min: 0
                }
            },
            messages: {
                name: {
                    required: "' . __('field_required') . '",
                    pattern: "' . __('role_name_pattern') . '",
                    minlength: "' . __('min_length', ['length' => 2]) . '",
                    maxlength: "' . __('max_length', ['length' => 50]) . '"
                },
                display_name: {
                    required: "' . __('field_required') . '",
                    minlength: "' . __('min_length', ['length' => 2]) . '",
                    maxlength: "' . __('max_length', ['length' => 100]) . '"
                },
                display_order: {
                    number: "' . __('must_be_number') . '",
                    min: "' . __('min_value', ['value' => 0]) . '"
                }
            },
            errorElement: "div",
            errorPlacement: function(error, element) {
                error.addClass("invalid-feedback");
                error.insertAfter(element);
            },
            highlight: function(element) {
                $(element).addClass("is-invalid");
            },
            unhighlight: function(element) {
                $(element).removeClass("is-invalid");
            }
        });
        
        // Convert role name to lowercase and replace spaces with underscores
        $("#name").on("input", function() {
            this.value = this.value.toLowerCase().replace(/[^a-z_]/g, "");
        });
    });
</script>
';

// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
