<?php
/**
 * Error Page View
 *
 * This file displays error messages to users.
 */

// Get error details from URL parameters
$errorCode = $_GET['code'] ?? '500';
$errorMessage = $_GET['message'] ?? __('unknown_error');

// Set page title
$pageTitle = __('error') . ' ' . $errorCode;

// Start output buffering
ob_start();
?>

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-sm">
                <div class="card-body text-center py-5">
                    <?php if ($errorCode == '403'): ?>
                        <i class="fas fa-ban text-danger" style="font-size: 4rem;"></i>
                        <h2 class="mt-3 text-danger"><?php echo __('access_denied'); ?></h2>
                        <p class="text-muted"><?php echo __('access_denied_message'); ?></p>
                    <?php elseif ($errorCode == '404'): ?>
                        <i class="fas fa-search text-warning" style="font-size: 4rem;"></i>
                        <h2 class="mt-3 text-warning"><?php echo __('page_not_found'); ?></h2>
                        <p class="text-muted"><?php echo __('page_not_found_message'); ?></p>
                    <?php elseif ($errorCode == '500'): ?>
                        <i class="fas fa-exclamation-triangle text-danger" style="font-size: 4rem;"></i>
                        <h2 class="mt-3 text-danger"><?php echo __('server_error'); ?></h2>
                        <p class="text-muted"><?php echo __('server_error_message'); ?></p>
                    <?php else: ?>
                        <i class="fas fa-exclamation-circle text-secondary" style="font-size: 4rem;"></i>
                        <h2 class="mt-3 text-secondary"><?php echo __('error'); ?></h2>
                        <p class="text-muted"><?php echo htmlspecialchars($errorMessage); ?></p>
                    <?php endif; ?>
                    
                    <div class="mt-4">
                        <a href="<?php echo getBaseUrl(); ?>/dashboard" class="btn btn-primary me-2">
                            <i class="fas fa-home me-2"></i><?php echo __('go_to_dashboard'); ?>
                        </a>
                        <button onclick="history.back()" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i><?php echo __('go_back'); ?>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
