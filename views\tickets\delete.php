<?php
/**
 * Delete Ticket View
 * 
 * This file displays the confirmation form to delete a ticket.
 */

// Set page title
$pageTitle = __('delete_ticket');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3"><?php echo __('delete_ticket'); ?></h1>
    
    <a href="<?php echo getBaseUrl(); ?>/tickets" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_tickets'); ?>
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo __('confirm_deletion'); ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-danger">
                    <h6 class="alert-heading">
                        <i class="fas fa-warning me-2"></i><?php echo __('warning'); ?>
                    </h6>
                    <p class="mb-0"><?php echo __('delete_ticket_warning'); ?></p>
                </div>
                
                <!-- Ticket Details -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i><?php echo __('ticket_details'); ?>
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted"><?php echo __('ticket_id'); ?></label>
                                    <div class="h5">#<?php echo str_pad($ticket['id'], 6, '0', STR_PAD_LEFT); ?></div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label text-muted"><?php echo __('title'); ?></label>
                                    <div class="fw-bold"><?php echo htmlspecialchars($ticket['title']); ?></div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label text-muted"><?php echo __('device'); ?></label>
                                    <div class="d-flex align-items-center">
                                        <div class="device-icon me-2">
                                            <i class="fas fa-medical-kit text-primary"></i>
                                        </div>
                                        <div>
                                            <div class="fw-bold"><?php echo htmlspecialchars($ticket['device_name']); ?></div>
                                            <small class="text-muted"><?php echo htmlspecialchars($ticket['serial_number']); ?></small>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label text-muted"><?php echo __('reported_by'); ?></label>
                                    <div class="d-flex align-items-center">
                                        <div class="user-avatar me-2">
                                            <div class="bg-info text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 32px; height: 32px; font-size: 0.8rem;">
                                                <?php echo strtoupper(substr($ticket['reported_by_name'], 0, 1)); ?>
                                            </div>
                                        </div>
                                        <div>
                                            <div class="fw-bold"><?php echo htmlspecialchars($ticket['reported_by_name']); ?></div>
                                            <small class="text-muted"><?php echo date('M d, Y', strtotime($ticket['created_at'])); ?></small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted"><?php echo __('priority'); ?></label>
                                    <div>
                                        <span class="badge bg-<?php echo getTicketPriorityColor($ticket['priority']); ?>">
                                            <?php echo __($ticket['priority']); ?>
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label text-muted"><?php echo __('status'); ?></label>
                                    <div>
                                        <span class="badge bg-<?php echo getTicketStatusColor($ticket['status']); ?>">
                                            <?php echo __($ticket['status']); ?>
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label text-muted"><?php echo __('assigned_to'); ?></label>
                                    <div>
                                        <?php if ($ticket['assigned_to_name']): ?>
                                            <div class="d-flex align-items-center">
                                                <div class="user-avatar me-2">
                                                    <div class="bg-warning text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 32px; height: 32px; font-size: 0.8rem;">
                                                        <?php echo strtoupper(substr($ticket['assigned_to_name'], 0, 1)); ?>
                                                    </div>
                                                </div>
                                                <div>
                                                    <div class="fw-bold"><?php echo htmlspecialchars($ticket['assigned_to_name']); ?></div>
                                                </div>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted"><?php echo __('unassigned'); ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label text-muted"><?php echo __('created_date'); ?></label>
                                    <div class="fw-bold"><?php echo date('F d, Y H:i', strtotime($ticket['created_at'])); ?></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('description'); ?></label>
                            <div class="border rounded p-3 bg-light">
                                <?php echo nl2br(htmlspecialchars($ticket['description'])); ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Related Data Warning -->
                <?php
                // Check for related data
                $relatedUpdates = $ticketModel->getUpdates($ticket['id']);
                $hasRelatedData = !empty($relatedUpdates);
                ?>
                
                <?php if ($hasRelatedData): ?>
                    <div class="alert alert-warning">
                        <h6 class="alert-heading">
                            <i class="fas fa-exclamation-triangle me-2"></i><?php echo __('related_data_found'); ?>
                        </h6>
                        <p class="mb-2"><?php echo __('ticket_has_related_data'); ?></p>
                        
                        <ul class="mb-0">
                            <?php if (!empty($relatedUpdates)): ?>
                                <li><strong><?php echo count($relatedUpdates); ?></strong> <?php echo __('ticket_updates_comments'); ?></li>
                            <?php endif; ?>
                        </ul>
                        
                        <div class="mt-2">
                            <small class="text-muted"><?php echo __('all_related_data_will_be_deleted'); ?></small>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- Status Warning -->
                <?php if ($ticket['status'] !== 'closed' && $ticket['status'] !== 'cancelled'): ?>
                    <div class="alert alert-info">
                        <h6 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i><?php echo __('ticket_status_notice'); ?>
                        </h6>
                        <p class="mb-0">
                            <?php echo __('ticket_not_closed_consider_closing'); ?>
                            <a href="<?php echo getBaseUrl(); ?>/tickets/status/<?php echo $ticket['id']; ?>" class="alert-link">
                                <?php echo __('change_status_instead'); ?>
                            </a>
                        </p>
                    </div>
                <?php endif; ?>
                
                <!-- Confirmation Form -->
                <form method="POST" action="<?php echo getBaseUrl(); ?>/tickets/delete/<?php echo $ticket['id']; ?>">
                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="confirm_deletion" name="confirm_deletion" required>
                            <label class="form-check-label" for="confirm_deletion">
                                <?php echo __('confirm_delete_ticket'); ?>
                            </label>
                        </div>
                        
                        <?php if ($hasRelatedData): ?>
                            <div class="form-check mt-2">
                                <input class="form-check-input" type="checkbox" id="confirm_related_data" name="confirm_related_data" required>
                                <label class="form-check-label" for="confirm_related_data">
                                    <?php echo __('confirm_delete_related_data'); ?>
                                </label>
                            </div>
                        <?php endif; ?>
                        
                        <div class="form-check mt-2">
                            <input class="form-check-input" type="checkbox" id="understand_permanent" name="understand_permanent" required>
                            <label class="form-check-label" for="understand_permanent">
                                <?php echo __('understand_action_permanent'); ?>
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="deletion_reason" class="form-label"><?php echo __('reason_for_deletion'); ?></label>
                        <textarea class="form-control" 
                                  id="deletion_reason" 
                                  name="deletion_reason" 
                                  rows="3" 
                                  placeholder="<?php echo __('explain_why_deleting_ticket'); ?>"
                                  required></textarea>
                        <div class="form-text"><?php echo __('deletion_reason_required'); ?></div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="<?php echo getBaseUrl(); ?>/tickets" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>
                        </a>
                        
                        <button type="submit" name="confirm" value="yes" class="btn btn-danger" id="delete_button" disabled>
                            <i class="fas fa-trash me-2"></i><?php echo __('delete_ticket'); ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Additional Information -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i><?php echo __('what_happens_when_deleted'); ?>
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-danger me-2"></i>
                        <?php echo __('ticket_will_be_permanently_deleted'); ?>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-danger me-2"></i>
                        <?php echo __('all_comments_updates_deleted'); ?>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-warning me-2"></i>
                        <?php echo __('notifications_will_be_removed'); ?>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-info me-2"></i>
                        <?php echo __('device_status_unchanged'); ?>
                    </li>
                    <li>
                        <i class="fas fa-check text-secondary me-2"></i>
                        <?php echo __('action_cannot_be_undone'); ?>
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- Alternative Actions -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-lightbulb me-2"></i><?php echo __('alternative_actions'); ?>
                </h6>
            </div>
            <div class="card-body">
                <p class="text-muted mb-3"><?php echo __('consider_these_alternatives'); ?>:</p>
                
                <div class="d-grid gap-2">
                    <a href="<?php echo getBaseUrl(); ?>/tickets/status/<?php echo $ticket['id']; ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-times-circle me-2"></i><?php echo __('close_ticket_instead'); ?>
                    </a>
                    
                    <a href="<?php echo getBaseUrl(); ?>/tickets/status/<?php echo $ticket['id']; ?>" class="btn btn-outline-warning">
                        <i class="fas fa-ban me-2"></i><?php echo __('cancel_ticket_instead'); ?>
                    </a>
                    
                    <a href="<?php echo getBaseUrl(); ?>/tickets/edit/<?php echo $ticket['id']; ?>" class="btn btn-outline-info">
                        <i class="fas fa-edit me-2"></i><?php echo __('edit_ticket_details'); ?>
                    </a>
                    
                    <a href="<?php echo getBaseUrl(); ?>/tickets/view/<?php echo $ticket['id']; ?>" class="btn btn-outline-primary">
                        <i class="fas fa-eye me-2"></i><?php echo __('review_ticket_details'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const confirmDeletionCheckbox = document.getElementById('confirm_deletion');
    const confirmRelatedDataCheckbox = document.getElementById('confirm_related_data');
    const understandPermanentCheckbox = document.getElementById('understand_permanent');
    const deleteButton = document.getElementById('delete_button');
    
    function updateDeleteButton() {
        const deletionConfirmed = confirmDeletionCheckbox.checked;
        const relatedDataConfirmed = confirmRelatedDataCheckbox ? confirmRelatedDataCheckbox.checked : true;
        const permanentUnderstood = understandPermanentCheckbox.checked;
        
        deleteButton.disabled = !(deletionConfirmed && relatedDataConfirmed && permanentUnderstood);
    }
    
    confirmDeletionCheckbox.addEventListener('change', updateDeleteButton);
    understandPermanentCheckbox.addEventListener('change', updateDeleteButton);
    
    if (confirmRelatedDataCheckbox) {
        confirmRelatedDataCheckbox.addEventListener('change', updateDeleteButton);
    }
    
    // Add confirmation dialog
    deleteButton.addEventListener('click', function(e) {
        const ticketTitle = '<?php echo addslashes($ticket['title']); ?>';
        if (!confirm(`<?php echo __('are_you_sure_delete_ticket'); ?> "${ticketTitle}"?`)) {
            e.preventDefault();
        }
    });
});
</script>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
