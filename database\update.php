<?php
/**
 * Database Update Script
 * 
 * This script updates the database structure for the Medical Device Management System.
 */

// Display errors
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set content type
header('Content-Type: text/html; charset=utf-8');

// Check if the script is being run from the command line
$isCli = php_sapi_name() === 'cli';

// Function to output messages
function output($message, $isError = false) {
    global $isCli;
    
    if ($isCli) {
        echo ($isError ? "ERROR: " : "") . $message . PHP_EOL;
    } else {
        echo ($isError ? '<div style="color: red; font-weight: bold;">ERROR: ' : '<div>') . $message . '</div>';
    }
}

// Function to get database configuration
function getDatabaseConfig() {
    // Check if config file exists
    if (!file_exists('../config/database.php')) {
        output("Database configuration file not found.", true);
        exit(1);
    }
    
    // Include database configuration
    include '../config/database.php';
    
    // Return database configuration
    return [
        'host' => DB_HOST,
        'name' => DB_NAME,
        'user' => DB_USER,
        'pass' => DB_PASS,
        'charset' => DB_CHARSET
    ];
}

// Function to check database version
function getDatabaseVersion($pdo) {
    try {
        // Check if settings table exists
        $stmt = $pdo->query("SHOW TABLES LIKE 'settings'");
        if (!$stmt->fetchColumn()) {
            return 0;
        }
        
        // Check if version setting exists
        $stmt = $pdo->query("SELECT setting_value FROM settings WHERE setting_key = 'db_version'");
        $version = $stmt->fetchColumn();
        
        return $version ? (int)$version : 1;
    } catch (PDOException $e) {
        output("Failed to check database version: " . $e->getMessage(), true);
        return 0;
    }
}

// Function to update database to version 2
function updateToVersion2($pdo) {
    try {
        output("Updating database to version 2...");
        
        // Add new columns to devices table
        $pdo->exec("
            ALTER TABLE devices
            ADD COLUMN calibration_date DATE NULL AFTER next_maintenance_date,
            ADD COLUMN calibration_interval INT NULL COMMENT 'Calibration interval in days' AFTER calibration_date,
            ADD COLUMN next_calibration_date DATE NULL AFTER calibration_interval,
            ADD COLUMN risk_level ENUM('low', 'medium', 'high') NOT NULL DEFAULT 'medium' AFTER next_calibration_date
        ");
        
        // Add new columns to maintenance_logs table
        $pdo->exec("
            ALTER TABLE maintenance_logs
            ADD COLUMN cost DECIMAL(10,2) NULL AFTER recommendations,
            ADD COLUMN downtime INT NULL COMMENT 'Downtime in hours' AFTER cost
        ");
        
        // Create device_attachments table
        $pdo->exec("
            CREATE TABLE device_attachments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                device_id INT NOT NULL,
                file_name VARCHAR(255) NOT NULL,
                file_path VARCHAR(255) NOT NULL,
                file_type VARCHAR(100) NOT NULL,
                file_size INT NOT NULL,
                description TEXT NULL,
                uploaded_by INT NOT NULL,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE,
                FOREIGN KEY (uploaded_by) REFERENCES users(id),
                INDEX (device_id)
            )
        ");
        
        // Create maintenance_log_attachments table
        $pdo->exec("
            CREATE TABLE maintenance_log_attachments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                maintenance_log_id INT NOT NULL,
                file_name VARCHAR(255) NOT NULL,
                file_path VARCHAR(255) NOT NULL,
                file_type VARCHAR(100) NOT NULL,
                file_size INT NOT NULL,
                description TEXT NULL,
                uploaded_by INT NOT NULL,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (maintenance_log_id) REFERENCES maintenance_logs(id) ON DELETE CASCADE,
                FOREIGN KEY (uploaded_by) REFERENCES users(id),
                INDEX (maintenance_log_id)
            )
        ");
        
        // Create ticket_attachments table
        $pdo->exec("
            CREATE TABLE ticket_attachments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                ticket_id INT NOT NULL,
                file_name VARCHAR(255) NOT NULL,
                file_path VARCHAR(255) NOT NULL,
                file_type VARCHAR(100) NOT NULL,
                file_size INT NOT NULL,
                description TEXT NULL,
                uploaded_by INT NOT NULL,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (ticket_id) REFERENCES tickets(id) ON DELETE CASCADE,
                FOREIGN KEY (uploaded_by) REFERENCES users(id),
                INDEX (ticket_id)
            )
        ");
        
        // Update db_version in settings
        $stmt = $pdo->prepare("
            INSERT INTO settings (setting_key, setting_value, description)
            VALUES ('db_version', '2', 'Database schema version')
            ON DUPLICATE KEY UPDATE setting_value = '2'
        ");
        $stmt->execute();
        
        output("Database updated to version 2 successfully.");
        
        return true;
    } catch (PDOException $e) {
        output("Failed to update database to version 2: " . $e->getMessage(), true);
        return false;
    }
}

// Main execution
if (!$isCli) {
    echo '<!DOCTYPE html>
    <html>
    <head>
        <title>Database Update</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 20px;
                line-height: 1.6;
            }
            h1 {
                color: #333;
            }
            div {
                margin-bottom: 10px;
            }
            .success {
                color: green;
            }
            .error {
                color: red;
                font-weight: bold;
            }
        </style>
    </head>
    <body>
        <h1>Medical Device Management System - Database Update</h1>';
}

output("Starting database update...");

// Get database configuration
$config = getDatabaseConfig();

try {
    // Connect to the database
    $pdo = new PDO(
        "mysql:host={$config['host']};dbname={$config['name']};charset={$config['charset']}",
        $config['user'],
        $config['pass'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
    
    // Check database version
    $version = getDatabaseVersion($pdo);
    
    if ($version === 0) {
        output("Database not found or not properly installed. Please run the installation script first.", true);
        exit(1);
    }
    
    output("Current database version: $version");
    
    // Update database based on version
    if ($version < 2) {
        if (!updateToVersion2($pdo)) {
            output("Update failed.", true);
            exit(1);
        }
    } else {
        output("Database is already up to date.");
    }
    
    output("Database update completed successfully.", false);
    
    if (!$isCli) {
        echo '<div class="success">You can now <a href="../index.php">login to the system</a>.</div>';
        echo '</body></html>';
    }
    
    exit(0);
} catch (PDOException $e) {
    output("Database connection failed: " . $e->getMessage(), true);
    
    if (!$isCli) {
        echo '</body></html>';
    }
    
    exit(1);
}
