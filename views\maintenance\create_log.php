<?php
/**
 * Create Maintenance Log View
 * 
 * This file displays the form to create a new maintenance log.
 */

// Set page title
$pageTitle = __('create_maintenance_log');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3"><?php echo __('create_maintenance_log'); ?></h1>
    
    <a href="<?php echo getBaseUrl(); ?>/maintenance/logs" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_logs'); ?>
    </a>
</div>

<?php if (!empty($errors)): ?>
    <div class="alert alert-danger">
        <h6><?php echo __('please_fix_errors'); ?>:</h6>
        <ul class="mb-0">
            <?php foreach ($errors as $field => $error): ?>
                <li><?php echo htmlspecialchars($error); ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clipboard-list me-2"></i><?php echo __('maintenance_log_details'); ?>
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?php echo getBaseUrl(); ?>/maintenance/create_log">
                    <div class="row">
                        <?php if (hasPermission('manage_hospitals')): ?>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="hospital_id" class="form-label"><?php echo __('hospital'); ?> <span class="text-danger">*</span></label>
                                    <select class="form-select <?php echo isset($errors['hospital_id']) ? 'is-invalid' : ''; ?>" 
                                            id="hospital_id" 
                                            name="hospital_id" 
                                            onchange="loadDevices(this.value)" 
                                            required>
                                        <option value=""><?php echo __('select_hospital'); ?></option>
                                        <?php foreach ($hospitals as $hospital): ?>
                                            <option value="<?php echo $hospital['id']; ?>" <?php echo ($logData['hospital_id'] ?? '') == $hospital['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($hospital['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <?php if (isset($errors['hospital_id'])): ?>
                                        <div class="invalid-feedback"><?php echo $errors['hospital_id']; ?></div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="device_id" class="form-label"><?php echo __('device'); ?> <span class="text-danger">*</span></label>
                                <select class="form-select <?php echo isset($errors['device_id']) ? 'is-invalid' : ''; ?>" 
                                        id="device_id" 
                                        name="device_id" 
                                        onchange="loadSchedules(this.value)" 
                                        required>
                                    <option value=""><?php echo __('select_device'); ?></option>
                                    <?php foreach ($devices as $device): ?>
                                        <option value="<?php echo $device['id']; ?>" <?php echo ($logData['device_id'] ?? '') == $device['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($device['name']); ?> - <?php echo htmlspecialchars($device['serial_number']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (isset($errors['device_id'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['device_id']; ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="maintenance_schedule_id" class="form-label"><?php echo __('related_schedule'); ?></label>
                                <select class="form-select <?php echo isset($errors['maintenance_schedule_id']) ? 'is-invalid' : ''; ?>" 
                                        id="maintenance_schedule_id" 
                                        name="maintenance_schedule_id">
                                    <option value=""><?php echo __('no_schedule_unscheduled'); ?></option>
                                    <?php foreach ($schedules as $schedule): ?>
                                        <option value="<?php echo $schedule['id']; ?>" <?php echo ($logData['maintenance_schedule_id'] ?? '') == $schedule['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($schedule['title']); ?> - <?php echo date('M d, Y', strtotime($schedule['scheduled_date'])); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (isset($errors['maintenance_schedule_id'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['maintenance_schedule_id']; ?></div>
                                <?php endif; ?>
                                <div class="form-text"><?php echo __('optional_link_to_schedule'); ?></div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="performed_date" class="form-label"><?php echo __('performed_date'); ?> <span class="text-danger">*</span></label>
                                <input type="date" 
                                       class="form-control <?php echo isset($errors['performed_date']) ? 'is-invalid' : ''; ?>" 
                                       id="performed_date" 
                                       name="performed_date" 
                                       value="<?php echo htmlspecialchars($logData['performed_date'] ?? ''); ?>" 
                                       max="<?php echo date('Y-m-d'); ?>"
                                       required>
                                <?php if (isset($errors['performed_date'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['performed_date']; ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status" class="form-label"><?php echo __('status'); ?> <span class="text-danger">*</span></label>
                                <select class="form-select <?php echo isset($errors['status']) ? 'is-invalid' : ''; ?>" 
                                        id="status" 
                                        name="status" 
                                        required>
                                    <option value="completed" <?php echo ($logData['status'] ?? 'completed') === 'completed' ? 'selected' : ''; ?>>
                                        <?php echo __('completed'); ?>
                                    </option>
                                    <option value="partial" <?php echo ($logData['status'] ?? '') === 'partial' ? 'selected' : ''; ?>>
                                        <?php echo __('partial'); ?>
                                    </option>
                                    <option value="failed" <?php echo ($logData['status'] ?? '') === 'failed' ? 'selected' : ''; ?>>
                                        <?php echo __('failed'); ?>
                                    </option>
                                    <option value="postponed" <?php echo ($logData['status'] ?? '') === 'postponed' ? 'selected' : ''; ?>>
                                        <?php echo __('postponed'); ?>
                                    </option>
                                </select>
                                <?php if (isset($errors['status'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['status']; ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="duration" class="form-label"><?php echo __('duration_minutes'); ?></label>
                                <input type="number" 
                                       class="form-control <?php echo isset($errors['duration']) ? 'is-invalid' : ''; ?>" 
                                       id="duration" 
                                       name="duration" 
                                       value="<?php echo htmlspecialchars($logData['duration'] ?? ''); ?>" 
                                       min="1" 
                                       max="1440"
                                       placeholder="<?php echo __('enter_duration'); ?>">
                                <?php if (isset($errors['duration'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['duration']; ?></div>
                                <?php endif; ?>
                                <div class="form-text"><?php echo __('optional_maintenance_duration'); ?></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label"><?php echo __('notes'); ?></label>
                        <textarea class="form-control <?php echo isset($errors['notes']) ? 'is-invalid' : ''; ?>" 
                                  id="notes" 
                                  name="notes" 
                                  rows="4" 
                                  placeholder="<?php echo __('describe_maintenance_performed'); ?>"><?php echo htmlspecialchars($logData['notes'] ?? ''); ?></textarea>
                        <?php if (isset($errors['notes'])): ?>
                            <div class="invalid-feedback"><?php echo $errors['notes']; ?></div>
                        <?php endif; ?>
                        <div class="form-text"><?php echo __('describe_work_performed'); ?></div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="<?php echo getBaseUrl(); ?>/maintenance/logs" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>
                        </a>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i><?php echo __('create_log'); ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- Quick Tips -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-lightbulb me-2"></i><?php echo __('quick_tips'); ?>
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <?php echo __('tip_select_device_first'); ?>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <?php echo __('tip_link_to_schedule'); ?>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <?php echo __('tip_detailed_notes'); ?>
                    </li>
                    <li>
                        <i class="fas fa-check text-success me-2"></i>
                        <?php echo __('tip_accurate_date'); ?>
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- Recent Logs -->
        <?php if (!empty($recentLogs ?? [])): ?>
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i><?php echo __('recent_logs'); ?>
                    </h6>
                </div>
                <div class="card-body">
                    <?php foreach (array_slice($recentLogs, 0, 3) as $recentLog): ?>
                        <div class="d-flex align-items-center mb-2">
                            <div class="me-2">
                                <span class="badge bg-<?php echo getMaintenanceStatusColor($recentLog['status']); ?> badge-sm">
                                    <?php echo __($recentLog['status']); ?>
                                </span>
                            </div>
                            <div class="flex-grow-1">
                                <div class="fw-bold small"><?php echo htmlspecialchars($recentLog['device_name']); ?></div>
                                <small class="text-muted"><?php echo date('M d', strtotime($recentLog['performed_date'])); ?></small>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
// Load devices based on selected hospital
function loadDevices(hospitalId) {
    const deviceSelect = document.getElementById('device_id');
    const scheduleSelect = document.getElementById('maintenance_schedule_id');
    
    // Clear current options
    deviceSelect.innerHTML = '<option value=""><?php echo __('loading'); ?>...</option>';
    scheduleSelect.innerHTML = '<option value=""><?php echo __('no_schedule_unscheduled'); ?></option>';
    
    if (hospitalId) {
        fetch(`<?php echo getBaseUrl(); ?>/api/devices?hospital_id=${hospitalId}`)
            .then(response => response.json())
            .then(data => {
                deviceSelect.innerHTML = '<option value=""><?php echo __('select_device'); ?></option>';
                data.forEach(device => {
                    deviceSelect.innerHTML += `<option value="${device.id}">${device.name} - ${device.serial_number}</option>`;
                });
            })
            .catch(error => {
                console.error('Error loading devices:', error);
                deviceSelect.innerHTML = '<option value=""><?php echo __('error_loading_devices'); ?></option>';
            });
    } else {
        deviceSelect.innerHTML = '<option value=""><?php echo __('select_device'); ?></option>';
    }
}

// Load schedules based on selected device
function loadSchedules(deviceId) {
    const scheduleSelect = document.getElementById('maintenance_schedule_id');
    
    // Clear current options
    scheduleSelect.innerHTML = '<option value=""><?php echo __('loading'); ?>...</option>';
    
    if (deviceId) {
        fetch(`<?php echo getBaseUrl(); ?>/api/maintenance/schedules?device_id=${deviceId}`)
            .then(response => response.json())
            .then(data => {
                scheduleSelect.innerHTML = '<option value=""><?php echo __('no_schedule_unscheduled'); ?></option>';
                data.forEach(schedule => {
                    const date = new Date(schedule.scheduled_date).toLocaleDateString();
                    scheduleSelect.innerHTML += `<option value="${schedule.id}">${schedule.title} - ${date}</option>`;
                });
            })
            .catch(error => {
                console.error('Error loading schedules:', error);
                scheduleSelect.innerHTML = '<option value=""><?php echo __('error_loading_schedules'); ?></option>';
            });
    } else {
        scheduleSelect.innerHTML = '<option value=""><?php echo __('no_schedule_unscheduled'); ?></option>';
    }
}
</script>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
