<?php
/**
 * Devices List View
 * 
 * This file displays the list of devices.
 */

// Set page title
$pageTitle = __('devices');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 text-gradient mb-1"><?php echo __('devices_management'); ?></h1>
        <p class="text-muted mb-0"><?php echo __('manage_all_medical_devices'); ?></p>
    </div>

    <div class="d-flex gap-2">
        <?php if (hasPermission('manage_devices')): ?>
        <a href="<?php echo getBaseUrl(); ?>/devices/create" class="btn btn-primary">
            <?php echo __('add_device'); ?>
        </a>
        <?php endif; ?>

        <div class="dropdown">
            <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <?php echo __('export'); ?>
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="<?php echo getBaseUrl(); ?>/devices/export?format=pdf">
                    PDF Report
                </a></li>
                <li><a class="dropdown-item" href="<?php echo getBaseUrl(); ?>/devices/export?format=excel">
                    Excel Spreadsheet
                </a></li>
                <li><a class="dropdown-item" href="<?php echo getBaseUrl(); ?>/devices/export?format=csv">
                    CSV Data
                </a></li>
            </ul>
        </div>
    </div>
</div>

<!-- Enhanced Filters -->
<div class="card card-glass mb-4 fade-in-up">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <?php echo __('filters'); ?>
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" action="<?php echo getBaseUrl(); ?>/devices" class="row g-3" id="filterForm">
            <div class="col-md-3">
                <label for="hospital_id" class="form-label"><?php echo __('hospital'); ?></label>
                <select class="form-select" id="hospital_id" name="hospital_id">
                    <option value=""><?php echo __('all_hospitals'); ?></option>
                    <?php foreach ($hospitals as $hospital): ?>
                        <option value="<?php echo $hospital['id']; ?>" <?php echo ($hospitalId == $hospital['id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($hospital['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="department_id" class="form-label"><?php echo __('department'); ?></label>
                <select class="form-select" id="department_id" name="department_id">
                    <option value=""><?php echo __('all_departments'); ?></option>
                    <?php foreach ($departments as $department): ?>
                        <option value="<?php echo $department['id']; ?>" <?php echo ($departmentId == $department['id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($department['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="status" class="form-label"><?php echo __('status'); ?></label>
                <select class="form-select" id="status" name="status">
                    <option value=""><?php echo __('all_statuses'); ?></option>
                    <option value="operational" <?php echo ($status == 'operational') ? 'selected' : ''; ?>><?php echo __('operational'); ?></option>
                    <option value="under_maintenance" <?php echo ($status == 'under_maintenance') ? 'selected' : ''; ?>><?php echo __('under_maintenance'); ?></option>
                    <option value="out_of_order" <?php echo ($status == 'out_of_order') ? 'selected' : ''; ?>><?php echo __('out_of_order'); ?></option>
                    <option value="retired" <?php echo ($status == 'retired') ? 'selected' : ''; ?>><?php echo __('retired'); ?></option>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="search" class="form-label"><?php echo __('search'); ?></label>
                <div class="input-group">
                    <input type="text" class="form-control" id="search" name="search" value="<?php echo htmlspecialchars($search ?? ''); ?>" placeholder="<?php echo __('search_devices'); ?>">
                    <button class="btn btn-primary" type="submit">
                        Search
                    </button>
                </div>
            </div>

            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearFilters()">
                        <?php echo __('clear_filters'); ?>
                    </button>
                    <small class="text-muted">
                        <?php echo count($devices); ?> <?php echo __('devices_found'); ?>
                    </small>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Enhanced Devices Table -->
<div class="card fade-in-up" style="animation-delay: 0.2s;">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <?php echo __('devices_list'); ?>
            </h5>
            <div class="d-flex gap-2">
                <button class="btn btn-sm btn-outline-primary active" onclick="toggleView('table')" id="tableViewBtn">
                    <?php echo __('table'); ?> View
                </button>
                <button class="btn btn-sm btn-outline-primary" onclick="toggleView('grid')" id="gridViewBtn">
                    <?php echo __('grid'); ?> View
                </button>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <!-- Table View -->
        <div class="table-responsive" id="tableView">
            <table class="table table-hover mb-0" id="devices-table">
                <thead class="table-light">
                    <tr>
                        <th><?php echo __('name'); ?></th>
                        <th><?php echo __('model'); ?></th>
                        <th><?php echo __('serial_number'); ?></th>
                        <th><?php echo __('hospital'); ?></th>
                        <th><?php echo __('department'); ?></th>
                        <th><?php echo __('status'); ?></th>
                        <th><?php echo __('warranty'); ?></th>
                        <th><?php echo __('actions'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($devices)): ?>
                        <tr>
                            <td colspan="8" class="text-center py-5">
                                <div class="text-muted">
                                    <h5><?php echo __('no_devices'); ?></h5>
                                    <p><?php echo __('no_devices_found_message'); ?></p>
                                    <?php if (hasPermission('manage_devices')): ?>
                                        <a href="<?php echo getBaseUrl(); ?>/devices/create" class="btn btn-primary">
                                            <?php echo __('add_first_device'); ?>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($devices as $device): ?>
                            <tr class="device-row" data-device-id="<?php echo $device['id']; ?>">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div>
                                            <strong><?php echo htmlspecialchars($device['name']); ?></strong>
                                            <?php if (!empty($device['description'])): ?>
                                                <br><small class="text-muted"><?php echo htmlspecialchars(substr($device['description'], 0, 50)); ?>...</small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-light text-dark"><?php echo htmlspecialchars($device['model']); ?></span>
                                    <?php if (!empty($device['manufacturer'])): ?>
                                        <br><small class="text-muted"><?php echo htmlspecialchars($device['manufacturer']); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <code class="bg-light px-2 py-1 rounded"><?php echo htmlspecialchars($device['serial_number']); ?></code>
                                </td>
                                <td>
                                    <?php if (hasPermission('view_hospitals')): ?>
                                        <a href="<?php echo getBaseUrl(); ?>/hospitals/view/<?php echo $device['hospital_id']; ?>" class="text-decoration-none">
                                            <?php echo htmlspecialchars($device['hospital_name']); ?>
                                        </a>
                                    <?php else: ?>
                                        <?php echo htmlspecialchars($device['hospital_name']); ?>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (hasPermission('view_departments')): ?>
                                        <a href="<?php echo getBaseUrl(); ?>/departments/view/<?php echo $device['department_id']; ?>" class="text-decoration-none">
                                            <?php echo htmlspecialchars($device['department_name']); ?>
                                        </a>
                                    <?php else: ?>
                                        <?php echo htmlspecialchars($device['department_name']); ?>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo getStatusColor($device['status']); ?> d-flex align-items-center" style="width: fit-content;">
                                        <?php echo __($device['status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <?php
                                    $warrantyExpiry = new DateTime($device['warranty_expiry']);
                                    $now = new DateTime();
                                    $isExpired = $warrantyExpiry < $now;
                                    $daysUntilExpiry = $now->diff($warrantyExpiry)->days;
                                    $isExpiringSoon = !$isExpired && $daysUntilExpiry <= 30;
                                    ?>
                                    <div class="warranty-info">
                                        <span class="<?php echo $isExpired ? 'text-danger' : ($isExpiringSoon ? 'text-warning' : 'text-success'); ?>">
                                            <?php echo $warrantyExpiry->format('M d, Y'); ?>
                                        </span>
                                        <?php if ($isExpired): ?>
                                            <br><small class="text-danger"><?php echo __('expired'); ?></small>
                                        <?php elseif ($isExpiringSoon): ?>
                                            <br><small class="text-warning"><?php echo $daysUntilExpiry; ?> <?php echo __('days_left'); ?></small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo getBaseUrl(); ?>/devices/view/<?php echo $device['id']; ?>"
                                           class="btn btn-sm btn-primary"
                                           data-bs-toggle="tooltip"
                                           title="<?php echo __('view_device'); ?>">
                                            View
                                        </a>

                                        <?php if (hasPermission('manage_devices')): ?>
                                        <a href="<?php echo getBaseUrl(); ?>/devices/edit/<?php echo $device['id']; ?>"
                                           class="btn btn-sm btn-warning"
                                           data-bs-toggle="tooltip"
                                           title="<?php echo __('edit_device'); ?>">
                                            Edit
                                        </a>

                                        <a href="<?php echo getBaseUrl(); ?>/devices/qrcode/<?php echo $device['id']; ?>"
                                           class="btn btn-sm btn-info"
                                           data-bs-toggle="tooltip"
                                           title="<?php echo __('generate_qr_code'); ?>">
                                            QR Code
                                        </a>

                                        <button type="button"
                                                class="btn btn-sm btn-danger"
                                                onclick="confirmDelete(<?php echo $device['id']; ?>, '<?php echo htmlspecialchars($device['name']); ?>')"
                                                data-bs-toggle="tooltip"
                                                title="<?php echo __('delete_device'); ?>">
                                            Delete
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Grid View -->
        <div class="row g-3 p-3" id="gridView" style="display: none;">
            <?php if (!empty($devices)): ?>
                <?php foreach ($devices as $device): ?>
                    <div class="col-lg-4 col-md-6">
                        <div class="card device-card h-100 shadow-sm">
                            <div class="card-header bg-light">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="card-title mb-0">
                                        <?php echo htmlspecialchars($device['name']); ?>
                                    </h6>
                                    <span class="badge bg-<?php echo getStatusColor($device['status']); ?>">
                                        <?php echo __($device['status']); ?>
                                    </span>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row g-2 mb-3">
                                    <div class="col-6">
                                        <small class="text-muted"><?php echo __('model'); ?></small>
                                        <div><?php echo htmlspecialchars($device['model']); ?></div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted"><?php echo __('serial_number'); ?></small>
                                        <div><code class="small"><?php echo htmlspecialchars($device['serial_number']); ?></code></div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <small class="text-muted"><?php echo __('location'); ?></small>
                                    <div>
                                        <?php echo htmlspecialchars($device['hospital_name']); ?>
                                        <br>
                                        <?php echo htmlspecialchars($device['department_name']); ?>
                                    </div>
                                </div>

                                <?php
                                $warrantyExpiry = new DateTime($device['warranty_expiry']);
                                $now = new DateTime();
                                $isExpired = $warrantyExpiry < $now;
                                ?>
                                <div class="mb-3">
                                    <small class="text-muted"><?php echo __('warranty'); ?></small>
                                    <div class="<?php echo $isExpired ? 'text-danger' : 'text-success'; ?>">
                                        <?php echo $warrantyExpiry->format('M d, Y'); ?>
                                        <?php if ($isExpired): ?>
                                            <span class="badge bg-danger ms-1"><?php echo __('expired'); ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent">
                                <div class="d-flex justify-content-between">
                                    <a href="<?php echo getBaseUrl(); ?>/devices/view/<?php echo $device['id']; ?>" class="btn btn-sm btn-primary">
                                        View Details
                                    </a>
                                    <?php if (hasPermission('manage_devices')): ?>
                                    <div class="btn-group">
                                        <a href="<?php echo getBaseUrl(); ?>/devices/edit/<?php echo $device['id']; ?>" class="btn btn-sm btn-warning">
                                            Edit
                                        </a>
                                        <a href="<?php echo getBaseUrl(); ?>/devices/qrcode/<?php echo $device['id']; ?>" class="btn btn-sm btn-info">
                                            QR Code
                                        </a>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo __('confirm_delete'); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p><?php echo __('delete_device_confirm'); ?></p>
                <p><strong id="deviceName"></strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo __('cancel'); ?></button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <button type="submit" class="btn btn-danger"><?php echo __('delete'); ?></button>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
// Add enhanced scripts
$scripts = '
<script>
// Initialize DataTables
$(document).ready(function() {
    $("#devices-table").DataTable({
        responsive: true,
        pageLength: 25,
        order: [[0, "asc"]],
        language: {
            search: "' . __('search') . ':",
            lengthMenu: "' . __('show') . ' _MENU_ ' . __('entries') . '",
            info: "' . __('showing') . ' _START_ ' . __('to') . ' _END_ ' . __('of') . ' _TOTAL_ ' . __('entries') . '",
            paginate: {
                first: "' . __('first') . '",
                last: "' . __('last') . '",
                next: "' . __('next') . '",
                previous: "' . __('previous') . '"
            }
        },
        columnDefs: [
            { orderable: false, targets: -1 } // Disable sorting on actions column
        ]
    });

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll("[data-bs-toggle=\"tooltip\"]"));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Auto-submit form when filters change
    $("#hospital_id, #department_id, #status").on("change", function() {
        showLoadingSpinner();
        this.form.submit();
    });

    // Enhanced search with debounce
    let searchTimeout;
    $("#search").on("input", function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            if (this.value.length >= 3 || this.value.length === 0) {
                showLoadingSpinner();
                this.form.submit();
            }
        }, 500);
    });
});

// View toggle functionality
function toggleView(viewType) {
    const tableView = document.getElementById("tableView");
    const gridView = document.getElementById("gridView");
    const tableBtn = document.getElementById("tableViewBtn");
    const gridBtn = document.getElementById("gridViewBtn");

    if (viewType === "table") {
        tableView.style.display = "block";
        gridView.style.display = "none";
        tableBtn.classList.add("active");
        gridBtn.classList.remove("active");
        localStorage.setItem("devicesView", "table");
    } else {
        tableView.style.display = "none";
        gridView.style.display = "block";
        gridBtn.classList.add("active");
        tableBtn.classList.remove("active");
        localStorage.setItem("devicesView", "grid");
    }
}

// Restore saved view preference
document.addEventListener("DOMContentLoaded", function() {
    const savedView = localStorage.getItem("devicesView");
    if (savedView === "grid") {
        toggleView("grid");
    }
});

// Delete confirmation
function confirmDelete(deviceId, deviceName) {
    document.getElementById("deviceName").textContent = deviceName;
    document.getElementById("deleteForm").action = "' . getBaseUrl() . '/devices/delete/" + deviceId;
    new bootstrap.Modal(document.getElementById("deleteModal")).show();
}

// Clear filters
function clearFilters() {
    const form = document.getElementById("filterForm");
    form.querySelectorAll("select, input[type=text]").forEach(element => {
        element.value = "";
    });
    showLoadingSpinner();
    form.submit();
}

// Loading spinner
function showLoadingSpinner() {
    const spinner = document.createElement("div");
    spinner.className = "d-flex justify-content-center align-items-center position-fixed top-0 start-0 w-100 h-100";
    spinner.style.backgroundColor = "rgba(0,0,0,0.5)";
    spinner.style.zIndex = "9999";
    spinner.innerHTML = `
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">' . __('loading') . '...</span>
        </div>
    `;
    document.body.appendChild(spinner);
}

// Enhanced device row interactions
document.addEventListener("DOMContentLoaded", function() {
    const deviceRows = document.querySelectorAll(".device-row");
    deviceRows.forEach(row => {
        row.addEventListener("mouseenter", function() {
            this.style.transform = "scale(1.01)";
            this.style.transition = "transform 0.2s ease";
        });

        row.addEventListener("mouseleave", function() {
            this.style.transform = "scale(1)";
        });
    });
});

// Keyboard shortcuts
document.addEventListener("keydown", function(e) {
    // Ctrl/Cmd + N for new device
    if ((e.ctrlKey || e.metaKey) && e.key === "n") {
        e.preventDefault();
        const addBtn = document.querySelector("a[href*=\"/devices/create\"]");
        if (addBtn) addBtn.click();
    }

    // Ctrl/Cmd + F for search
    if ((e.ctrlKey || e.metaKey) && e.key === "f") {
        e.preventDefault();
        document.getElementById("search").focus();
    }
});
</script>
';
?>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
