<?php
/**
 * Database Migration Script
 * 
 * This script updates existing database installations with new tables and fields.
 */

// Start session
session_start();

// Set default timezone
date_default_timezone_set('UTC');

// Load configuration
require_once __DIR__ . '/../config/database.php';

// Load includes
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/auth.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !hasPermission('manage_system')) {
    die('Access denied. Admin privileges required.');
}

// Get current database version
function getCurrentVersion($pdo) {
    try {
        $stmt = $pdo->query("SELECT setting_value FROM settings WHERE setting_key = 'db_version'");
        $result = $stmt->fetch();
        return $result ? $result['setting_value'] : '1.0.0';
    } catch (PDOException $e) {
        return '1.0.0';
    }
}

// Set database version
function setVersion($pdo, $version) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO settings (setting_key, setting_value, description)
            VALUES ('db_version', ?, 'Database schema version')
            ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
        ");
        return $stmt->execute([$version]);
    } catch (PDOException $e) {
        error_log("Set Version Error: " . $e->getMessage());
        return false;
    }
}

// Check if table exists
function tableExists($pdo, $tableName) {
    try {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$tableName]);
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

// Check if column exists
function columnExists($pdo, $tableName, $columnName) {
    try {
        $stmt = $pdo->prepare("SHOW COLUMNS FROM `{$tableName}` LIKE ?");
        $stmt->execute([$columnName]);
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

// Migration functions
function migrate_1_0_1($pdo) {
    $migrations = [];
    
    // Add remember_tokens table
    if (!tableExists($pdo, 'remember_tokens')) {
        $migrations[] = "
            CREATE TABLE remember_tokens (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                token VARCHAR(255) NOT NULL UNIQUE,
                expires_at DATETIME NOT NULL,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )
        ";
    }
    
    // Add password_resets table
    if (!tableExists($pdo, 'password_resets')) {
        $migrations[] = "
            CREATE TABLE password_resets (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                token VARCHAR(255) NOT NULL UNIQUE,
                expires_at DATETIME NOT NULL,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                UNIQUE KEY unique_user_reset (user_id)
            )
        ";
    }
    
    // Add missing columns to devices table
    if (!columnExists($pdo, 'devices', 'location')) {
        $migrations[] = "ALTER TABLE devices ADD COLUMN location VARCHAR(100) NULL AFTER qr_code";
    }
    
    if (!columnExists($pdo, 'devices', 'notes')) {
        $migrations[] = "ALTER TABLE devices ADD COLUMN notes TEXT NULL AFTER location";
    }
    
    // Add permissions column to users table if it doesn't exist
    if (!columnExists($pdo, 'users', 'permissions')) {
        $migrations[] = "ALTER TABLE users ADD COLUMN permissions JSON NULL AFTER language";
    }
    
    return $migrations;
}

function migrate_1_0_2($pdo) {
    $migrations = [];
    
    // Add any future migrations here
    
    return $migrations;
}

// Main migration process
try {
    $currentVersion = getCurrentVersion($pdo);
    $targetVersion = '1.0.1';
    
    echo "<h2>Database Migration</h2>";
    echo "<p>Current version: {$currentVersion}</p>";
    echo "<p>Target version: {$targetVersion}</p>";
    
    if (version_compare($currentVersion, $targetVersion, '>=')) {
        echo "<p style='color: green;'>Database is already up to date.</p>";
        exit;
    }
    
    // Start transaction
    $pdo->beginTransaction();
    
    $allMigrations = [];
    
    // Apply migrations based on current version
    if (version_compare($currentVersion, '1.0.1', '<')) {
        $allMigrations = array_merge($allMigrations, migrate_1_0_1($pdo));
    }
    
    if (version_compare($currentVersion, '1.0.2', '<')) {
        $allMigrations = array_merge($allMigrations, migrate_1_0_2($pdo));
    }
    
    // Execute migrations
    if (!empty($allMigrations)) {
        echo "<h3>Applying Migrations:</h3>";
        echo "<ul>";
        
        foreach ($allMigrations as $migration) {
            try {
                $pdo->exec($migration);
                echo "<li style='color: green;'>✓ " . substr($migration, 0, 50) . "...</li>";
            } catch (PDOException $e) {
                echo "<li style='color: red;'>✗ Error: " . $e->getMessage() . "</li>";
                throw $e;
            }
        }
        
        echo "</ul>";
    } else {
        echo "<p>No migrations needed.</p>";
    }
    
    // Update version
    if (setVersion($pdo, $targetVersion)) {
        echo "<p style='color: green;'>Database version updated to {$targetVersion}</p>";
    } else {
        throw new Exception("Failed to update database version");
    }
    
    // Commit transaction
    $pdo->commit();
    
    echo "<h3 style='color: green;'>Migration completed successfully!</h3>";
    echo "<p><a href='../index.php'>Return to Application</a></p>";
    
} catch (Exception $e) {
    // Rollback transaction
    if ($pdo->inTransaction()) {
        $pdo->rollback();
    }
    
    echo "<h3 style='color: red;'>Migration failed!</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check the error log and try again.</p>";
    
    error_log("Migration Error: " . $e->getMessage());
}
?>
