<?php
/**
 * Dashboard Controller
 *
 * This file handles dashboard-related operations.
 */

// Check if the user is logged in
requireLogin();

// Get the current user
$currentUser = getCurrentUser();
$hospitalId = $currentUser['hospital_id'];

// Get statistics based on user's hospital
$stats = [
    'hospitals' => 0,
    'departments' => 0,
    'devices' => 0,
    'devices_operational' => 0,
    'devices_maintenance' => 0,
    'devices_out_of_order' => 0,
    'devices_retired' => 0,
    'maintenance_scheduled' => 0,
    'maintenance_overdue' => 0,
    'tickets_open' => 0,
    'tickets_in_progress' => 0,
    'tickets_resolved' => 0,
    'tickets_closed' => 0,
    'users' => 0
];

// Get hospital count (admin only)
if (hasPermission('manage_hospitals')) {
    $stats['hospitals'] = $hospitalModel->count();
}

// Get department count
if ($hospitalId) {
    $stats['departments'] = $departmentModel->count($hospitalId);
} else {
    $stats['departments'] = $departmentModel->count();
}

// Get device counts
if ($hospitalId) {
    $stats['devices'] = $deviceModel->count($hospitalId);
    $stats['devices_operational'] = $deviceModel->countByStatus('operational', $hospitalId);
    $stats['devices_maintenance'] = $deviceModel->countByStatus('under_maintenance', $hospitalId);
    $stats['devices_out_of_order'] = $deviceModel->countByStatus('out_of_order', $hospitalId);
    $stats['devices_retired'] = $deviceModel->countByStatus('retired', $hospitalId);
} else {
    $stats['devices'] = $deviceModel->count();
    $stats['devices_operational'] = $deviceModel->countByStatus('operational');
    $stats['devices_maintenance'] = $deviceModel->countByStatus('under_maintenance');
    $stats['devices_out_of_order'] = $deviceModel->countByStatus('out_of_order');
    $stats['devices_retired'] = $deviceModel->countByStatus('retired');
}

// Get ticket counts
if ($hospitalId) {
    $stats['tickets_open'] = $ticketModel->countByStatus('open', $hospitalId);
    $stats['tickets_in_progress'] = $ticketModel->countByStatus('in_progress', $hospitalId);
    $stats['tickets_resolved'] = $ticketModel->countByStatus('resolved', $hospitalId);
    $stats['tickets_closed'] = $ticketModel->countByStatus('closed', $hospitalId);
} else {
    $stats['tickets_open'] = $ticketModel->countByStatus('open');
    $stats['tickets_in_progress'] = $ticketModel->countByStatus('in_progress');
    $stats['tickets_resolved'] = $ticketModel->countByStatus('resolved');
    $stats['tickets_closed'] = $ticketModel->countByStatus('closed');
}

// Get user count (admin only)
if (hasPermission('manage_users')) {
    if ($hospitalId) {
        $stats['users'] = $userModel->count($hospitalId);
    } else {
        $stats['users'] = $userModel->count();
    }
}

// Get recent tickets
if ($currentUser['role'] === 'staff') {
    // Staff can only see their own tickets
    $recentTickets = $ticketModel->getAll(null, $hospitalId, $currentUser['id']);
} elseif ($currentUser['role'] === 'technician' || $currentUser['role'] === 'engineer') {
    // Technicians and engineers can see tickets assigned to them and open tickets
    $assignedTickets = $ticketModel->getAll(null, $hospitalId, null, $currentUser['id']);
    $openTickets = $ticketModel->getByStatus('open', $hospitalId);
    $recentTickets = array_merge($assignedTickets, $openTickets);

    // Remove duplicates
    $uniqueTickets = [];
    foreach ($recentTickets as $ticket) {
        $uniqueTickets[$ticket['id']] = $ticket;
    }
    $recentTickets = array_values($uniqueTickets);

    // Sort by created_at (newest first)
    usort($recentTickets, function($a, $b) {
        return strtotime($b['created_at']) - strtotime($a['created_at']);
    });

    // Limit to 10 tickets
    $recentTickets = array_slice($recentTickets, 0, 10);
} else {
    // Admin can see all tickets
    $recentTickets = $ticketModel->getAll(null, $hospitalId);
    $recentTickets = array_slice($recentTickets, 0, 10);
}

// Get upcoming maintenance
$upcomingMaintenance = $maintenanceModel->getUpcomingSchedules(7, $hospitalId);
$upcomingMaintenance = array_slice($upcomingMaintenance, 0, 5);

// Get overdue maintenance
$overdueMaintenance = $maintenanceModel->getOverdueSchedules($hospitalId);
$overdueMaintenance = array_slice($overdueMaintenance, 0, 5);

// Include the dashboard view
include 'views/dashboard/index.php';
