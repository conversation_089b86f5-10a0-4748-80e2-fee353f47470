<?php
/**
 * Email Functions
 *
 * This file contains functions for sending emails.
 */

/**
 * Send an email
 *
 * @param string $to The recipient email address
 * @param string $subject The email subject
 * @param string $message The email message (HTML)
 * @param string $from The sender email address
 * @param string $fromName The sender name
 * @param array $attachments Optional attachments
 * @return bool True if email was sent, false otherwise
 */
function sendEmail($to, $subject, $message, $from = null, $fromName = null, $attachments = []) {
    // Get system settings
    global $pdo;
    $stmt = $pdo->query("SELECT * FROM settings WHERE setting_key IN ('email_from', 'email_from_name', 'smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_encryption')");
    $settings = [];
    while ($row = $stmt->fetch()) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }

    // Set default from email and name if not provided
    $from = $from ?? ($settings['email_from'] ?? '<EMAIL>');
    $fromName = $fromName ?? ($settings['email_from_name'] ?? 'Medical Device Management System');

    // Check if PHPMailer is available
    if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
        return sendEmailWithPHPMailer($to, $subject, $message, $from, $fromName, $attachments, $settings);
    } else {
        return sendEmailWithPHP($to, $subject, $message, $from, $fromName, $attachments);
    }
}

/**
 * Send an email using PHPMailer
 *
 * @param string $to The recipient email address
 * @param string $subject The email subject
 * @param string $message The email message (HTML)
 * @param string $from The sender email address
 * @param string $fromName The sender name
 * @param array $attachments Optional attachments
 * @param array $settings SMTP settings
 * @return bool True if email was sent, false otherwise
 */
function sendEmailWithPHPMailer($to, $subject, $message, $from, $fromName, $attachments = [], $settings = []) {
    // Include PHPMailer
    require_once 'vendor/autoload.php';

    // Create a new PHPMailer instance
    $mail = new PHPMailer\PHPMailer\PHPMailer(true);

    try {
        // Set up SMTP if settings are available
        if (!empty($settings['smtp_host'])) {
            $mail->isSMTP();
            $mail->Host = $settings['smtp_host'];
            $mail->Port = $settings['smtp_port'] ?? 587;

            // Set authentication if username and password are provided
            if (!empty($settings['smtp_username'])) {
                $mail->SMTPAuth = true;
                $mail->Username = $settings['smtp_username'];
                $mail->Password = $settings['smtp_password'];
            }

            // Set encryption if specified
            if (!empty($settings['smtp_encryption'])) {
                $mail->SMTPSecure = $settings['smtp_encryption'];
            }
        }

        // Set sender
        $mail->setFrom($from, $fromName);

        // Add recipient
        $mail->addAddress($to);

        // Set email format to HTML
        $mail->isHTML(true);

        // Set subject and body
        $mail->Subject = $subject;
        $mail->Body = $message;
        $mail->AltBody = strip_tags(str_replace(['<br>', '<br/>', '<br />'], "\n", $message));

        // Add attachments
        foreach ($attachments as $attachment) {
            if (is_array($attachment) && isset($attachment['path']) && isset($attachment['name'])) {
                $mail->addAttachment($attachment['path'], $attachment['name']);
            } elseif (is_string($attachment) && file_exists($attachment)) {
                $mail->addAttachment($attachment);
            }
        }

        // Send the email
        $mail->send();

        return true;
    } catch (Exception $e) {
        error_log("Email Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Send an email using PHP mail() function
 *
 * @param string $to The recipient email address
 * @param string $subject The email subject
 * @param string $message The email message (HTML)
 * @param string $from The sender email address
 * @param string $fromName The sender name
 * @param array $attachments Optional attachments
 * @return bool True if email was sent, false otherwise
 */
function sendEmailWithPHP($to, $subject, $message, $from, $fromName, $attachments = []) {
    // Generate a boundary for the email parts
    $boundary = md5(time());

    // Set headers
    $headers = [
        'MIME-Version: 1.0',
        'Content-Type: multipart/mixed; boundary="' . $boundary . '"',
        'From: ' . $fromName . ' <' . $from . '>',
        'Reply-To: ' . $from,
        'X-Mailer: PHP/' . phpversion()
    ];

    // Start the message
    $body = '--' . $boundary . PHP_EOL;
    $body .= 'Content-Type: text/html; charset=utf-8' . PHP_EOL;
    $body .= 'Content-Transfer-Encoding: 7bit' . PHP_EOL . PHP_EOL;
    $body .= $message . PHP_EOL . PHP_EOL;

    // Add attachments
    foreach ($attachments as $attachment) {
        if (is_array($attachment) && isset($attachment['path']) && isset($attachment['name']) && file_exists($attachment['path'])) {
            $file = $attachment['path'];
            $name = $attachment['name'];
            $content = file_get_contents($file);
            $content = chunk_split(base64_encode($content));

            $body .= '--' . $boundary . PHP_EOL;
            $body .= 'Content-Type: application/octet-stream; name="' . $name . '"' . PHP_EOL;
            $body .= 'Content-Transfer-Encoding: base64' . PHP_EOL;
            $body .= 'Content-Disposition: attachment; filename="' . $name . '"' . PHP_EOL . PHP_EOL;
            $body .= $content . PHP_EOL . PHP_EOL;
        } elseif (is_string($attachment) && file_exists($attachment)) {
            $file = $attachment;
            $name = basename($file);
            $content = file_get_contents($file);
            $content = chunk_split(base64_encode($content));

            $body .= '--' . $boundary . PHP_EOL;
            $body .= 'Content-Type: application/octet-stream; name="' . $name . '"' . PHP_EOL;
            $body .= 'Content-Transfer-Encoding: base64' . PHP_EOL;
            $body .= 'Content-Disposition: attachment; filename="' . $name . '"' . PHP_EOL . PHP_EOL;
            $body .= $content . PHP_EOL . PHP_EOL;
        }
    }

    // End the message
    $body .= '--' . $boundary . '--';

    // Send the email
    return mail($to, $subject, $body, implode(PHP_EOL, $headers));
}

/**
 * Send a notification email
 *
 * @param string $to The recipient email address
 * @param string $subject The email subject
 * @param string $message The email message
 * @param string $type The notification type
 * @param int $referenceId The reference ID
 * @return bool True if email was sent, false otherwise
 */
function sendNotificationEmail($to, $subject, $message, $type, $referenceId) {
    // Get the base URL
    $baseUrl = getBaseUrl();

    // Set the link based on notification type
    $link = $baseUrl;
    switch ($type) {
        case 'ticket':
            $link = $baseUrl . '/tickets/view/' . $referenceId;
            break;
        case 'maintenance_schedule':
            $link = $baseUrl . '/maintenance/view_schedule/' . $referenceId;
            break;
        case 'maintenance_log':
            $link = $baseUrl . '/maintenance/view_log/' . $referenceId;
            break;
        case 'device':
            $link = $baseUrl . '/devices/view/' . $referenceId;
            break;
        case 'user':
            $link = $baseUrl . '/users/view/' . $referenceId;
            break;
    }

    // Create HTML email template
    $html = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>' . $subject . '</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                margin: 0;
                padding: 0;
            }
            .container {
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
            }
            .header {
                background-color: #007bff;
                color: #fff;
                padding: 20px;
                text-align: center;
            }
            .content {
                padding: 20px;
                background-color: #f9f9f9;
                border: 1px solid #ddd;
            }
            .footer {
                text-align: center;
                margin-top: 20px;
                font-size: 12px;
                color: #777;
            }
            .button {
                display: inline-block;
                background-color: #007bff;
                color: #fff;
                padding: 10px 20px;
                text-decoration: none;
                border-radius: 4px;
                margin-top: 20px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>' . $subject . '</h1>
            </div>
            <div class="content">
                <p>' . nl2br($message) . '</p>
                <p><a href="' . $link . '" class="button">' . __('view_details') . '</a></p>
            </div>
            <div class="footer">
                <p>' . __('email_footer_text') . '</p>
                <p>&copy; ' . date('Y') . ' ' . __('app_name') . '</p>
            </div>
        </div>
    </body>
    </html>
    ';

    // Send the email
    return sendEmail($to, $subject, $html);
}

/**
 * Send a password reset email
 *
 * @param string $to The recipient email address
 * @param string $name The recipient name
 * @param string $resetLink The reset link
 * @return bool True if email was sent, false otherwise
 */
function sendPasswordResetEmail($to, $name, $resetLink) {
    // Get the base URL
    $baseUrl = getBaseUrl();

    // Set the subject
    $subject = __('password_reset_subject');

    // Create HTML email template
    $html = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>' . $subject . '</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                margin: 0;
                padding: 0;
            }
            .container {
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
            }
            .header {
                background-color: #007bff;
                color: #fff;
                padding: 20px;
                text-align: center;
            }
            .content {
                padding: 20px;
                background-color: #f9f9f9;
                border: 1px solid #ddd;
            }
            .footer {
                text-align: center;
                margin-top: 20px;
                font-size: 12px;
                color: #777;
            }
            .button {
                display: inline-block;
                background-color: #007bff;
                color: #fff;
                padding: 10px 20px;
                text-decoration: none;
                border-radius: 4px;
                margin-top: 20px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>' . $subject . '</h1>
            </div>
            <div class="content">
                <p>' . __('hello') . ' ' . $name . ',</p>
                <p>' . __('password_reset_message') . '</p>
                <p><a href="' . $resetLink . '" class="button">' . __('reset_password') . '</a></p>
                <p>' . __('password_reset_expire') . '</p>
                <p>' . __('password_reset_ignore') . '</p>
            </div>
            <div class="footer">
                <p>' . __('email_footer_text') . '</p>
                <p>&copy; ' . date('Y') . ' ' . __('app_name') . '</p>
            </div>
        </div>
    </body>
    </html>
    ';

    // Send the email
    return sendEmail($to, $subject, $html);
}

/**
 * Send a welcome email
 *
 * @param string $to The recipient email address
 * @param string $username The username
 * @param string $password The password
 * @return bool True if email was sent, false otherwise
 */
function sendWelcomeEmail($to, $username, $password) {
    // Get the base URL
    $baseUrl = getBaseUrl();

    // Set the login link
    $loginLink = $baseUrl . '/login';

    // Set the subject
    $subject = __('welcome_subject');

    // Create HTML email template
    $html = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>' . $subject . '</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                margin: 0;
                padding: 0;
            }
            .container {
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
            }
            .header {
                background-color: #007bff;
                color: #fff;
                padding: 20px;
                text-align: center;
            }
            .content {
                padding: 20px;
                background-color: #f9f9f9;
                border: 1px solid #ddd;
            }
            .footer {
                text-align: center;
                margin-top: 20px;
                font-size: 12px;
                color: #777;
            }
            .button {
                display: inline-block;
                background-color: #007bff;
                color: #fff;
                padding: 10px 20px;
                text-decoration: none;
                border-radius: 4px;
                margin-top: 20px;
            }
            .credentials {
                background-color: #f0f0f0;
                padding: 10px;
                border-radius: 4px;
                margin: 20px 0;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>' . $subject . '</h1>
            </div>
            <div class="content">
                <p>' . __('welcome_message') . '</p>
                <div class="credentials">
                    <p><strong>' . __('username') . ':</strong> ' . $username . '</p>
                    <p><strong>' . __('password') . ':</strong> ' . $password . '</p>
                </div>
                <p>' . __('welcome_instructions') . '</p>
                <p><a href="' . $loginLink . '" class="button">' . __('login') . '</a></p>
            </div>
            <div class="footer">
                <p>' . __('email_footer_text') . '</p>
                <p>&copy; ' . date('Y') . ' ' . __('app_name') . '</p>
            </div>
        </div>
    </body>
    </html>
    ';

    // Send the email
    return sendEmail($to, $subject, $html);
}
