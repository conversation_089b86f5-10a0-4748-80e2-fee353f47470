<?php
/**
 * Device Maintenance Integration Test
 * 
 * This file contains integration tests for device maintenance functionality.
 */

// Include bootstrap
require_once __DIR__ . '/../bootstrap.php';

// Test data
$testHospital = [
    'name' => 'Test Hospital ' . time(),
    'address' => '123 Test Street',
    'city' => 'Test City',
    'country' => 'Test Country',
    'phone' => '************',
    'email' => 'test_' . time() . '@example.com',
    'website' => 'https://example.com',
    'notes' => 'Test hospital for integration tests'
];

$testDepartment = [
    'name' => 'Test Department ' . time(),
    'location' => 'Floor 1',
    'phone' => '************',
    'email' => 'test_dept_' . time() . '@example.com',
    'notes' => 'Test department for integration tests'
];

$testDevice = [
    'name' => 'Test Device ' . time(),
    'model' => 'Test Model',
    'serial_number' => 'SN' . time(),
    'manufacturer' => 'Test Manufacturer',
    'category' => 'Test Category',
    'purchase_date' => date('Y-m-d'),
    'warranty_expiry' => date('Y-m-d', strtotime('+1 year')),
    'status' => 'operational',
    'maintenance_interval' => 90, // 90 days
    'location' => 'Room 101',
    'notes' => 'Test device for integration tests'
];

$testMaintenanceSchedule = [
    'title' => 'Test Maintenance ' . time(),
    'description' => 'Test maintenance schedule',
    'scheduled_date' => date('Y-m-d', strtotime('+30 days')),
    'status' => 'scheduled',
    'priority' => 'medium',
    'notes' => 'Test maintenance schedule for integration tests'
];

$testMaintenanceLog = [
    'maintenance_date' => date('Y-m-d'),
    'actions_taken' => 'Test maintenance actions',
    'parts_replaced' => 'None',
    'results' => 'Device is operational',
    'recommendations' => 'No recommendations'
];

// Test: Create Hospital
$testFramework->addTest('Create Hospital', function() use ($testFramework, $hospitalModel, $testHospital) {
    // Create the hospital
    $hospitalId = $hospitalModel->create($testHospital);
    
    // Check if hospital was created
    if (!$testFramework->assertTrue($hospitalId > 0, 'Failed to create hospital')) {
        return false;
    }
    
    // Store hospital ID for other tests
    $GLOBALS['test_hospital_id'] = $hospitalId;
    
    return true;
});

// Test: Create Department
$testFramework->addTest('Create Department', function() use ($testFramework, $departmentModel, $testDepartment) {
    // Get the hospital ID from the previous test
    $hospitalId = $GLOBALS['test_hospital_id'] ?? null;
    
    // Skip if hospital ID is not available
    if (!$hospitalId) {
        return 'Hospital ID not available';
    }
    
    // Set hospital ID for the department
    $testDepartment['hospital_id'] = $hospitalId;
    
    // Create the department
    $departmentId = $departmentModel->create($testDepartment);
    
    // Check if department was created
    if (!$testFramework->assertTrue($departmentId > 0, 'Failed to create department')) {
        return false;
    }
    
    // Store department ID for other tests
    $GLOBALS['test_department_id'] = $departmentId;
    
    return true;
});

// Test: Create Device
$testFramework->addTest('Create Device', function() use ($testFramework, $deviceModel, $testDevice) {
    // Get the department ID from the previous test
    $departmentId = $GLOBALS['test_department_id'] ?? null;
    
    // Skip if department ID is not available
    if (!$departmentId) {
        return 'Department ID not available';
    }
    
    // Set department ID for the device
    $testDevice['department_id'] = $departmentId;
    
    // Create the device
    $deviceId = $deviceModel->create($testDevice);
    
    // Check if device was created
    if (!$testFramework->assertTrue($deviceId > 0, 'Failed to create device')) {
        return false;
    }
    
    // Store device ID for other tests
    $GLOBALS['test_device_id'] = $deviceId;
    
    return true;
});

// Test: Create Maintenance Schedule
$testFramework->addTest('Create Maintenance Schedule', function() use ($testFramework, $maintenanceModel, $testMaintenanceSchedule) {
    // Get the device ID from the previous test
    $deviceId = $GLOBALS['test_device_id'] ?? null;
    
    // Skip if device ID is not available
    if (!$deviceId) {
        return 'Device ID not available';
    }
    
    // Set device ID for the maintenance schedule
    $testMaintenanceSchedule['device_id'] = $deviceId;
    
    // Create the maintenance schedule
    $scheduleId = $maintenanceModel->createSchedule($testMaintenanceSchedule);
    
    // Check if maintenance schedule was created
    if (!$testFramework->assertTrue($scheduleId > 0, 'Failed to create maintenance schedule')) {
        return false;
    }
    
    // Store schedule ID for other tests
    $GLOBALS['test_schedule_id'] = $scheduleId;
    
    return true;
});

// Test: Get Upcoming Maintenance
$testFramework->addTest('Get Upcoming Maintenance', function() use ($testFramework, $maintenanceModel) {
    // Get the device ID from the previous test
    $deviceId = $GLOBALS['test_device_id'] ?? null;
    
    // Skip if device ID is not available
    if (!$deviceId) {
        return 'Device ID not available';
    }
    
    // Get upcoming maintenance schedules (next 60 days)
    $schedules = $maintenanceModel->getUpcomingSchedules(60);
    
    // Check if schedules were found
    if (!$testFramework->assertNotEmpty($schedules, 'No upcoming maintenance schedules found')) {
        return false;
    }
    
    // Check if the test schedule is in the list
    $found = false;
    foreach ($schedules as $schedule) {
        if ($schedule['device_id'] == $deviceId) {
            $found = true;
            break;
        }
    }
    
    if (!$testFramework->assertTrue($found, 'Test schedule not found in upcoming maintenance')) {
        return false;
    }
    
    return true;
});

// Test: Create Maintenance Log
$testFramework->addTest('Create Maintenance Log', function() use ($testFramework, $maintenanceModel, $testMaintenanceLog, $deviceModel) {
    // Get the device ID and schedule ID from the previous tests
    $deviceId = $GLOBALS['test_device_id'] ?? null;
    $scheduleId = $GLOBALS['test_schedule_id'] ?? null;
    
    // Skip if device ID or schedule ID is not available
    if (!$deviceId || !$scheduleId) {
        return 'Device ID or Schedule ID not available';
    }
    
    // Set device ID and schedule ID for the maintenance log
    $testMaintenanceLog['device_id'] = $deviceId;
    $testMaintenanceLog['schedule_id'] = $scheduleId;
    $testMaintenanceLog['technician_id'] = 1; // Assuming admin user with ID 1
    
    // Create the maintenance log
    $logId = $maintenanceModel->createLog($testMaintenanceLog);
    
    // Check if maintenance log was created
    if (!$testFramework->assertTrue($logId > 0, 'Failed to create maintenance log')) {
        return false;
    }
    
    // Store log ID for other tests
    $GLOBALS['test_log_id'] = $logId;
    
    // Check if the device's last maintenance date was updated
    $device = $deviceModel->getById($deviceId);
    
    if (!$testFramework->assertEquals(date('Y-m-d'), $device['last_maintenance_date'], 'Device last maintenance date not updated')) {
        return false;
    }
    
    // Check if the schedule status was updated to completed
    $schedule = $maintenanceModel->getScheduleById($scheduleId);
    
    if (!$testFramework->assertEquals('completed', $schedule['status'], 'Schedule status not updated to completed')) {
        return false;
    }
    
    return true;
});

// Test: Get Maintenance Logs for Device
$testFramework->addTest('Get Maintenance Logs for Device', function() use ($testFramework, $maintenanceModel) {
    // Get the device ID from the previous test
    $deviceId = $GLOBALS['test_device_id'] ?? null;
    
    // Skip if device ID is not available
    if (!$deviceId) {
        return 'Device ID not available';
    }
    
    // Get maintenance logs for the device
    $logs = $maintenanceModel->getAllLogs($deviceId);
    
    // Check if logs were found
    if (!$testFramework->assertNotEmpty($logs, 'No maintenance logs found')) {
        return false;
    }
    
    // Check if the test log is in the list
    $found = false;
    foreach ($logs as $log) {
        if ($log['device_id'] == $deviceId) {
            $found = true;
            break;
        }
    }
    
    if (!$testFramework->assertTrue($found, 'Test log not found in maintenance logs')) {
        return false;
    }
    
    return true;
});

// Test: Clean Up (Delete Test Data)
$testFramework->addTest('Clean Up', function() use ($testFramework, $deviceModel, $departmentModel, $hospitalModel, $maintenanceModel) {
    // Get IDs from previous tests
    $deviceId = $GLOBALS['test_device_id'] ?? null;
    $departmentId = $GLOBALS['test_department_id'] ?? null;
    $hospitalId = $GLOBALS['test_hospital_id'] ?? null;
    $scheduleId = $GLOBALS['test_schedule_id'] ?? null;
    $logId = $GLOBALS['test_log_id'] ?? null;
    
    // Delete maintenance log
    if ($logId) {
        $maintenanceModel->deleteLog($logId);
    }
    
    // Delete maintenance schedule
    if ($scheduleId) {
        $maintenanceModel->deleteSchedule($scheduleId);
    }
    
    // Delete device
    if ($deviceId) {
        $result = $deviceModel->delete($deviceId);
        if (!$testFramework->assertTrue($result, 'Failed to delete device')) {
            return false;
        }
    }
    
    // Delete department
    if ($departmentId) {
        $result = $departmentModel->delete($departmentId);
        if (!$testFramework->assertTrue($result, 'Failed to delete department')) {
            return false;
        }
    }
    
    // Delete hospital
    if ($hospitalId) {
        $result = $hospitalModel->delete($hospitalId);
        if (!$testFramework->assertTrue($result, 'Failed to delete hospital')) {
            return false;
        }
    }
    
    return true;
});

// Run the tests
$testFramework->runTests();
