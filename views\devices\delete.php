<?php
/**
 * Delete Device View
 * 
 * This file displays the confirmation form to delete a device.
 */

// Set page title
$pageTitle = __('delete_device');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3"><?php echo __('delete_device'); ?></h1>
    
    <a href="<?php echo getBaseUrl(); ?>/devices" class="btn btn-secondary">
        <?php echo __('back_to_devices'); ?>
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <?php echo __('confirm_deletion'); ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-danger">
                    <h6 class="alert-heading">
                        <?php echo __('warning'); ?>
                    </h6>
                    <p class="mb-0"><?php echo __('delete_device_warning'); ?></p>
                </div>
                
                <!-- Device Details -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <?php echo __('device_details'); ?>
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted"><?php echo __('device_name'); ?></label>
                                    <div class="h5"><?php echo htmlspecialchars($device['name']); ?></div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label text-muted"><?php echo __('serial_number'); ?></label>
                                    <div class="fw-bold"><?php echo htmlspecialchars($device['serial_number']); ?></div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label text-muted"><?php echo __('manufacturer'); ?></label>
                                    <div class="fw-bold"><?php echo htmlspecialchars($device['manufacturer']); ?></div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label text-muted"><?php echo __('model'); ?></label>
                                    <div class="fw-bold"><?php echo htmlspecialchars($device['model']); ?></div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted"><?php echo __('location'); ?></label>
                                    <div class="fw-bold">
                                        <?php echo htmlspecialchars($device['hospital_name']); ?> - <?php echo htmlspecialchars($device['department_name']); ?>
                                    </div>
                                    <?php if ($device['location']): ?>
                                        <small class="text-muted"><?php echo htmlspecialchars($device['location']); ?></small>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label text-muted"><?php echo __('category'); ?></label>
                                    <div class="fw-bold"><?php echo htmlspecialchars($device['category']); ?></div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label text-muted"><?php echo __('status'); ?></label>
                                    <div>
                                        <span class="badge bg-<?php echo getDeviceStatusColor($device['status']); ?>">
                                            <?php echo __($device['status']); ?>
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label text-muted"><?php echo __('purchase_date'); ?></label>
                                    <div class="fw-bold"><?php echo date('M d, Y', strtotime($device['purchase_date'])); ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Related Data Warning -->
                <?php
                // Check for related data
                $relatedTickets = $ticketModel->getAll($device['id']);
                $relatedMaintenance = $maintenanceModel->getAllSchedules($device['id']);
                $relatedLogs = $maintenanceModel->getAllLogs(null, $device['id']);
                
                $hasRelatedData = !empty($relatedTickets) || !empty($relatedMaintenance) || !empty($relatedLogs);
                ?>
                
                <?php if ($hasRelatedData): ?>
                    <div class="alert alert-warning">
                        <h6 class="alert-heading">
                            <i class="fas fa-exclamation-triangle me-2"></i><?php echo __('related_data_found'); ?>
                        </h6>
                        <p class="mb-2"><?php echo __('device_has_related_data'); ?></p>
                        
                        <ul class="mb-0">
                            <?php if (!empty($relatedTickets)): ?>
                                <li><strong><?php echo count($relatedTickets); ?></strong> <?php echo __('tickets'); ?></li>
                            <?php endif; ?>
                            
                            <?php if (!empty($relatedMaintenance)): ?>
                                <li><strong><?php echo count($relatedMaintenance); ?></strong> <?php echo __('maintenance_schedules'); ?></li>
                            <?php endif; ?>
                            
                            <?php if (!empty($relatedLogs)): ?>
                                <li><strong><?php echo count($relatedLogs); ?></strong> <?php echo __('maintenance_logs'); ?></li>
                            <?php endif; ?>
                        </ul>
                        
                        <div class="mt-2">
                            <small class="text-muted"><?php echo __('related_data_will_also_be_deleted'); ?></small>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- QR Code Warning -->
                <?php if ($device['qr_code']): ?>
                    <div class="alert alert-info">
                        <h6 class="alert-heading">
                            <i class="fas fa-qrcode me-2"></i><?php echo __('qr_code_notice'); ?>
                        </h6>
                        <p class="mb-0"><?php echo __('device_qr_code_will_be_deleted'); ?></p>
                    </div>
                <?php endif; ?>
                
                <!-- Confirmation Form -->
                <form method="POST" action="<?php echo getBaseUrl(); ?>/devices/delete/<?php echo $device['id']; ?>">
                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="confirm_deletion" name="confirm_deletion" required>
                            <label class="form-check-label" for="confirm_deletion">
                                <?php echo __('confirm_delete_device'); ?>
                            </label>
                        </div>
                        
                        <?php if ($hasRelatedData): ?>
                            <div class="form-check mt-2">
                                <input class="form-check-input" type="checkbox" id="confirm_related_data" name="confirm_related_data" required>
                                <label class="form-check-label" for="confirm_related_data">
                                    <?php echo __('confirm_delete_related_data'); ?>
                                </label>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="<?php echo getBaseUrl(); ?>/devices" class="btn btn-secondary">
                            <?php echo __('cancel'); ?>
                        </a>

                        <button type="submit" name="confirm" value="yes" class="btn btn-danger" id="delete_button" disabled>
                            <?php echo __('delete_device'); ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Additional Information -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i><?php echo __('what_happens_when_deleted'); ?>
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2 text-danger">
                        • <?php echo __('device_will_be_permanently_deleted'); ?>
                    </li>
                    <li class="mb-2 text-danger">
                        • <?php echo __('all_tickets_will_be_deleted'); ?>
                    </li>
                    <li class="mb-2 text-danger">
                        • <?php echo __('all_maintenance_schedules_deleted'); ?>
                    </li>
                    <li class="mb-2 text-danger">
                        • <?php echo __('all_maintenance_logs_deleted'); ?>
                    </li>
                    <li class="mb-2 text-warning">
                        • <?php echo __('qr_code_files_deleted'); ?>
                    </li>
                    <li class="text-secondary">
                        • <?php echo __('action_cannot_be_undone'); ?>
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- Alternative Actions -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-lightbulb me-2"></i><?php echo __('alternative_actions'); ?>
                </h6>
            </div>
            <div class="card-body">
                <p class="text-muted mb-3"><?php echo __('consider_these_alternatives'); ?>:</p>
                
                <div class="d-grid gap-2">
                    <a href="<?php echo getBaseUrl(); ?>/devices/edit/<?php echo $device['id']; ?>" class="btn btn-warning">
                        <?php echo __('change_device_status_to_retired'); ?>
                    </a>

                    <a href="<?php echo getBaseUrl(); ?>/devices/edit/<?php echo $device['id']; ?>" class="btn btn-info">
                        <?php echo __('mark_as_out_of_service'); ?>
                    </a>

                    <a href="<?php echo getBaseUrl(); ?>/devices/view/<?php echo $device['id']; ?>" class="btn btn-primary">
                        <?php echo __('review_device_details'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const confirmDeletionCheckbox = document.getElementById('confirm_deletion');
    const confirmRelatedDataCheckbox = document.getElementById('confirm_related_data');
    const deleteButton = document.getElementById('delete_button');
    
    function updateDeleteButton() {
        const deletionConfirmed = confirmDeletionCheckbox.checked;
        const relatedDataConfirmed = confirmRelatedDataCheckbox ? confirmRelatedDataCheckbox.checked : true;
        
        deleteButton.disabled = !(deletionConfirmed && relatedDataConfirmed);
    }
    
    confirmDeletionCheckbox.addEventListener('change', updateDeleteButton);
    
    if (confirmRelatedDataCheckbox) {
        confirmRelatedDataCheckbox.addEventListener('change', updateDeleteButton);
    }
    
    // Add confirmation dialog
    deleteButton.addEventListener('click', function(e) {
        const deviceName = '<?php echo addslashes($device['name']); ?>';
        if (!confirm(`<?php echo __('are_you_sure_delete_device'); ?> "${deviceName}"?`)) {
            e.preventDefault();
        }
    });
});
</script>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
