<?php
/**
 * API Controller
 * 
 * This file handles API requests for AJAX operations.
 */

// Set content type to JSON
header('Content-Type: application/json');

// Handle CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Get the API endpoint
$endpoint = isset($url[1]) ? $url[1] : '';

// Initialize response
$response = [
    'success' => false,
    'message' => '',
    'data' => null
];

// Handle API endpoints
switch ($endpoint) {
    case 'get_departments':
        // Get departments by hospital ID
        $hospitalId = isset($_GET['hospital_id']) ? (int)$_GET['hospital_id'] : 0;
        
        if ($hospitalId) {
            $departments = $departmentModel->getByHospital($hospitalId);
            $response['success'] = true;
            $response['data'] = $departments;
        } else {
            $response['message'] = 'Hospital ID is required';
        }
        break;
        
    case 'get_devices':
        // Get devices by hospital ID and/or department ID
        $hospitalId = isset($_GET['hospital_id']) ? (int)$_GET['hospital_id'] : 0;
        $departmentId = isset($_GET['department_id']) ? (int)$_GET['department_id'] : 0;
        
        if ($hospitalId || $departmentId) {
            $devices = $deviceModel->getAll($hospitalId, $departmentId);
            $response['success'] = true;
            $response['data'] = $devices;
        } else {
            $response['message'] = 'Hospital ID or Department ID is required';
        }
        break;
        
    case 'get_device':
        // Get device by ID
        $deviceId = isset($_GET['device_id']) ? (int)$_GET['device_id'] : 0;
        
        if ($deviceId) {
            $device = $deviceModel->getById($deviceId);
            
            if ($device) {
                $response['success'] = true;
                $response['data'] = $device;
            } else {
                $response['message'] = 'Device not found';
            }
        } else {
            $response['message'] = 'Device ID is required';
        }
        break;
        
    case 'get_maintenance_schedules':
        // Get maintenance schedules by device ID
        $deviceId = isset($_GET['device_id']) ? (int)$_GET['device_id'] : 0;
        
        if ($deviceId) {
            $schedules = $maintenanceModel->getAllSchedules($deviceId);
            $response['success'] = true;
            $response['data'] = $schedules;
        } else {
            $response['message'] = 'Device ID is required';
        }
        break;
        
    case 'get_tickets':
        // Get tickets by device ID
        $deviceId = isset($_GET['device_id']) ? (int)$_GET['device_id'] : 0;
        
        if ($deviceId) {
            $tickets = $ticketModel->getAll($deviceId);
            $response['success'] = true;
            $response['data'] = $tickets;
        } else {
            $response['message'] = 'Device ID is required';
        }
        break;
        
    case 'get_users':
        // Check if the user is logged in
        if (!isLoggedIn()) {
            $response['message'] = 'Authentication required';
            break;
        }
        
        // Get users by role and/or hospital ID
        $role = isset($_GET['role']) ? $_GET['role'] : '';
        $hospitalId = isset($_GET['hospital_id']) ? (int)$_GET['hospital_id'] : 0;
        
        if ($role) {
            $users = $userModel->getByRole($role, $hospitalId);
            $response['success'] = true;
            $response['data'] = $users;
        } else {
            $response['message'] = 'Role is required';
        }
        break;
        
    case 'create_ticket':
        // Check if the user is logged in
        if (!isLoggedIn()) {
            $response['message'] = 'Authentication required';
            break;
        }
        
        // Get the current user
        $currentUser = getCurrentUser();
        
        // Check if the request is a POST request
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $response['message'] = 'Method not allowed';
            break;
        }
        
        // Get the request data
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            $response['message'] = 'Invalid request data';
            break;
        }
        
        // Create the ticket
        $ticketData = [
            'device_id' => $data['device_id'] ?? null,
            'reported_by' => $currentUser['id'],
            'assigned_to' => $data['assigned_to'] ?? null,
            'title' => $data['title'] ?? '',
            'description' => $data['description'] ?? '',
            'priority' => $data['priority'] ?? 'medium',
            'status' => 'open'
        ];
        
        // Validate data
        if (empty($ticketData['device_id'])) {
            $response['message'] = 'Device ID is required';
            break;
        }
        
        if (empty($ticketData['title'])) {
            $response['message'] = 'Title is required';
            break;
        }
        
        if (empty($ticketData['description'])) {
            $response['message'] = 'Description is required';
            break;
        }
        
        // Create the ticket
        $ticketId = $ticketModel->create($ticketData);
        
        if ($ticketId) {
            $response['success'] = true;
            $response['message'] = 'Ticket created successfully';
            $response['data'] = ['ticket_id' => $ticketId];
        } else {
            $response['message'] = 'Failed to create ticket';
        }
        break;
        
    case 'update_ticket_status':
        // Check if the user is logged in
        if (!isLoggedIn()) {
            $response['message'] = 'Authentication required';
            break;
        }
        
        // Get the current user
        $currentUser = getCurrentUser();
        
        // Check if the request is a POST request
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $response['message'] = 'Method not allowed';
            break;
        }
        
        // Get the request data
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            $response['message'] = 'Invalid request data';
            break;
        }
        
        // Update the ticket status
        $ticketId = $data['ticket_id'] ?? 0;
        $status = $data['status'] ?? '';
        $comment = $data['comment'] ?? '';
        
        if (!$ticketId) {
            $response['message'] = 'Ticket ID is required';
            break;
        }
        
        if (!$status) {
            $response['message'] = 'Status is required';
            break;
        }
        
        // Check if the user has permission to update the ticket
        if (!hasPermission('manage_tickets')) {
            $response['message'] = 'Permission denied';
            break;
        }
        
        // Update the ticket status
        $result = $ticketModel->changeStatus($ticketId, $status, $currentUser['id'], $comment);
        
        if ($result) {
            $response['success'] = true;
            $response['message'] = 'Ticket status updated successfully';
        } else {
            $response['message'] = 'Failed to update ticket status';
        }
        break;
        
    case 'get_notifications':
        // Check if the user is logged in
        if (!isLoggedIn()) {
            $response['message'] = 'Authentication required';
            break;
        }
        
        // Get the current user
        $currentUser = getCurrentUser();
        
        // Get unread notifications
        $notifications = getUnreadNotifications($currentUser['id']);
        $count = countUnreadNotifications($currentUser['id']);
        
        $response['success'] = true;
        $response['data'] = [
            'notifications' => $notifications,
            'count' => $count
        ];
        break;
        
    case 'mark_notification_read':
        // Check if the user is logged in
        if (!isLoggedIn()) {
            $response['message'] = 'Authentication required';
            break;
        }
        
        // Get the notification ID
        $notificationId = isset($_GET['notification_id']) ? (int)$_GET['notification_id'] : 0;
        
        if (!$notificationId) {
            $response['message'] = 'Notification ID is required';
            break;
        }
        
        // Mark the notification as read
        $result = markNotificationAsRead($notificationId);
        
        if ($result) {
            $response['success'] = true;
            $response['message'] = 'Notification marked as read';
        } else {
            $response['message'] = 'Failed to mark notification as read';
        }
        break;
        
    default:
        $response['message'] = 'Invalid API endpoint';
        break;
}

// Return the response
echo json_encode($response);
