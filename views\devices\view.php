<?php
/**
 * View Device
 * 
 * This file displays the details of a device.
 */

// Set page title
$pageTitle = __('device_details');
$pageSubtitle = htmlspecialchars($device['name']) . ' - ' . htmlspecialchars($device['serial_number']);

// Start output buffering
ob_start();
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-3">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="<?php echo getBaseUrl(); ?>/dashboard"><i class="fas fa-home"></i></a></li>
        <li class="breadcrumb-item"><a href="<?php echo getBaseUrl(); ?>/devices"><?php echo __('devices'); ?></a></li>
        <li class="breadcrumb-item active" aria-current="page"><?php echo htmlspecialchars($device['name']); ?></li>
    </ol>
</nav>

<!-- Header with Status Banner -->
<div class="card card-glass border-0 mb-4 fade-in-up">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="d-flex align-items-center mb-3">
                    <div class="device-icon-large me-4">
                        <i class="fas fa-microscope fa-3x text-primary"></i>
                    </div>
                    <div>
                        <h1 class="h2 text-gradient mb-1"><?php echo htmlspecialchars($device['name']); ?></h1>
                        <p class="text-muted mb-2"><?php echo htmlspecialchars($device['model']); ?> • <?php echo htmlspecialchars($device['manufacturer']); ?></p>
                        <div class="d-flex align-items-center gap-3">
                            <span class="badge bg-<?php echo getStatusColor($device['status']); ?> fs-6 px-3 py-2">
                                <i class="fas fa-circle me-1" style="font-size: 0.5rem;"></i>
                                <?php echo __($device['status']); ?>
                            </span>
                            <code class="bg-light px-2 py-1 rounded"><?php echo htmlspecialchars($device['serial_number']); ?></code>
                            <?php
                            $warrantyExpiry = new DateTime($device['warranty_expiry']);
                            $now = new DateTime();
                            $isExpired = $warrantyExpiry < $now;
                            $daysUntilExpiry = $now->diff($warrantyExpiry)->days;
                            $isExpiringSoon = !$isExpired && $daysUntilExpiry <= 30;
                            ?>
                            <span class="badge <?php echo $isExpired ? 'bg-danger' : ($isExpiringSoon ? 'bg-warning' : 'bg-success'); ?>">
                                <i class="fas fa-shield-alt me-1"></i>
                                <?php echo $isExpired ? __('warranty_expired') : ($isExpiringSoon ? __('warranty_expiring') : __('warranty_valid')); ?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <div class="btn-group-vertical d-grid gap-2">
                    <a href="<?php echo getBaseUrl(); ?>/devices" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_list'); ?>
                    </a>

                    <?php if (hasPermission('manage_devices')): ?>
                    <a href="<?php echo getBaseUrl(); ?>/devices/edit/<?php echo $device['id']; ?>" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i><?php echo __('edit_device'); ?>
                    </a>

                    <div class="dropdown">
                        <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog me-2"></i><?php echo __('more_actions'); ?>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end shadow">
                            <li><a class="dropdown-item" href="<?php echo getBaseUrl(); ?>/devices/qrcode/<?php echo $device['id']; ?>">
                                <i class="fas fa-qrcode me-2 text-info"></i><?php echo __('view_qr_code'); ?>
                            </a></li>
                            <li><a class="dropdown-item" href="<?php echo getBaseUrl(); ?>/tickets/create?device_id=<?php echo $device['id']; ?>">
                                <i class="fas fa-ticket-alt me-2 text-warning"></i><?php echo __('report_issue'); ?>
                            </a></li>
                            <li><a class="dropdown-item" href="<?php echo getBaseUrl(); ?>/maintenance/create_schedule?device_id=<?php echo $device['id']; ?>">
                                <i class="fas fa-calendar-plus me-2 text-success"></i><?php echo __('schedule_maintenance'); ?>
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="printDevice()">
                                <i class="fas fa-print me-2 text-secondary"></i><?php echo __('print_details'); ?>
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="exportDevice()">
                                <i class="fas fa-download me-2 text-primary"></i><?php echo __('export_data'); ?>
                            </a></li>
                        </ul>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Device Information -->
    <div class="col-md-8">
        <!-- Device Details Card -->
        <div class="card mb-4 fade-in-up">
            <div class="card-header bg-gradient-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i><?php echo __('device_information'); ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-4">
                    <!-- Basic Information -->
                    <div class="col-md-6">
                        <h6 class="text-primary border-bottom pb-2 mb-3">
                            <i class="fas fa-tag me-2"></i><?php echo __('basic_information'); ?>
                        </h6>
                        <div class="info-group">
                            <div class="info-item mb-3">
                                <label class="info-label"><?php echo __('name'); ?></label>
                                <div class="info-value fw-bold"><?php echo htmlspecialchars($device['name']); ?></div>
                            </div>
                            <div class="info-item mb-3">
                                <label class="info-label"><?php echo __('model'); ?></label>
                                <div class="info-value">
                                    <span class="badge bg-light text-dark fs-6"><?php echo htmlspecialchars($device['model']); ?></span>
                                </div>
                            </div>
                            <div class="info-item mb-3">
                                <label class="info-label"><?php echo __('serial_number'); ?></label>
                                <div class="info-value">
                                    <code class="bg-light px-2 py-1 rounded"><?php echo htmlspecialchars($device['serial_number']); ?></code>
                                    <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('<?php echo htmlspecialchars($device['serial_number']); ?>')">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="info-item mb-3">
                                <label class="info-label"><?php echo __('manufacturer'); ?></label>
                                <div class="info-value"><?php echo htmlspecialchars($device['manufacturer']); ?></div>
                            </div>
                            <div class="info-item mb-3">
                                <label class="info-label"><?php echo __('category'); ?></label>
                                <div class="info-value">
                                    <span class="badge bg-secondary"><?php echo htmlspecialchars($device['category'] ?? 'N/A'); ?></span>
                                </div>
                            </div>
                            <div class="info-item mb-3">
                                <label class="info-label"><?php echo __('status'); ?></label>
                                <div class="info-value">
                                    <span class="badge bg-<?php echo getStatusColor($device['status']); ?> fs-6 px-3 py-2">
                                        <i class="fas fa-circle me-1" style="font-size: 0.5rem;"></i>
                                        <?php echo __($device['status']); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Location & Dates -->
                    <div class="col-md-6">
                        <h6 class="text-info border-bottom pb-2 mb-3">
                            <i class="fas fa-map-marker-alt me-2"></i><?php echo __('location_and_dates'); ?>
                        </h6>
                        <div class="info-group">
                            <div class="info-item mb-3">
                                <label class="info-label"><?php echo __('hospital'); ?></label>
                                <div class="info-value">
                                    <?php if (hasPermission('view_hospitals')): ?>
                                        <a href="<?php echo getBaseUrl(); ?>/hospitals/view/<?php echo $device['hospital_id']; ?>" class="text-decoration-none">
                                            <i class="fas fa-hospital text-primary me-2"></i>
                                            <?php echo htmlspecialchars($device['hospital_name']); ?>
                                        </a>
                                    <?php else: ?>
                                        <i class="fas fa-hospital text-muted me-2"></i>
                                        <?php echo htmlspecialchars($device['hospital_name']); ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="info-item mb-3">
                                <label class="info-label"><?php echo __('department'); ?></label>
                                <div class="info-value">
                                    <?php if (hasPermission('view_departments')): ?>
                                        <a href="<?php echo getBaseUrl(); ?>/departments/view/<?php echo $device['department_id']; ?>" class="text-decoration-none">
                                            <i class="fas fa-building text-info me-2"></i>
                                            <?php echo htmlspecialchars($device['department_name']); ?>
                                        </a>
                                    <?php else: ?>
                                        <i class="fas fa-building text-muted me-2"></i>
                                        <?php echo htmlspecialchars($device['department_name']); ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="info-item mb-3">
                                <label class="info-label"><?php echo __('location'); ?></label>
                                <div class="info-value">
                                    <i class="fas fa-map-pin text-warning me-2"></i>
                                    <?php echo htmlspecialchars($device['location'] ?? __('not_specified')); ?>
                                </div>
                            </div>
                            <div class="info-item mb-3">
                                <label class="info-label"><?php echo __('purchase_date'); ?></label>
                                <div class="info-value">
                                    <i class="fas fa-calendar text-success me-2"></i>
                                    <?php echo date('M d, Y', strtotime($device['purchase_date'])); ?>
                                    <small class="text-muted ms-2">
                                        (<?php
                                        $purchaseDate = new DateTime($device['purchase_date']);
                                        $now = new DateTime();
                                        $age = $now->diff($purchaseDate);
                                        echo $age->y . ' ' . __('years') . ', ' . $age->m . ' ' . __('months');
                                        ?>)
                                    </small>
                                </div>
                            </div>
                            <div class="info-item mb-3">
                                <label class="info-label"><?php echo __('warranty_expiry'); ?></label>
                                <div class="info-value">
                                    <?php
                                    $warrantyExpiry = new DateTime($device['warranty_expiry']);
                                    $now = new DateTime();
                                    $isExpired = $warrantyExpiry < $now;
                                    $daysUntilExpiry = $now->diff($warrantyExpiry)->days;
                                    $isExpiringSoon = !$isExpired && $daysUntilExpiry <= 30;
                                    ?>
                                    <i class="fas fa-shield-alt <?php echo $isExpired ? 'text-danger' : ($isExpiringSoon ? 'text-warning' : 'text-success'); ?> me-2"></i>
                                    <span class="<?php echo $isExpired ? 'text-danger' : ($isExpiringSoon ? 'text-warning' : 'text-success'); ?>">
                                        <?php echo $warrantyExpiry->format('M d, Y'); ?>
                                    </span>
                                    <?php if ($isExpired): ?>
                                        <span class="badge bg-danger ms-2"><?php echo __('expired'); ?></span>
                                    <?php elseif ($isExpiringSoon): ?>
                                        <span class="badge bg-warning ms-2"><?php echo $daysUntilExpiry; ?> <?php echo __('days_left'); ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="info-item mb-3">
                                <label class="info-label"><?php echo __('maintenance_interval'); ?></label>
                                <div class="info-value">
                                    <i class="fas fa-clock text-primary me-2"></i>
                                    <?php if ($device['maintenance_interval']): ?>
                                        <?php echo $device['maintenance_interval']; ?> <?php echo __('days'); ?>
                                        <small class="text-muted ms-2">(<?php echo __('every'); ?> <?php echo round($device['maintenance_interval']/30, 1); ?> <?php echo __('months'); ?>)</small>
                                    <?php else: ?>
                                        <span class="text-muted"><?php echo __('not_set'); ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if (!empty($device['description']) || !empty($device['notes'])): ?>
                <hr class="my-4">
                <div class="row">
                    <?php if (!empty($device['description'])): ?>
                    <div class="col-md-6">
                        <h6 class="text-success border-bottom pb-2 mb-3">
                            <i class="fas fa-file-alt me-2"></i><?php echo __('description'); ?>
                        </h6>
                        <div class="bg-light p-3 rounded">
                            <?php echo nl2br(htmlspecialchars($device['description'])); ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($device['notes'])): ?>
                    <div class="col-md-6">
                        <h6 class="text-warning border-bottom pb-2 mb-3">
                            <i class="fas fa-sticky-note me-2"></i><?php echo __('notes'); ?>
                        </h6>
                        <div class="bg-light p-3 rounded">
                            <?php echo nl2br(htmlspecialchars($device['notes'])); ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Maintenance History -->
        <div class="card mb-4 fade-in-up" style="animation-delay: 0.2s;">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i><?php echo __('maintenance_history'); ?>
                </h5>
                
                <?php if (hasPermission('manage_maintenance')): ?>
                <a href="<?php echo getBaseUrl(); ?>/maintenance/create?device_id=<?php echo $device['id']; ?>" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus me-2"></i><?php echo __('schedule_maintenance'); ?>
                </a>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <?php if (empty($maintenanceHistory)): ?>
                    <p class="text-muted"><?php echo __('no_maintenance_history'); ?></p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th><?php echo __('date'); ?></th>
                                    <th><?php echo __('type'); ?></th>
                                    <th><?php echo __('description'); ?></th>
                                    <th><?php echo __('status'); ?></th>
                                    <th><?php echo __('technician'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($maintenanceHistory as $maintenance): ?>
                                    <tr>
                                        <td><?php echo date('Y-m-d', strtotime($maintenance['scheduled_date'])); ?></td>
                                        <td><?php echo htmlspecialchars($maintenance['title']); ?></td>
                                        <td><?php echo htmlspecialchars($maintenance['description']); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo getMaintenanceStatusColor($maintenance['status']); ?>">
                                                <?php echo __($maintenance['status']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo htmlspecialchars($maintenance['technician_name'] ?? 'N/A'); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Sidebar -->
    <div class="col-md-4">
        <!-- QR Code -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0"><?php echo __('qr_code'); ?></h5>
            </div>
            <div class="card-body text-center">
                <?php if (!empty($device['qr_code']) && file_exists($device['qr_code'])): ?>
                    <img src="<?php echo getBaseUrl(); ?>/<?php echo $device['qr_code']; ?>" alt="QR Code" class="img-fluid mb-3" style="max-width: 200px;">
                    <br>
                    <a href="<?php echo getBaseUrl(); ?>/<?php echo $device['qr_code']; ?>" download class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-download me-2"></i><?php echo __('download'); ?>
                    </a>
                <?php else: ?>
                    <p class="text-muted"><?php echo __('no_qr_code'); ?></p>
                    <?php if (hasPermission('manage_devices')): ?>
                    <a href="<?php echo getBaseUrl(); ?>/devices/generate_qr/<?php echo $device['id']; ?>" class="btn btn-sm btn-primary">
                        <i class="fas fa-qrcode me-2"></i><?php echo __('generate_qr'); ?>
                    </a>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <?php if (hasPermission('manage_devices') || hasPermission('manage_maintenance')): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0"><?php echo __('quick_actions'); ?></h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <?php if (hasPermission('manage_maintenance')): ?>
                    <a href="<?php echo getBaseUrl(); ?>/maintenance/create?device_id=<?php echo $device['id']; ?>" class="btn btn-outline-primary">
                        <i class="fas fa-wrench me-2"></i><?php echo __('schedule_maintenance'); ?>
                    </a>
                    
                    <a href="<?php echo getBaseUrl(); ?>/tickets/create?device_id=<?php echo $device['id']; ?>" class="btn btn-outline-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i><?php echo __('report_issue'); ?>
                    </a>
                    <?php endif; ?>
                    
                    <?php if (hasPermission('manage_devices')): ?>
                    <a href="<?php echo getBaseUrl(); ?>/devices/edit/<?php echo $device['id']; ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-edit me-2"></i><?php echo __('edit_device'); ?>
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- Device Statistics -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><?php echo __('statistics'); ?></h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary"><?php echo count($maintenanceHistory); ?></h4>
                            <small class="text-muted"><?php echo __('maintenance_records'); ?></small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info"><?php echo $device['age_years'] ?? 'N/A'; ?></h4>
                        <small class="text-muted"><?php echo __('years_old'); ?></small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Add enhanced scripts
$scripts = '
<style>
.info-label {
    font-size: 0.875rem;
    color: #6c757d;
    font-weight: 500;
    margin-bottom: 0.25rem;
    display: block;
}

.info-value {
    font-size: 1rem;
    color: #212529;
    margin-bottom: 0.5rem;
}

.info-group {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 0.5rem;
    border-left: 4px solid #007bff;
}

.device-icon-large {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.card-glass {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

@media print {
    .btn, .dropdown, .breadcrumb {
        display: none !important;
    }

    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
}
</style>

<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll("[data-bs-toggle=\"tooltip\"]"));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Add smooth scrolling to anchor links
    $("a[href^=\"#\"]").on("click", function(e) {
        e.preventDefault();
        const target = $(this.getAttribute("href"));
        if (target.length) {
            $("html, body").animate({
                scrollTop: target.offset().top - 100
            }, 500);
        }
    });

    // Auto-refresh device status every 30 seconds
    setInterval(function() {
        refreshDeviceStatus();
    }, 30000);
});

// Copy to clipboard function
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showToast("' . __('copied_to_clipboard') . '", "success");
    }).catch(function(err) {
        console.error("Could not copy text: ", err);
        showToast("' . __('copy_failed') . '", "error");
    });
}

// Print device details
function printDevice() {
    window.print();
}

// Export device data
function exportDevice() {
    const deviceData = {
        id: ' . $device['id'] . ',
        name: "' . addslashes($device['name']) . '",
        model: "' . addslashes($device['model']) . '",
        serial_number: "' . addslashes($device['serial_number']) . '",
        manufacturer: "' . addslashes($device['manufacturer']) . '",
        status: "' . addslashes($device['status']) . '",
        hospital: "' . addslashes($device['hospital_name']) . '",
        department: "' . addslashes($device['department_name']) . '",
        location: "' . addslashes($device['location'] ?? '') . '",
        purchase_date: "' . $device['purchase_date'] . '",
        warranty_expiry: "' . $device['warranty_expiry'] . '",
        maintenance_interval: "' . ($device['maintenance_interval'] ?? '') . '",
        notes: "' . addslashes($device['notes'] ?? '') . '"
    };

    const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(deviceData, null, 2));
    const downloadAnchorNode = document.createElement("a");
    downloadAnchorNode.setAttribute("href", dataStr);
    downloadAnchorNode.setAttribute("download", "device_" + deviceData.serial_number + ".json");
    document.body.appendChild(downloadAnchorNode);
    downloadAnchorNode.click();
    downloadAnchorNode.remove();

    showToast("' . __('device_data_exported') . '", "success");
}

// Refresh device status
function refreshDeviceStatus() {
    fetch("' . getBaseUrl() . '/api/devices/' . $device['id'] . '/status")
        .then(response => response.json())
        .then(data => {
            if (data.status && data.status !== "' . $device['status'] . '") {
                // Update status badge
                const statusBadges = document.querySelectorAll(".badge:contains(\"' . __($device['status']) . '\")");
                statusBadges.forEach(badge => {
                    badge.className = "badge bg-" + getStatusColor(data.status);
                    badge.innerHTML = "<i class=\"fas fa-circle me-1\" style=\"font-size: 0.5rem;\"></i>" + data.status;
                });

                showToast("' . __('device_status_updated') . '", "info");
            }
        })
        .catch(error => {
            console.error("Error refreshing status:", error);
        });
}

// Show toast notification
function showToast(message, type = "info") {
    const toastContainer = document.getElementById("toast-container") || createToastContainer();

    const toast = document.createElement("div");
    toast.className = `toast align-items-center text-white bg-${type === "error" ? "danger" : type} border-0`;
    toast.setAttribute("role", "alert");
    toast.setAttribute("aria-live", "assertive");
    toast.setAttribute("aria-atomic", "true");

    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-${type === "success" ? "check" : type === "error" ? "times" : "info"}-circle me-2"></i>
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    toastContainer.appendChild(toast);

    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    // Remove toast after it hides
    toast.addEventListener("hidden.bs.toast", function() {
        toast.remove();
    });
}

// Create toast container if it doesn\'t exist
function createToastContainer() {
    const container = document.createElement("div");
    container.id = "toast-container";
    container.className = "toast-container position-fixed top-0 end-0 p-3";
    container.style.zIndex = "9999";
    document.body.appendChild(container);
    return container;
}

// Get status color helper function
function getStatusColor(status) {
    const colors = {
        "active": "success",
        "inactive": "secondary",
        "maintenance": "warning",
        "broken": "danger",
        "retired": "dark"
    };
    return colors[status] || "secondary";
}

// Keyboard shortcuts
document.addEventListener("keydown", function(e) {
    // Ctrl/Cmd + E for edit
    if ((e.ctrlKey || e.metaKey) && e.key === "e") {
        e.preventDefault();
        const editBtn = document.querySelector("a[href*=\"/devices/edit\"]");
        if (editBtn) editBtn.click();
    }

    // Ctrl/Cmd + P for print
    if ((e.ctrlKey || e.metaKey) && e.key === "p") {
        e.preventDefault();
        printDevice();
    }

    // Ctrl/Cmd + S for export
    if ((e.ctrlKey || e.metaKey) && e.key === "s") {
        e.preventDefault();
        exportDevice();
    }
});

// Add loading states to buttons
document.addEventListener("click", function(e) {
    if (e.target.matches("a[href], button[type=\"submit\"]")) {
        const btn = e.target;
        if (!btn.classList.contains("no-loading")) {
            btn.style.opacity = "0.7";
            btn.style.pointerEvents = "none";

            setTimeout(() => {
                btn.style.opacity = "1";
                btn.style.pointerEvents = "auto";
            }, 2000);
        }
    }
});
</script>
';

// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
