<?php
/**
 * 404 Not Found Page
 * 
 * This file displays a 404 error page.
 */

// Set page title
$pageTitle = '404 ' . __('not_found');

// Start output buffering
ob_start();
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8 text-center">
            <div class="error-template">
                <h1 class="display-1">404</h1>
                <h2><?php echo __('not_found'); ?></h2>
                <div class="error-details mb-4">
                    <?php echo __('page_not_found_message'); ?>
                </div>
                <div class="error-actions">
                    <a href="<?php echo getBaseUrl(); ?>/dashboard" class="btn btn-primary">
                        <i class="fas fa-home me-2"></i>
                        <?php echo __('back_to_dashboard'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
