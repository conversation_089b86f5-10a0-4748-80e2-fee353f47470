<?php
/**
 * Role Model
 * 
 * This class handles role-related database operations.
 */

class Role {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * Get all roles
     * 
     * @return array The roles
     */
    public function getAll() {
        try {
            $stmt = $this->pdo->prepare("
                SELECT r.*, 
                       COUNT(u.id) as user_count
                FROM roles r
                LEFT JOIN users u ON r.name = u.role
                GROUP BY r.id
                ORDER BY r.display_order, r.name
            ");
            
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get All Roles Error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get a role by ID
     * 
     * @param int $id The role ID
     * @return array|bool The role or false if not found
     */
    public function getById($id) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT r.*, 
                       COUNT(u.id) as user_count
                FROM roles r
                LEFT JOIN users u ON r.name = u.role
                WHERE r.id = ?
                GROUP BY r.id
            ");
            
            $stmt->execute([$id]);
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Get Role By ID Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get a role by name
     * 
     * @param string $name The role name
     * @return array|bool The role or false if not found
     */
    public function getByName($name) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT r.*, 
                       COUNT(u.id) as user_count
                FROM roles r
                LEFT JOIN users u ON r.name = u.role
                WHERE r.name = ?
                GROUP BY r.id
            ");
            
            $stmt->execute([$name]);
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Get Role By Name Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create a new role
     * 
     * @param array $data The role data
     * @return int|bool The role ID if successful, false otherwise
     */
    public function create($data) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO roles (name, display_name, description, permissions, display_order, is_system)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $data['name'],
                $data['display_name'],
                $data['description'] ?? null,
                json_encode($data['permissions'] ?? []),
                $data['display_order'] ?? 0,
                $data['is_system'] ?? 0
            ]);
            
            return $this->pdo->lastInsertId();
        } catch (PDOException $e) {
            error_log("Create Role Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update a role
     * 
     * @param int $id The role ID
     * @param array $data The role data
     * @return bool True if successful, false otherwise
     */
    public function update($id, $data) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE roles SET
                    name = ?,
                    display_name = ?,
                    description = ?,
                    permissions = ?,
                    display_order = ?,
                    updated_at = NOW()
                WHERE id = ? AND is_system = 0
            ");
            
            return $stmt->execute([
                $data['name'],
                $data['display_name'],
                $data['description'] ?? null,
                json_encode($data['permissions'] ?? []),
                $data['display_order'] ?? 0,
                $id
            ]);
        } catch (PDOException $e) {
            error_log("Update Role Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete a role
     * 
     * @param int $id The role ID
     * @return bool True if successful, false otherwise
     */
    public function delete($id) {
        try {
            // Check if role is a system role
            $stmt = $this->pdo->prepare("SELECT is_system, name FROM roles WHERE id = ?");
            $stmt->execute([$id]);
            $role = $stmt->fetch();
            
            if (!$role || $role['is_system']) {
                return false; // Cannot delete system roles
            }
            
            // Check if role is in use
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM users WHERE role = ?");
            $stmt->execute([$role['name']]);
            $userCount = $stmt->fetchColumn();
            
            if ($userCount > 0) {
                return false; // Cannot delete role that is in use
            }
            
            $stmt = $this->pdo->prepare("DELETE FROM roles WHERE id = ? AND is_system = 0");
            return $stmt->execute([$id]);
        } catch (PDOException $e) {
            error_log("Delete Role Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get available permissions
     * 
     * @return array The available permissions
     */
    public function getAvailablePermissions() {
        return [
            'view_dashboard' => 'View Dashboard',
            'manage_users' => 'Manage Users',
            'view_users' => 'View Users',
            'manage_hospitals' => 'Manage Hospitals',
            'view_hospitals' => 'View Hospitals',
            'manage_departments' => 'Manage Departments',
            'view_departments' => 'View Departments',
            'manage_devices' => 'Manage Devices',
            'view_devices' => 'View Devices',
            'manage_maintenance' => 'Manage Maintenance',
            'view_maintenance' => 'View Maintenance',
            'manage_tickets' => 'Manage Tickets',
            'view_tickets' => 'View Tickets',
            'view_reports' => 'View Reports',
            'export_data' => 'Export Data',
            'manage_roles' => 'Manage Roles',
            'view_roles' => 'View Roles'
        ];
    }
    
    /**
     * Get role statistics
     * 
     * @param int $roleId The role ID
     * @return array The role statistics
     */
    public function getStatistics($roleId) {
        try {
            $role = $this->getById($roleId);
            if (!$role) {
                return [];
            }
            
            $stats = [
                'total_users' => $role['user_count'],
                'active_users' => 0,
                'inactive_users' => 0
            ];
            
            // Get user status breakdown
            $stmt = $this->pdo->prepare("
                SELECT status, COUNT(*) as count
                FROM users
                WHERE role = ?
                GROUP BY status
            ");
            $stmt->execute([$role['name']]);
            
            while ($row = $stmt->fetch()) {
                if ($row['status'] === 'active') {
                    $stats['active_users'] = $row['count'];
                } else {
                    $stats['inactive_users'] += $row['count'];
                }
            }
            
            return $stats;
        } catch (PDOException $e) {
            error_log("Get Role Statistics Error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Check if role name exists
     * 
     * @param string $name The role name
     * @param int $excludeId Optional role ID to exclude from check
     * @return bool True if exists, false otherwise
     */
    public function nameExists($name, $excludeId = null) {
        try {
            $sql = "SELECT COUNT(*) FROM roles WHERE name = ?";
            $params = [$name];
            
            if ($excludeId) {
                $sql .= " AND id != ?";
                $params[] = $excludeId;
            }
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchColumn() > 0;
        } catch (PDOException $e) {
            error_log("Role Name Exists Check Error: " . $e->getMessage());
            return false;
        }
    }
}
