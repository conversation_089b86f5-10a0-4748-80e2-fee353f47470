<?php
/**
 * Database Installation Script
 * 
 * This script sets up the database for the Medical Device Management System.
 */

// Set default timezone
date_default_timezone_set('UTC');

echo "<h1>Medical Device Management System - Database Installation</h1>";

// Check if config file exists
if (!file_exists('config/database.php')) {
    echo "<p style='color: red;'>Error: Database configuration file not found!</p>";
    echo "<p>Please create config/database.php with your database settings.</p>";
    exit;
}

// Load database configuration
require_once 'config/database.php';

echo "<h2>Step 1: Testing Database Connection</h2>";

try {
    // Test connection
    $testPdo = new PDO("mysql:host=" . DB_HOST . ";charset=utf8mb4", DB_USER, DB_PASS, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]);

    echo "<p style='color: green;'>✓ Database connection successful</p>";

    // Create database if it doesn't exist
    echo "<h2>Step 2: Creating Database</h2>";
    $testPdo->exec("CREATE DATABASE IF NOT EXISTS `" . DB_NAME . "` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p style='color: green;'>✓ Database '" . DB_NAME . "' created/verified</p>";

    // Connect to the specific database
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]);

} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration in config/database.php</p>";
    exit;
}

echo "<h2>Step 3: Installing Database Schema</h2>";

// Read and execute schema file
$schemaFile = 'database/schema.sql';
if (!file_exists($schemaFile)) {
    echo "<p style='color: red;'>✗ Schema file not found: {$schemaFile}</p>";
    exit;
}

$schema = file_get_contents($schemaFile);

// Remove the database creation and use statements from schema
$schema = preg_replace('/^-- Create database.*$/m', '', $schema);
$schema = preg_replace('/^CREATE DATABASE.*$/m', '', $schema);
$schema = preg_replace('/^-- Use database.*$/m', '', $schema);
$schema = preg_replace('/^USE .*$/m', '', $schema);

// Split into individual statements
$statements = array_filter(array_map('trim', explode(';', $schema)));

$successCount = 0;
$errorCount = 0;

foreach ($statements as $statement) {
    if (empty($statement) || strpos($statement, '--') === 0) {
        continue;
    }
    
    try {
        $pdo->exec($statement);
        $successCount++;
        
        // Show what was executed
        $firstLine = strtok($statement, "\n");
        if (strlen($firstLine) > 50) {
            $firstLine = substr($firstLine, 0, 50) . '...';
        }
        echo "<p style='color: green;'>✓ {$firstLine}</p>";
        
    } catch (PDOException $e) {
        $errorCount++;
        $firstLine = strtok($statement, "\n");
        if (strlen($firstLine) > 50) {
            $firstLine = substr($firstLine, 0, 50) . '...';
        }
        echo "<p style='color: orange;'>⚠ {$firstLine} - " . $e->getMessage() . "</p>";
    }
}

echo "<h2>Step 4: Installation Summary</h2>";
echo "<p>Successful statements: <strong>{$successCount}</strong></p>";
echo "<p>Errors/Warnings: <strong>{$errorCount}</strong></p>";

// Verify installation
echo "<h2>Step 5: Verifying Installation</h2>";

$requiredTables = [
    'roles', 'users', 'hospitals', 'departments', 'devices',
    'maintenance_schedules', 'maintenance_logs', 'tickets',
    'ticket_updates', 'notifications', 'settings',
    'activity_logs', 'security_logs', 'user_permissions',
    'remember_tokens', 'password_resets'
];

$stmt = $pdo->query("SHOW TABLES");
$existingTables = $stmt->fetchAll(PDO::FETCH_COLUMN);

$missingTables = array_diff($requiredTables, $existingTables);

if (empty($missingTables)) {
    echo "<p style='color: green;'>✓ All required tables created successfully</p>";
} else {
    echo "<p style='color: red;'>✗ Missing tables: " . implode(', ', $missingTables) . "</p>";
}

// Check if admin user exists
echo "<h2>Step 6: Checking Default Admin User</h2>";

try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE username = 'admin'");
    $result = $stmt->fetch();
    
    if ($result['count'] > 0) {
        echo "<p style='color: green;'>✓ Default admin user exists</p>";
        
        // Verify password
        $stmt = $pdo->prepare("SELECT password FROM users WHERE username = 'admin'");
        $stmt->execute();
        $user = $stmt->fetch();
        
        if (password_verify('admin123', $user['password'])) {
            echo "<p style='color: green;'>✓ Default admin password is correct</p>";
        } else {
            echo "<p style='color: orange;'>⚠ Default admin password may be incorrect</p>";
            
            // Reset password
            $newPassword = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE username = 'admin'");
            $stmt->execute([$newPassword]);
            echo "<p style='color: green;'>✓ Admin password reset to 'admin123'</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠ Default admin user not found, creating...</p>";
        
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO users (username, password, email, full_name, role, language, status)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        
        $result = $stmt->execute([
            'admin',
            $hashedPassword,
            '<EMAIL>',
            'System Administrator',
            'admin',
            'en',
            'active'
        ]);
        
        if ($result) {
            echo "<p style='color: green;'>✓ Default admin user created</p>";
        } else {
            echo "<p style='color: red;'>✗ Failed to create default admin user</p>";
        }
    }
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ Error checking admin user: " . $e->getMessage() . "</p>";
}

// Check sample data
echo "<h2>Step 7: Sample Data</h2>";

try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM hospitals");
    $hospitalCount = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM settings");
    $settingsCount = $stmt->fetch()['count'];
    
    echo "<p>Hospitals: <strong>{$hospitalCount}</strong></p>";
    echo "<p>Settings: <strong>{$settingsCount}</strong></p>";
    
    if ($hospitalCount == 0) {
        echo "<p style='color: orange;'>⚠ No sample hospitals found. You may want to add some sample data.</p>";
    }
    
    if ($settingsCount == 0) {
        echo "<p style='color: orange;'>⚠ No settings found. Default settings may not have been inserted.</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ Error checking sample data: " . $e->getMessage() . "</p>";
}

echo "<h2>Installation Complete!</h2>";

if ($errorCount == 0) {
    echo "<p style='color: green; font-size: 18px;'><strong>✓ Installation completed successfully!</strong></p>";
    echo "<p><strong>Default Login Credentials:</strong></p>";
    echo "<p>Username: <code>admin</code></p>";
    echo "<p>Password: <code>admin123</code></p>";
    echo "<p><a href='login.php' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Login Page</a></p>";
} else {
    echo "<p style='color: orange; font-size: 18px;'><strong>⚠ Installation completed with some warnings</strong></p>";
    echo "<p>Please review the warnings above. The system should still work, but you may want to address any issues.</p>";
    echo "<p><a href='debug_login.php'>Run Login Debug</a> | <a href='login.php'>Go to Login Page</a></p>";
}

echo "<hr>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ul>";
echo "<li>Test login with admin/admin123</li>";
echo "<li>Change the default admin password</li>";
echo "<li>Add hospitals and departments</li>";
echo "<li>Configure email settings</li>";
echo "<li>Add users and assign roles</li>";
echo "</ul>";

// Add some CSS
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
code { background-color: #f5f5f5; padding: 2px 6px; border-radius: 3px; font-family: monospace; }
ul { background-color: #f9f9f9; padding: 15px; border-radius: 5px; }
</style>";
?>
