<?php
/**
 * Maintenance Schedules List View
 * 
 * This file displays the list of maintenance schedules.
 */

// Set page title
$pageTitle = __('maintenance_schedules');

// Start output buffering
ob_start();
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-3">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="<?php echo getBaseUrl(); ?>/dashboard">Home</a></li>
        <li class="breadcrumb-item active" aria-current="page"><?php echo __('maintenance'); ?></li>
    </ol>
</nav>

<!-- Header Section -->
<div class="card card-glass border-0 mb-4 fade-in-up">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="d-flex align-items-center">
                    <div>
                        <h1 class="h2 text-gradient mb-1"><?php echo __('maintenance_management'); ?></h1>
                        <p class="text-muted mb-0"><?php echo __('manage_maintenance_schedules'); ?></p>
                        <small class="text-info">
                            <?php echo count($schedules); ?> <?php echo __('schedules_active'); ?>
                        </small>
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex gap-2 justify-content-end flex-wrap">
                    <a href="<?php echo getBaseUrl(); ?>/maintenance/logs" class="btn btn-outline-info">
                        <?php echo __('maintenance_logs'); ?>
                    </a>

                    <?php if (hasPermission('manage_maintenance')): ?>
                    <a href="<?php echo getBaseUrl(); ?>/maintenance/create_schedule" class="btn btn-primary">
                        <?php echo __('schedule_maintenance'); ?>
                    </a>
                    <?php endif; ?>

                    <div class="dropdown">
                        <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-download me-2"></i><?php echo __('export'); ?>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end shadow">
                            <li><h6 class="dropdown-header"><?php echo __('export_formats'); ?></h6></li>
                            <li><a class="dropdown-item" href="<?php echo getBaseUrl(); ?>/maintenance/export?format=pdf">
                                <i class="fas fa-file-pdf me-2 text-danger"></i>PDF <?php echo __('report'); ?>
                            </a></li>
                            <li><a class="dropdown-item" href="<?php echo getBaseUrl(); ?>/maintenance/export?format=excel">
                                <i class="fas fa-file-excel me-2 text-success"></i>Excel <?php echo __('spreadsheet'); ?>
                            </a></li>
                            <li><a class="dropdown-item" href="<?php echo getBaseUrl(); ?>/maintenance/export?format=csv">
                                <i class="fas fa-file-csv me-2 text-info"></i>CSV <?php echo __('data'); ?>
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="printSchedules()">
                                <i class="fas fa-print me-2 text-secondary"></i><?php echo __('print_list'); ?>
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <?php
    $totalSchedules = count($schedules);
    $scheduledCount = count(array_filter($schedules, function($s) { return $s['status'] === 'scheduled'; }));
    $completedCount = count(array_filter($schedules, function($s) { return $s['status'] === 'completed'; }));
    $overdueCount = count(array_filter($schedules, function($s) {
        $scheduledDate = new DateTime($s['scheduled_date']);
        $now = new DateTime();
        return $scheduledDate < $now && $s['status'] === 'scheduled';
    }));
    ?>
    <div class="col-md-3">
        <div class="stats-card primary fade-in-up">
            <div class="card-body">
                <div class="stats-number"><?php echo $totalSchedules; ?></div>
                <div class="stats-text"><?php echo __('total_schedules'); ?></div>
                <i class="fas fa-calendar-alt stats-icon"></i>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card info fade-in-up" style="animation-delay: 0.1s;">
            <div class="card-body">
                <div class="stats-number"><?php echo $scheduledCount; ?></div>
                <div class="stats-text"><?php echo __('scheduled'); ?></div>
                <i class="fas fa-clock stats-icon"></i>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card success fade-in-up" style="animation-delay: 0.2s;">
            <div class="card-body">
                <div class="stats-number"><?php echo $completedCount; ?></div>
                <div class="stats-text"><?php echo __('completed'); ?></div>
                <i class="fas fa-check-circle stats-icon"></i>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card danger fade-in-up" style="animation-delay: 0.3s;">
            <div class="card-body">
                <div class="stats-number"><?php echo $overdueCount; ?></div>
                <div class="stats-text"><?php echo __('overdue'); ?></div>
                <i class="fas fa-exclamation-triangle stats-icon"></i>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Filters -->
<div class="card card-glass mb-4 fade-in-up">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-filter me-2"></i><?php echo __('filters'); ?>
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" action="<?php echo getBaseUrl(); ?>/maintenance" class="row g-3">
            <div class="col-md-3">
                <label for="hospital_id" class="form-label"><?php echo __('hospital'); ?></label>
                <select class="form-select" id="hospital_id" name="hospital_id">
                    <option value=""><?php echo __('all_hospitals'); ?></option>
                    <?php foreach ($hospitals as $hospital): ?>
                        <option value="<?php echo $hospital['id']; ?>" <?php echo ($hospitalId == $hospital['id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($hospital['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="device_id" class="form-label"><?php echo __('device'); ?></label>
                <select class="form-select" id="device_id" name="device_id">
                    <option value=""><?php echo __('all_devices'); ?></option>
                    <?php foreach ($devices as $device): ?>
                        <option value="<?php echo $device['id']; ?>" <?php echo ($deviceId == $device['id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($device['name'] . ' (' . $device['serial_number'] . ')'); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="status" class="form-label"><?php echo __('status'); ?></label>
                <select class="form-select" id="status" name="status">
                    <option value=""><?php echo __('all_statuses'); ?></option>
                    <option value="scheduled" <?php echo ($status == 'scheduled') ? 'selected' : ''; ?>><?php echo __('scheduled'); ?></option>
                    <option value="completed" <?php echo ($status == 'completed') ? 'selected' : ''; ?>><?php echo __('completed'); ?></option>
                    <option value="overdue" <?php echo ($status == 'overdue') ? 'selected' : ''; ?>><?php echo __('overdue'); ?></option>
                    <option value="cancelled" <?php echo ($status == 'cancelled') ? 'selected' : ''; ?>><?php echo __('cancelled'); ?></option>
                </select>
            </div>
            
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i><?php echo __('filter'); ?>
                    </button>
                </div>
            </div>

            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearFilters()">
                        <i class="fas fa-times me-1"></i><?php echo __('clear_filters'); ?>
                    </button>
                    <small class="text-muted">
                        <?php echo count($schedules); ?> <?php echo __('schedules_found'); ?>
                    </small>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Enhanced Maintenance Schedules Table -->
<div class="card fade-in-up" style="animation-delay: 0.4s;">
    <div class="card-header bg-light">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-list me-2"></i><?php echo __('maintenance_schedules_list'); ?>
            </h5>
            <div class="d-flex gap-2">
                <button class="btn btn-sm btn-outline-primary" onclick="toggleView('table')" id="tableViewBtn">
                    <i class="fas fa-table"></i> <?php echo __('table'); ?>
                </button>
                <button class="btn btn-sm btn-outline-primary" onclick="toggleView('timeline')" id="timelineViewBtn">
                    <i class="fas fa-calendar-alt"></i> <?php echo __('timeline'); ?>
                </button>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <!-- Table View -->
        <div id="tableView">
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="schedules-table">
                    <thead class="table-light">
                        <tr>
                            <th>
                                <i class="fas fa-microscope me-1"></i><?php echo __('device'); ?>
                            </th>
                            <th>
                                <i class="fas fa-tasks me-1"></i><?php echo __('maintenance_task'); ?>
                            </th>
                            <th>
                                <i class="fas fa-calendar me-1"></i><?php echo __('scheduled_date'); ?>
                            </th>
                            <th>
                                <i class="fas fa-repeat me-1"></i><?php echo __('frequency'); ?>
                            </th>
                            <th>
                                <i class="fas fa-heartbeat me-1"></i><?php echo __('status'); ?>
                            </th>
                            <th>
                                <i class="fas fa-flag me-1"></i><?php echo __('priority'); ?>
                            </th>
                            <th>
                                <i class="fas fa-user me-1"></i><?php echo __('created_by'); ?>
                            </th>
                            <th>
                                <i class="fas fa-cogs me-1"></i><?php echo __('actions'); ?>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($schedules)): ?>
                            <tr>
                                <td colspan="8" class="text-center py-5">
                                    <div class="text-muted">
                                        <i class="fas fa-calendar-times fa-3x mb-3 opacity-50"></i>
                                        <h5><?php echo __('no_maintenance_schedules'); ?></h5>
                                        <p><?php echo __('no_schedules_found_message'); ?></p>
                                        <?php if (hasPermission('manage_maintenance')): ?>
                                            <a href="<?php echo getBaseUrl(); ?>/maintenance/create_schedule" class="btn btn-primary">
                                                <i class="fas fa-plus me-2"></i><?php echo __('create_first_schedule'); ?>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($schedules as $schedule): ?>
                                <?php
                                $scheduledDate = new DateTime($schedule['scheduled_date']);
                                $now = new DateTime();
                                $isOverdue = $scheduledDate < $now && $schedule['status'] === 'scheduled';
                                $daysUntil = $now->diff($scheduledDate)->days;
                                $isUpcoming = !$isOverdue && $daysUntil <= 7 && $schedule['status'] === 'scheduled';
                                ?>
                                <tr class="schedule-row <?php echo $isOverdue ? 'table-danger' : ($isUpcoming ? 'table-warning' : ''); ?>"
                                    data-schedule-id="<?php echo $schedule['id']; ?>">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="device-icon me-3">
                                                <i class="fas fa-microscope text-primary"></i>
                                            </div>
                                            <div>
                                                <a href="<?php echo getBaseUrl(); ?>/devices/view/<?php echo $schedule['device_id']; ?>" class="text-decoration-none">
                                                    <strong><?php echo htmlspecialchars($schedule['device_name']); ?></strong>
                                                </a>
                                                <br>
                                                <small class="text-muted">
                                                    <i class="fas fa-barcode me-1"></i>
                                                    <?php echo htmlspecialchars($schedule['serial_number']); ?>
                                                </small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($schedule['title']); ?></strong>
                                            <?php if (!empty($schedule['description'])): ?>
                                                <br>
                                                <small class="text-muted"><?php echo htmlspecialchars(substr($schedule['description'], 0, 50)); ?>...</small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="date-info">
                                            <span class="<?php echo $isOverdue ? 'text-danger fw-bold' : ($isUpcoming ? 'text-warning fw-bold' : ''); ?>">
                                                <i class="fas fa-calendar-day me-1"></i>
                                                <?php echo $scheduledDate->format('M d, Y'); ?>
                                            </span>
                                            <br>
                                            <small class="<?php echo $isOverdue ? 'text-danger' : ($isUpcoming ? 'text-warning' : 'text-muted'); ?>">
                                                <?php if ($isOverdue): ?>
                                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                                    <?php echo $daysUntil; ?> <?php echo __('days_overdue'); ?>
                                                <?php elseif ($isUpcoming): ?>
                                                    <i class="fas fa-clock me-1"></i>
                                                    <?php echo $daysUntil; ?> <?php echo __('days_remaining'); ?>
                                                <?php else: ?>
                                                    <i class="fas fa-calendar me-1"></i>
                                                    <?php echo $scheduledDate->format('l'); ?>
                                                <?php endif; ?>
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info fs-6 px-3 py-2">
                                            <i class="fas fa-repeat me-1"></i>
                                            <?php echo __($schedule['frequency']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo getMaintenanceStatusColor($schedule['status']); ?> fs-6 px-3 py-2">
                                            <i class="fas fa-circle me-1" style="font-size: 0.5rem;"></i>
                                            <?php echo __($schedule['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo getPriorityColor($schedule['priority']); ?> fs-6 px-3 py-2">
                                            <i class="fas fa-flag me-1"></i>
                                            <?php echo __($schedule['priority']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="user-info">
                                            <i class="fas fa-user text-muted me-1"></i>
                                            <?php echo htmlspecialchars($schedule['created_by_name'] ?? __('unknown')); ?>
                                            <?php if (!empty($schedule['created_at'])): ?>
                                                <br>
                                                <small class="text-muted">
                                                    <?php echo date('M d, Y', strtotime($schedule['created_at'])); ?>
                                                </small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo getBaseUrl(); ?>/maintenance/view_schedule/<?php echo $schedule['id']; ?>"
                                               class="btn btn-sm btn-outline-primary"
                                               data-bs-toggle="tooltip"
                                               title="<?php echo __('view_schedule'); ?>">
                                                <i class="fas fa-eye"></i>
                                            </a>

                                            <?php if (hasPermission('manage_maintenance')): ?>
                                            <a href="<?php echo getBaseUrl(); ?>/maintenance/edit_schedule/<?php echo $schedule['id']; ?>"
                                               class="btn btn-sm btn-outline-secondary"
                                               data-bs-toggle="tooltip"
                                               title="<?php echo __('edit_schedule'); ?>">
                                                <i class="fas fa-edit"></i>
                                            </a>

                                            <?php if ($schedule['status'] === 'scheduled'): ?>
                                            <a href="<?php echo getBaseUrl(); ?>/maintenance/create_log?schedule_id=<?php echo $schedule['id']; ?>"
                                               class="btn btn-sm btn-outline-success"
                                               data-bs-toggle="tooltip"
                                               title="<?php echo __('perform_maintenance'); ?>">
                                                <i class="fas fa-wrench"></i>
                                            </a>
                                            <?php endif; ?>

                                            <button type="button"
                                                    class="btn btn-sm btn-outline-danger"
                                                    onclick="confirmDelete(<?php echo $schedule['id']; ?>, '<?php echo htmlspecialchars($schedule['title']); ?>')"
                                                    data-bs-toggle="tooltip"
                                                    title="<?php echo __('delete_schedule'); ?>">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Timeline View -->
        <div id="timelineView" style="display: none;" class="p-4">
            <div class="timeline">
                <?php if (!empty($schedules)): ?>
                    <?php
                    // Sort schedules by date for timeline
                    usort($schedules, function($a, $b) {
                        return strtotime($a['scheduled_date']) - strtotime($b['scheduled_date']);
                    });
                    ?>
                    <?php foreach ($schedules as $index => $schedule): ?>
                        <?php
                        $scheduledDate = new DateTime($schedule['scheduled_date']);
                        $now = new DateTime();
                        $isOverdue = $scheduledDate < $now && $schedule['status'] === 'scheduled';
                        $isPast = $scheduledDate < $now;
                        ?>
                        <div class="timeline-item <?php echo $isOverdue ? 'overdue' : ($isPast ? 'past' : 'future'); ?>">
                            <div class="timeline-marker">
                                <i class="fas fa-<?php echo $schedule['status'] === 'completed' ? 'check' : ($isOverdue ? 'exclamation-triangle' : 'clock'); ?>"></i>
                            </div>
                            <div class="timeline-content">
                                <div class="timeline-header">
                                    <h6 class="mb-1"><?php echo htmlspecialchars($schedule['title']); ?></h6>
                                    <small class="text-muted"><?php echo $scheduledDate->format('M d, Y'); ?></small>
                                </div>
                                <div class="timeline-body">
                                    <p class="mb-2">
                                        <strong><?php echo htmlspecialchars($schedule['device_name']); ?></strong>
                                        <span class="badge bg-<?php echo getMaintenanceStatusColor($schedule['status']); ?> ms-2">
                                            <?php echo __($schedule['status']); ?>
                                        </span>
                                    </p>
                                    <?php if (!empty($schedule['description'])): ?>
                                        <p class="text-muted small mb-2"><?php echo htmlspecialchars($schedule['description']); ?></p>
                                    <?php endif; ?>
                                    <div class="d-flex gap-2">
                                        <a href="<?php echo getBaseUrl(); ?>/maintenance/view_schedule/<?php echo $schedule['id']; ?>" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye me-1"></i><?php echo __('view'); ?>
                                        </a>
                                        <?php if (hasPermission('manage_maintenance') && $schedule['status'] === 'scheduled'): ?>
                                        <a href="<?php echo getBaseUrl(); ?>/maintenance/create_log?schedule_id=<?php echo $schedule['id']; ?>" class="btn btn-sm btn-success">
                                            <i class="fas fa-wrench me-1"></i><?php echo __('perform'); ?>
                                        </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-calendar-times fa-3x mb-3 opacity-50"></i>
                        <h5><?php echo __('no_maintenance_schedules'); ?></h5>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo __('confirm_delete'); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p><?php echo __('delete_schedule_confirm'); ?></p>
                <p><strong id="scheduleName"></strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo __('cancel'); ?></button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="confirm" value="yes">
                    <button type="submit" class="btn btn-danger"><?php echo __('delete'); ?></button>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
// Add enhanced scripts
$scripts = '
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: "";
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #007bff, #6c757d);
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
    padding-left: 30px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    border: 3px solid #fff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.timeline-item.overdue .timeline-marker {
    background: #dc3545;
}

.timeline-item.past .timeline-marker {
    background: #6c757d;
}

.timeline-item.future .timeline-marker {
    background: #28a745;
}

.timeline-content {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.timeline-header {
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    margin-bottom: 10px;
}

.schedule-row {
    transition: all 0.2s ease;
}

.schedule-row:hover {
    transform: translateX(5px);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.date-info {
    min-width: 120px;
}

.user-info {
    min-width: 100px;
}

.icon-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.card-glass {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

@media print {
    .btn, .dropdown, .breadcrumb, #timelineView {
        display: none !important;
    }

    #tableView {
        display: block !important;
    }

    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
}
</style>

<script>
$(document).ready(function() {
    // Initialize DataTable
    $("#schedules-table").DataTable({
        responsive: true,
        pageLength: 25,
        order: [[2, "asc"]], // Sort by scheduled date
        language: {
            search: "' . __('search') . ':",
            lengthMenu: "' . __('show') . ' _MENU_ ' . __('entries') . '",
            info: "' . __('showing') . ' _START_ ' . __('to') . ' _END_ ' . __('of') . ' _TOTAL_ ' . __('entries') . '",
            paginate: {
                first: "' . __('first') . '",
                last: "' . __('last') . '",
                next: "' . __('next') . '",
                previous: "' . __('previous') . '"
            }
        },
        columnDefs: [
            { orderable: false, targets: -1 } // Disable sorting on actions column
        ]
    });

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll("[data-bs-toggle=\"tooltip\"]"));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Auto-submit form when filters change
    $("#hospital_id, #device_id, #status").on("change", function() {
        showLoadingSpinner();
        this.form.submit();
    });

    // Load devices when hospital changes
    $("#hospital_id").on("change", function() {
        const hospitalId = this.value;
        const deviceSelect = $("#device_id");

        // Clear current options
        deviceSelect.html("<option value=\"\">' . __('all_devices') . '</option>");

        if (hospitalId) {
            // Show loading
            deviceSelect.prop("disabled", true);

            // Fetch devices for the selected hospital
            fetch("' . getBaseUrl() . '/api/get_devices?hospital_id=" + hospitalId)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        data.data.forEach(device => {
                            const option = $("<option></option>");
                            option.val(device.id);
                            option.text(device.name + " (" + device.serial_number + ")");
                            deviceSelect.append(option);
                        });
                    }
                })
                .catch(error => {
                    console.error("Error loading devices:", error);
                    showToast("' . __('error_loading_devices') . '", "error");
                })
                .finally(() => {
                    deviceSelect.prop("disabled", false);
                });
        }
    });

    // Restore saved view preference
    const savedView = localStorage.getItem("maintenanceView");
    if (savedView === "timeline") {
        toggleView("timeline");
    }
});

// View toggle functionality
function toggleView(viewType) {
    const tableView = document.getElementById("tableView");
    const timelineView = document.getElementById("timelineView");
    const tableBtn = document.getElementById("tableViewBtn");
    const timelineBtn = document.getElementById("timelineViewBtn");

    if (viewType === "table") {
        tableView.style.display = "block";
        timelineView.style.display = "none";
        tableBtn.classList.add("active");
        timelineBtn.classList.remove("active");
        localStorage.setItem("maintenanceView", "table");
    } else {
        tableView.style.display = "none";
        timelineView.style.display = "block";
        timelineBtn.classList.add("active");
        tableBtn.classList.remove("active");
        localStorage.setItem("maintenanceView", "timeline");
    }
}

// Delete confirmation
function confirmDelete(scheduleId, scheduleName) {
    document.getElementById("scheduleName").textContent = scheduleName;
    document.getElementById("deleteForm").action = "' . getBaseUrl() . '/maintenance/delete_schedule/" + scheduleId;
    new bootstrap.Modal(document.getElementById("deleteModal")).show();
}

// Clear filters
function clearFilters() {
    showLoadingSpinner();
    window.location.href = "' . getBaseUrl() . '/maintenance";
}

// Print schedules
function printSchedules() {
    // Ensure table view is shown for printing
    const currentView = localStorage.getItem("maintenanceView");
    toggleView("table");

    window.print();

    // Restore previous view after printing
    if (currentView === "timeline") {
        setTimeout(() => toggleView("timeline"), 1000);
    }
}

// Loading spinner
function showLoadingSpinner() {
    const spinner = document.createElement("div");
    spinner.className = "d-flex justify-content-center align-items-center position-fixed top-0 start-0 w-100 h-100";
    spinner.style.backgroundColor = "rgba(0,0,0,0.5)";
    spinner.style.zIndex = "9999";
    spinner.innerHTML = `
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">' . __('loading') . '...</span>
        </div>
    `;
    document.body.appendChild(spinner);
}

// Show toast notification
function showToast(message, type = "info") {
    const toastContainer = document.getElementById("toast-container") || createToastContainer();

    const toast = document.createElement("div");
    toast.className = `toast align-items-center text-white bg-${type === "error" ? "danger" : type} border-0`;
    toast.setAttribute("role", "alert");
    toast.setAttribute("aria-live", "assertive");
    toast.setAttribute("aria-atomic", "true");

    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-${type === "success" ? "check" : type === "error" ? "times" : "info"}-circle me-2"></i>
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    toastContainer.appendChild(toast);

    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    // Remove toast after it hides
    toast.addEventListener("hidden.bs.toast", function() {
        toast.remove();
    });
}

// Create toast container if it doesn\'t exist
function createToastContainer() {
    const container = document.createElement("div");
    container.id = "toast-container";
    container.className = "toast-container position-fixed top-0 end-0 p-3";
    container.style.zIndex = "9999";
    document.body.appendChild(container);
    return container;
}

// Keyboard shortcuts
document.addEventListener("keydown", function(e) {
    // Ctrl/Cmd + N for new schedule
    if ((e.ctrlKey || e.metaKey) && e.key === "n") {
        e.preventDefault();
        const addBtn = document.querySelector("a[href*=\"/maintenance/create_schedule\"]");
        if (addBtn) addBtn.click();
    }

    // Ctrl/Cmd + L for logs
    if ((e.ctrlKey || e.metaKey) && e.key === "l") {
        e.preventDefault();
        const logsBtn = document.querySelector("a[href*=\"/maintenance/logs\"]");
        if (logsBtn) logsBtn.click();
    }

    // T key to toggle view
    if (e.key === "t" && !e.ctrlKey && !e.metaKey) {
        const currentView = localStorage.getItem("maintenanceView") || "table";
        toggleView(currentView === "table" ? "timeline" : "table");
    }
});

// Auto-refresh overdue schedules every 5 minutes
setInterval(function() {
    const overdueRows = document.querySelectorAll(".schedule-row.table-danger");
    if (overdueRows.length > 0) {
        // Pulse animation for overdue items
        overdueRows.forEach(row => {
            row.style.animation = "pulse 1s ease-in-out";
            setTimeout(() => {
                row.style.animation = "";
            }, 1000);
        });
    }
}, 300000); // 5 minutes
</script>
';

// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
