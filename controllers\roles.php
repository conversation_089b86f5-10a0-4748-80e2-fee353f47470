<?php
/**
 * Roles Controller
 * 
 * This file handles role-related operations.
 */

// Check if the user is logged in
requireLogin();

// Get the current user
$currentUser = getCurrentUser();

// Handle actions
$action = isset($url[1]) ? $url[1] : 'index';
$roleId = isset($url[2]) ? (int)$url[2] : 0;

switch ($action) {
    case 'index':
        // Check if the user has permission to view roles
        if (!hasPermission('view_roles') && !hasPermission('manage_roles')) {
            setFlashMessage('error', __('access_denied'));
            redirect('dashboard');
        }
        
        // Get all roles
        $roles = $roleModel->getAll();
        
        // Include the roles index view
        include 'views/roles/index.php';
        break;
        
    case 'create':
        // Check if the user has permission to manage roles
        requirePermission('manage_roles');
        
        // Check for stored form data and errors from previous submission
        $roleData = $_SESSION['form_data'] ?? [
            'name' => '',
            'display_name' => '',
            'description' => '',
            'permissions' => [],
            'display_order' => 0
        ];
        
        $errors = $_SESSION['form_errors'] ?? [];
        
        // Clear stored form data and errors
        unset($_SESSION['form_data']);
        unset($_SESSION['form_errors']);
        
        // Get available permissions
        $availablePermissions = $roleModel->getAvailablePermissions();
        
        // Include the roles create view
        include 'views/roles/create.php';
        break;

    case 'store':
        // Check if the user has permission to manage roles
        requirePermission('manage_roles');

        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validate CSRF token
            if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
                setFlashMessage('error', __('invalid_csrf_token'));
                redirect('roles/create');
            }

            $roleData = [
                'name' => trim($_POST['name'] ?? ''),
                'display_name' => trim($_POST['display_name'] ?? ''),
                'description' => trim($_POST['description'] ?? ''),
                'permissions' => $_POST['permissions'] ?? [],
                'display_order' => (int)($_POST['display_order'] ?? 0)
            ];

            // Validate data
            $errors = [];

            if (empty($roleData['name'])) {
                $errors['name'] = __('required_field');
            } elseif (!preg_match('/^[a-z_]+$/', $roleData['name'])) {
                $errors['name'] = __('invalid_role_name');
            } elseif ($roleModel->nameExists($roleData['name'])) {
                $errors['name'] = __('role_name_exists');
            }

            if (empty($roleData['display_name'])) {
                $errors['display_name'] = __('required_field');
            }

            if (!is_array($roleData['permissions'])) {
                $roleData['permissions'] = [];
            }

            // If no errors, create the role
            if (empty($errors)) {
                $roleId = $roleModel->create($roleData);

                if ($roleId) {
                    // Log the action
                    logAction('create_role', 'Created role: ' . $roleData['name']);

                    // Clear any stored form data
                    unset($_SESSION['form_data']);
                    unset($_SESSION['form_errors']);

                    // Set success message and redirect
                    setFlashMessage('success', __('role') . ' "' . $roleData['display_name'] . '" ' . __('created_successfully'));
                    redirect('roles');
                } else {
                    $errors['general'] = __('create_failed');
                }
            }

            // If there are errors, redirect back to create form with errors
            if (!empty($errors)) {
                $_SESSION['form_errors'] = $errors;
                $_SESSION['form_data'] = $roleData;
                redirect('roles/create');
            }
        } else {
            // If not POST request, redirect to create form
            redirect('roles/create');
        }
        break;

    case 'edit':
        // Check if the user has permission to manage roles
        requirePermission('manage_roles');
        
        // Get the role
        $role = $roleModel->getById($roleId);
        
        // Check if the role exists
        if (!$role) {
            setFlashMessage('error', __('not_found'));
            redirect('roles');
        }
        
        // Check if it's a system role
        if ($role['is_system']) {
            setFlashMessage('error', __('cannot_edit_system_role'));
            redirect('roles');
        }
        
        // Check for stored form data and errors from previous submission
        $roleData = $_SESSION['form_data'] ?? [
            'name' => $role['name'],
            'display_name' => $role['display_name'],
            'description' => $role['description'],
            'permissions' => json_decode($role['permissions'], true) ?? [],
            'display_order' => $role['display_order']
        ];
        
        $errors = $_SESSION['form_errors'] ?? [];
        
        // Clear stored form data and errors
        unset($_SESSION['form_data']);
        unset($_SESSION['form_errors']);
        
        // Get available permissions
        $availablePermissions = $roleModel->getAvailablePermissions();
        
        // Include the roles edit view
        include 'views/roles/edit.php';
        break;

    case 'update':
        // Check if the user has permission to manage roles
        requirePermission('manage_roles');
        
        // Get the role
        $role = $roleModel->getById($roleId);
        
        // Check if the role exists
        if (!$role) {
            setFlashMessage('error', __('not_found'));
            redirect('roles');
        }
        
        // Check if it's a system role
        if ($role['is_system']) {
            setFlashMessage('error', __('cannot_edit_system_role'));
            redirect('roles');
        }

        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validate CSRF token
            if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
                setFlashMessage('error', __('invalid_csrf_token'));
                redirect('roles/edit/' . $roleId);
            }

            $roleData = [
                'name' => trim($_POST['name'] ?? $role['name']),
                'display_name' => trim($_POST['display_name'] ?? $role['display_name']),
                'description' => trim($_POST['description'] ?? $role['description']),
                'permissions' => $_POST['permissions'] ?? [],
                'display_order' => (int)($_POST['display_order'] ?? $role['display_order'])
            ];

            // Validate data
            $errors = [];

            if (empty($roleData['name'])) {
                $errors['name'] = __('required_field');
            } elseif (!preg_match('/^[a-z_]+$/', $roleData['name'])) {
                $errors['name'] = __('invalid_role_name');
            } elseif ($roleModel->nameExists($roleData['name'], $roleId)) {
                $errors['name'] = __('role_name_exists');
            }

            if (empty($roleData['display_name'])) {
                $errors['display_name'] = __('required_field');
            }

            if (!is_array($roleData['permissions'])) {
                $roleData['permissions'] = [];
            }

            // If no errors, update the role
            if (empty($errors)) {
                $result = $roleModel->update($roleId, $roleData);
                
                if ($result) {
                    // Log the action
                    logAction('update_role', 'Updated role: ' . $roleData['name']);
                    
                    // Set flash message
                    setFlashMessage('success', __('updated_successfully', [__('role')]));
                    
                    // Redirect to roles index
                    redirect('roles');
                } else {
                    $errors['general'] = __('update_failed');
                }
            }

            // If there are errors, redirect back to edit form with errors
            if (!empty($errors)) {
                $_SESSION['form_errors'] = $errors;
                $_SESSION['form_data'] = $roleData;
                redirect('roles/edit/' . $roleId);
            }
        } else {
            // If not POST request, redirect to edit form
            redirect('roles/edit/' . $roleId);
        }
        break;
        
    case 'delete':
        // Check if the user has permission to manage roles
        requirePermission('manage_roles');
        
        // Get the role
        $role = $roleModel->getById($roleId);
        
        // Check if the role exists
        if (!$role) {
            setFlashMessage('error', __('not_found'));
            redirect('roles');
        }
        
        // Check if it's a system role
        if ($role['is_system']) {
            setFlashMessage('error', __('cannot_delete_system_role'));
            redirect('roles');
        }
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm']) && $_POST['confirm'] === 'yes') {
            $result = $roleModel->delete($roleId);
            
            if ($result) {
                // Log the action
                logAction('delete_role', 'Deleted role: ' . $role['name']);
                
                // Set flash message
                setFlashMessage('success', __('deleted_successfully', [__('role')]));
            } else {
                setFlashMessage('error', __('delete_failed_role_in_use'));
            }
            
            // Redirect to roles index
            redirect('roles');
        }
        
        // Include the roles delete view
        include 'views/roles/delete.php';
        break;
        
    case 'view':
        // Check if the user has permission to view roles
        if (!hasPermission('view_roles') && !hasPermission('manage_roles')) {
            setFlashMessage('error', __('access_denied'));
            redirect('dashboard');
        }
        
        // Get the role
        $role = $roleModel->getById($roleId);
        
        // Check if the role exists
        if (!$role) {
            setFlashMessage('error', __('not_found'));
            redirect('roles');
        }
        
        // Get role statistics
        $stats = $roleModel->getStatistics($roleId);
        
        // Get available permissions for display
        $availablePermissions = $roleModel->getAvailablePermissions();
        $rolePermissions = json_decode($role['permissions'], true) ?? [];
        
        // Include the roles view
        include 'views/roles/view.php';
        break;
        
    default:
        // 404 Not Found
        header("HTTP/1.0 404 Not Found");
        include 'views/404.php';
        break;
}
