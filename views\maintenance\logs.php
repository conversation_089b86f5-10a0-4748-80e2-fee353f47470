<?php
/**
 * Maintenance Logs View
 * 
 * This file displays the maintenance logs.
 */

// Set page title
$pageTitle = __('maintenance_logs');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3"><?php echo __('maintenance_logs'); ?></h1>
    
    <div class="btn-group">
        <a href="<?php echo getBaseUrl(); ?>/maintenance" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_schedules'); ?>
        </a>
        
        <?php if (hasPermission('manage_maintenance')): ?>
            <a href="<?php echo getBaseUrl(); ?>/maintenance/create_log" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i><?php echo __('create_log'); ?>
            </a>
        <?php endif; ?>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?php echo getBaseUrl(); ?>/maintenance/logs" class="row g-3">
            <?php if (hasPermission('manage_hospitals')): ?>
                <div class="col-md-3">
                    <label for="hospital_id" class="form-label"><?php echo __('hospital'); ?></label>
                    <select class="form-select" id="hospital_id" name="hospital_id" onchange="loadDevices(this.value)">
                        <option value=""><?php echo __('all_hospitals'); ?></option>
                        <?php foreach ($hospitals as $hospital): ?>
                            <option value="<?php echo $hospital['id']; ?>" <?php echo ($hospitalId == $hospital['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($hospital['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            <?php endif; ?>
            
            <div class="col-md-3">
                <label for="device_id" class="form-label"><?php echo __('device'); ?></label>
                <select class="form-select" id="device_id" name="device_id">
                    <option value=""><?php echo __('all_devices'); ?></option>
                    <?php foreach ($devices as $device): ?>
                        <option value="<?php echo $device['id']; ?>" <?php echo ($deviceId == $device['id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($device['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="schedule_id" class="form-label"><?php echo __('schedule'); ?></label>
                <select class="form-select" id="schedule_id" name="schedule_id">
                    <option value=""><?php echo __('all_schedules'); ?></option>
                    <?php foreach ($schedules ?? [] as $schedule): ?>
                        <option value="<?php echo $schedule['id']; ?>" <?php echo ($scheduleId == $schedule['id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($schedule['title']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-filter me-2"></i><?php echo __('filter'); ?>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Maintenance Logs Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-clipboard-list me-2"></i><?php echo __('maintenance_logs'); ?>
            <span class="badge bg-secondary ms-2"><?php echo count($logs); ?></span>
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($logs)): ?>
            <div class="text-center py-4">
                <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                <h5 class="text-muted"><?php echo __('no_logs_found'); ?></h5>
                <p class="text-muted"><?php echo __('no_logs_message'); ?></p>
                
                <?php if (hasPermission('manage_maintenance')): ?>
                    <a href="<?php echo getBaseUrl(); ?>/maintenance/create_log" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i><?php echo __('create_first_log'); ?>
                    </a>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th><?php echo __('device'); ?></th>
                            <th><?php echo __('schedule'); ?></th>
                            <th><?php echo __('performed_by'); ?></th>
                            <th><?php echo __('performed_date'); ?></th>
                            <th><?php echo __('status'); ?></th>
                            <th><?php echo __('notes'); ?></th>
                            <th><?php echo __('actions'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($logs as $log): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="device-icon me-2">
                                            <i class="fas fa-medical-kit text-primary"></i>
                                        </div>
                                        <div>
                                            <div class="fw-bold"><?php echo htmlspecialchars($log['device_name']); ?></div>
                                            <small class="text-muted"><?php echo htmlspecialchars($log['serial_number']); ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <?php if ($log['schedule_title']): ?>
                                        <a href="<?php echo getBaseUrl(); ?>/maintenance/view_schedule/<?php echo $log['maintenance_schedule_id']; ?>" class="text-decoration-none">
                                            <?php echo htmlspecialchars($log['schedule_title']); ?>
                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted"><?php echo __('unscheduled'); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="user-avatar me-2">
                                            <div class="bg-info text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 32px; height: 32px; font-size: 0.8rem;">
                                                <?php echo strtoupper(substr($log['performed_by_name'], 0, 1)); ?>
                                            </div>
                                        </div>
                                        <div>
                                            <div class="fw-bold"><?php echo htmlspecialchars($log['performed_by_name']); ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="fw-bold"><?php echo date('M d, Y', strtotime($log['performed_date'])); ?></span><br>
                                    <small class="text-muted"><?php echo date('H:i', strtotime($log['created_at'])); ?></small>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo getMaintenanceStatusColor($log['status']); ?>">
                                        <?php echo __($log['status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($log['notes']): ?>
                                        <span class="text-truncate d-inline-block" style="max-width: 200px;" title="<?php echo htmlspecialchars($log['notes']); ?>">
                                            <?php echo htmlspecialchars($log['notes']); ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="text-muted"><?php echo __('no_notes'); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="<?php echo getBaseUrl(); ?>/maintenance/view_log/<?php echo $log['id']; ?>" 
                                           class="btn btn-outline-primary" 
                                           title="<?php echo __('view_log'); ?>">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        
                                        <?php if (hasPermission('manage_maintenance')): ?>
                                            <a href="<?php echo getBaseUrl(); ?>/maintenance/edit_log/<?php echo $log['id']; ?>" 
                                               class="btn btn-outline-warning" 
                                               title="<?php echo __('edit_log'); ?>">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
// Load devices based on selected hospital
function loadDevices(hospitalId) {
    const deviceSelect = document.getElementById('device_id');
    
    // Clear current options
    deviceSelect.innerHTML = '<option value=""><?php echo __('loading'); ?>...</option>';
    
    if (hospitalId) {
        fetch(`<?php echo getBaseUrl(); ?>/api/devices?hospital_id=${hospitalId}`)
            .then(response => response.json())
            .then(data => {
                deviceSelect.innerHTML = '<option value=""><?php echo __('all_devices'); ?></option>';
                data.forEach(device => {
                    deviceSelect.innerHTML += `<option value="${device.id}">${device.name}</option>`;
                });
            })
            .catch(error => {
                console.error('Error loading devices:', error);
                deviceSelect.innerHTML = '<option value=""><?php echo __('error_loading_devices'); ?></option>';
            });
    } else {
        deviceSelect.innerHTML = '<option value=""><?php echo __('all_devices'); ?></option>';
    }
}
</script>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
