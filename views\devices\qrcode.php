<?php
/**
 * Device QR Code View
 * 
 * This file displays the QR code for a device.
 */

// Set page title
$pageTitle = __('qr_code') . ' - ' . $device['name'];

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3"><?php echo __('qr_code'); ?>: <?php echo htmlspecialchars($device['name']); ?></h1>
    
    <div class="btn-group">
        <a href="<?php echo getBaseUrl(); ?>/devices" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_list'); ?>
        </a>
        <a href="<?php echo getBaseUrl(); ?>/devices/view/<?php echo $device['id']; ?>" class="btn btn-primary">
            <i class="fas fa-eye me-2"></i><?php echo __('view_device'); ?>
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header text-center">
                <h5 class="card-title mb-0"><?php echo __('device_qr_code'); ?></h5>
            </div>
            <div class="card-body text-center">
                <!-- Device Information -->
                <div class="mb-4">
                    <h6 class="text-muted"><?php echo __('device_information'); ?></h6>
                    <table class="table table-borderless table-sm mx-auto" style="max-width: 400px;">
                        <tr>
                            <td><strong><?php echo __('name'); ?>:</strong></td>
                            <td><?php echo htmlspecialchars($device['name']); ?></td>
                        </tr>
                        <tr>
                            <td><strong><?php echo __('model'); ?>:</strong></td>
                            <td><?php echo htmlspecialchars($device['model']); ?></td>
                        </tr>
                        <tr>
                            <td><strong><?php echo __('serial_number'); ?>:</strong></td>
                            <td><?php echo htmlspecialchars($device['serial_number']); ?></td>
                        </tr>
                        <tr>
                            <td><strong><?php echo __('hospital'); ?>:</strong></td>
                            <td><?php echo htmlspecialchars($device['hospital_name']); ?></td>
                        </tr>
                        <tr>
                            <td><strong><?php echo __('department'); ?>:</strong></td>
                            <td><?php echo htmlspecialchars($device['department_name']); ?></td>
                        </tr>
                    </table>
                </div>
                
                <!-- QR Code -->
                <div class="mb-4">
                    <?php if (!empty($device['qr_code']) && file_exists($device['qr_code'])): ?>
                        <img src="<?php echo getBaseUrl(); ?>/<?php echo $device['qr_code']; ?>" alt="QR Code" class="img-fluid border" style="max-width: 300px;">
                        
                        <div class="mt-3">
                            <p class="text-muted small">
                                <?php echo __('qr_code_description'); ?>
                            </p>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="mt-4">
                            <a href="<?php echo getBaseUrl(); ?>/<?php echo $device['qr_code']; ?>" download class="btn btn-primary me-2">
                                <i class="fas fa-download me-2"></i><?php echo __('download_qr'); ?>
                            </a>
                            
                            <button type="button" class="btn btn-outline-secondary me-2" onclick="printQR()">
                                <i class="fas fa-print me-2"></i><?php echo __('print_qr'); ?>
                            </button>
                            
                            <?php if (hasPermission('manage_devices')): ?>
                            <a href="<?php echo getBaseUrl(); ?>/devices/regenerate_qr/<?php echo $device['id']; ?>" class="btn btn-outline-warning" onclick="return confirm('<?php echo __('regenerate_qr_confirm'); ?>')">
                                <i class="fas fa-sync me-2"></i><?php echo __('regenerate_qr'); ?>
                            </a>
                            <?php endif; ?>
                        </div>
                        
                    <?php else: ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo __('no_qr_code_generated'); ?>
                        </div>
                        
                        <?php if (hasPermission('manage_devices')): ?>
                        <a href="<?php echo getBaseUrl(); ?>/devices/generate_qr/<?php echo $device['id']; ?>" class="btn btn-primary">
                            <i class="fas fa-qrcode me-2"></i><?php echo __('generate_qr_code'); ?>
                        </a>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
                
                <!-- QR Code URL -->
                <?php if (!empty($device['qr_code'])): ?>
                <div class="mt-4">
                    <h6 class="text-muted"><?php echo __('device_url'); ?></h6>
                    <div class="input-group">
                        <input type="text" class="form-control" id="deviceUrl" value="<?php echo getBaseUrl(); ?>/devices/view/<?php echo $device['id']; ?>" readonly>
                        <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard()">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                    <small class="text-muted"><?php echo __('qr_code_url_description'); ?></small>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Print Styles -->
<style media="print">
    .btn, .card-header, .navbar, .sidebar {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .card-body {
        padding: 0 !important;
    }
    
    @page {
        margin: 1cm;
    }
    
    body {
        font-size: 12pt;
    }
    
    .qr-print-info {
        page-break-inside: avoid;
    }
</style>

<script>
function printQR() {
    window.print();
}

function copyToClipboard() {
    const urlInput = document.getElementById('deviceUrl');
    urlInput.select();
    urlInput.setSelectionRange(0, 99999); // For mobile devices
    
    try {
        document.execCommand('copy');
        
        // Show success message
        const button = event.target.closest('button');
        const originalHTML = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i>';
        button.classList.remove('btn-outline-secondary');
        button.classList.add('btn-success');
        
        setTimeout(() => {
            button.innerHTML = originalHTML;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-secondary');
        }, 2000);
        
    } catch (err) {
        console.error('Failed to copy URL: ', err);
        alert('<?php echo __('copy_failed'); ?>');
    }
}

// Auto-focus on URL input for easy copying
document.addEventListener('DOMContentLoaded', function() {
    const urlInput = document.getElementById('deviceUrl');
    if (urlInput) {
        urlInput.addEventListener('click', function() {
            this.select();
        });
    }
});
</script>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
