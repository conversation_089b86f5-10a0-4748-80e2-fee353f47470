<?php
/**
 * Installation Script
 * 
 * This file creates the database tables and initial data.
 */

// Check if the script is being run directly
if (!defined('BASEPATH')) {
    define('BASEPATH', dirname(__DIR__));
}

// Create database tables
$sql = [
    // Roles table
    "CREATE TABLE IF NOT EXISTS roles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(50) NOT NULL UNIQUE,
        display_name VARCHAR(100) NOT NULL,
        description TEXT NULL,
        permissions JSON NULL,
        display_order INT NOT NULL DEFAULT 0,
        is_system BOOLEAN NOT NULL DEFAULT FALSE,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX (name),
        INDEX (display_order)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

    // Hospitals table
    "CREATE TABLE IF NOT EXISTS hospitals (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        address TEXT NULL,
        city VARCHAR(100) NULL,
        country VARCHAR(100) NULL,
        phone VARCHAR(50) NULL,
        email VARCHAR(255) NULL,
        website VARCHAR(255) NULL,
        notes TEXT NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX (name)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    // Departments table
    "CREATE TABLE IF NOT EXISTS departments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        hospital_id INT NOT NULL,
        name VARCHAR(255) NOT NULL,
        location VARCHAR(100) NULL,
        phone VARCHAR(50) NULL,
        email VARCHAR(255) NULL,
        description TEXT NULL,
        notes TEXT NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (hospital_id) REFERENCES hospitals(id) ON DELETE CASCADE,
        INDEX (hospital_id),
        INDEX (name)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    // Users table
    "CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        email VARCHAR(100) NOT NULL UNIQUE,
        full_name VARCHAR(100) NOT NULL,
        role ENUM('admin', 'engineer', 'technician', 'staff') NOT NULL DEFAULT 'staff',
        permissions JSON NULL,
        hospital_id INT NULL,
        language VARCHAR(10) NOT NULL DEFAULT 'en',
        last_login DATETIME NULL,
        status ENUM('active', 'inactive', 'locked') NOT NULL DEFAULT 'active',
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (hospital_id) REFERENCES hospitals(id) ON DELETE SET NULL,
        INDEX (hospital_id),
        INDEX (role)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    // Devices table
    "CREATE TABLE IF NOT EXISTS devices (
        id INT AUTO_INCREMENT PRIMARY KEY,
        hospital_id INT NOT NULL,
        department_id INT NOT NULL,
        name VARCHAR(255) NOT NULL,
        model VARCHAR(255) NULL,
        serial_number VARCHAR(255) NOT NULL UNIQUE,
        manufacturer VARCHAR(255) NULL,
        category VARCHAR(100) NULL DEFAULT 'Other',
        purchase_date DATE NULL,
        warranty_expiry DATE NULL,
        status ENUM('operational', 'under_maintenance', 'out_of_order', 'retired') NOT NULL DEFAULT 'operational',
        maintenance_interval INT NULL COMMENT 'Maintenance interval in days',
        last_maintenance_date DATE NULL,
        next_maintenance_date DATE NULL,
        qr_code VARCHAR(255) NULL,
        location VARCHAR(100) NULL,
        notes TEXT NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (hospital_id) REFERENCES hospitals(id) ON DELETE CASCADE,
        FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
        INDEX (hospital_id),
        INDEX (department_id),
        INDEX (status),
        INDEX (serial_number)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    // Maintenance schedules table
    "CREATE TABLE IF NOT EXISTS maintenance_schedules (
        id INT AUTO_INCREMENT PRIMARY KEY,
        device_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT NULL,
        scheduled_date DATE NOT NULL,
        frequency ENUM('once', 'daily', 'weekly', 'monthly', 'quarterly', 'yearly') NOT NULL DEFAULT 'once',
        status ENUM('scheduled', 'completed', 'overdue', 'cancelled') NOT NULL DEFAULT 'scheduled',
        priority ENUM('low', 'medium', 'high') NOT NULL DEFAULT 'medium',
        created_by INT NOT NULL,
        notes TEXT NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
        INDEX (device_id),
        INDEX (scheduled_date),
        INDEX (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    // Maintenance logs table
    "CREATE TABLE IF NOT EXISTS maintenance_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        maintenance_schedule_id INT NULL,
        device_id INT NOT NULL,
        performed_by INT NOT NULL,
        performed_date DATE NOT NULL,
        maintenance_date DATE NOT NULL,
        actions_taken TEXT NULL,
        parts_replaced TEXT NULL,
        results TEXT NULL,
        recommendations TEXT NULL,
        status ENUM('completed', 'incomplete') NOT NULL DEFAULT 'completed',
        notes TEXT NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (maintenance_schedule_id) REFERENCES maintenance_schedules(id) ON DELETE SET NULL,
        FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE,
        FOREIGN KEY (performed_by) REFERENCES users(id) ON DELETE CASCADE,
        INDEX (device_id),
        INDEX (performed_date)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    // Tickets table
    "CREATE TABLE IF NOT EXISTS tickets (
        id INT AUTO_INCREMENT PRIMARY KEY,
        device_id INT NULL,
        reported_by INT NOT NULL,
        assigned_to INT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT NULL,
        priority ENUM('low', 'medium', 'high', 'critical') NOT NULL DEFAULT 'medium',
        status ENUM('open', 'in_progress', 'resolved', 'closed') NOT NULL DEFAULT 'open',
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE SET NULL,
        FOREIGN KEY (reported_by) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL,
        INDEX (device_id),
        INDEX (status),
        INDEX (priority)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    // Ticket updates table
    "CREATE TABLE IF NOT EXISTS ticket_updates (
        id INT AUTO_INCREMENT PRIMARY KEY,
        ticket_id INT NOT NULL,
        user_id INT NOT NULL,
        comment TEXT NULL,
        status_change VARCHAR(50) NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (ticket_id) REFERENCES tickets(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX (ticket_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    // Notifications table
    "CREATE TABLE IF NOT EXISTS notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        message TEXT NULL,
        type ENUM('info', 'success', 'warning', 'error') NOT NULL DEFAULT 'info',
        reference_id INT NULL,
        reference_type VARCHAR(50) NULL,
        link VARCHAR(255) NULL,
        is_read BOOLEAN NOT NULL DEFAULT 0,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX (user_id),
        INDEX (is_read)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    // Settings table
    "CREATE TABLE IF NOT EXISTS settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(50) NOT NULL UNIQUE,
        setting_value TEXT NOT NULL,
        description TEXT NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

    // Activity logs table
    "CREATE TABLE IF NOT EXISTS activity_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NULL,
        action VARCHAR(255) NOT NULL,
        details TEXT NULL,
        ip_address VARCHAR(45) NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
        INDEX (user_id),
        INDEX (action),
        INDEX (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

    // Security logs table
    "CREATE TABLE IF NOT EXISTS security_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        event VARCHAR(50) NOT NULL,
        username VARCHAR(50) NULL,
        ip_address VARCHAR(45) NOT NULL,
        details TEXT NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        INDEX (event),
        INDEX (username),
        INDEX (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

    // User permissions table
    "CREATE TABLE IF NOT EXISTS user_permissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        permission VARCHAR(50) NOT NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY (user_id, permission),
        INDEX (user_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    // Remember tokens table
    "CREATE TABLE IF NOT EXISTS remember_tokens (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        token VARCHAR(255) NOT NULL UNIQUE,
        expires_at DATETIME NOT NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

    // Password resets table
    "CREATE TABLE IF NOT EXISTS password_resets (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        token VARCHAR(255) NOT NULL UNIQUE,
        expires_at DATETIME NOT NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_reset (user_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
];

// Execute SQL statements
foreach ($sql as $query) {
    try {
        $pdo->exec($query);
    } catch (PDOException $e) {
        die("Error creating tables: " . $e->getMessage());
    }
}

// Insert default roles
try {
    $stmt = $pdo->prepare("
        INSERT IGNORE INTO roles (name, display_name, description, permissions, display_order, is_system) VALUES
        (?, ?, ?, ?, ?, ?),
        (?, ?, ?, ?, ?, ?),
        (?, ?, ?, ?, ?, ?),
        (?, ?, ?, ?, ?, ?)
    ");

    $stmt->execute([
        'admin', 'Administrator', 'Full system access with all permissions',
        json_encode(['view_dashboard','manage_users','manage_hospitals','manage_departments','manage_devices','manage_maintenance','manage_tickets','view_reports','export_data','manage_roles','view_roles']),
        1, 1,
        'engineer', 'Engineer', 'Technical staff with device and maintenance management',
        json_encode(['view_dashboard','view_users','view_hospitals','manage_departments','manage_devices','manage_maintenance','manage_tickets','view_reports','export_data']),
        2, 1,
        'technician', 'Technician', 'Maintenance and repair technician',
        json_encode(['view_dashboard','view_departments','view_devices','manage_maintenance','manage_tickets','view_reports']),
        3, 1,
        'staff', 'Staff', 'Basic staff with limited access',
        json_encode(['view_dashboard','view_departments','view_devices','view_maintenance','view_tickets']),
        4, 1
    ]);
} catch (PDOException $e) {
    // Ignore if roles already exist
}

// Insert default settings
try {
    $stmt = $pdo->prepare("
        INSERT IGNORE INTO settings (setting_key, setting_value, description) VALUES
        (?, ?, ?),
        (?, ?, ?),
        (?, ?, ?),
        (?, ?, ?),
        (?, ?, ?),
        (?, ?, ?),
        (?, ?, ?),
        (?, ?, ?),
        (?, ?, ?),
        (?, ?, ?),
        (?, ?, ?),
        (?, ?, ?),
        (?, ?, ?),
        (?, ?, ?)
    ");

    $stmt->execute([
        'site_name', 'Medical Device Management System', 'The name of the site',
        'site_description', 'A system for managing medical devices in hospitals', 'The description of the site',
        'email_from', '<EMAIL>', 'The email address to send emails from',
        'email_from_name', 'Medical Device Management System', 'The name to send emails from',
        'smtp_host', '', 'SMTP host for sending emails',
        'smtp_port', '587', 'SMTP port for sending emails',
        'smtp_username', '', 'SMTP username for sending emails',
        'smtp_password', '', 'SMTP password for sending emails',
        'smtp_encryption', 'tls', 'SMTP encryption for sending emails',
        'maintenance_notification_days', '7', 'Number of days before maintenance to send notification',
        'warranty_expiry_notification_days', '30', 'Number of days before warranty expiry to send notification',
        'default_language', 'en', 'Default language for the system',
        'enable_qr_codes', '1', 'Enable QR codes for devices',
        'enable_email_notifications', '0', 'Enable email notifications'
    ]);
} catch (PDOException $e) {
    // Ignore if settings already exist
}

// Create default hospital
try {
    $stmt = $pdo->prepare("
        INSERT INTO hospitals (name, address, phone, email)
        VALUES (?, ?, ?, ?)
    ");
    
    $stmt->execute([
        'Main Hospital',
        '123 Main Street, City',
        '+**********',
        '<EMAIL>'
    ]);
    
    $hospitalId = $pdo->lastInsertId();
} catch (PDOException $e) {
    die("Error creating default hospital: " . $e->getMessage());
}

// Create default departments
try {
    $departments = [
        ['Cardiology', 'Heart and cardiovascular system'],
        ['Neurology', 'Brain and nervous system'],
        ['Radiology', 'Medical imaging'],
        ['Laboratory', 'Medical testing'],
        ['Emergency', 'Emergency care']
    ];
    
    $stmt = $pdo->prepare("
        INSERT INTO departments (hospital_id, name, description)
        VALUES (?, ?, ?)
    ");
    
    foreach ($departments as $department) {
        $stmt->execute([
            $hospitalId,
            $department[0],
            $department[1]
        ]);
    }
} catch (PDOException $e) {
    die("Error creating default departments: " . $e->getMessage());
}

// Create default admin user
try {
    $password = password_hash('admin123', PASSWORD_DEFAULT);
    $permissions = json_encode([
        'view_dashboard',
        'manage_users',
        'manage_hospitals',
        'manage_departments',
        'manage_devices',
        'manage_maintenance',
        'manage_tickets',
        'view_reports',
        'export_data'
    ]);
    
    $stmt = $pdo->prepare("
        INSERT INTO users (
            hospital_id, username, password, email, full_name,
            role, permissions, language, status
        ) VALUES (
            ?, ?, ?, ?, ?, ?, ?, ?, ?
        )
    ");

    $stmt->execute([
        $hospitalId,
        'admin',
        $password,
        '<EMAIL>',
        'System Administrator',
        'admin',
        $permissions,
        'en',
        'active'
    ]);
} catch (PDOException $e) {
    die("Error creating default admin user: " . $e->getMessage());
}

// Create uploads directory if it doesn't exist
$uploadsDir = BASEPATH . '/uploads/qrcodes';
if (!is_dir($uploadsDir)) {
    mkdir($uploadsDir, 0755, true);
}

// Create success message
echo '<div style="max-width: 600px; margin: 50px auto; padding: 20px; background-color: #f8f9fa; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">';
echo '<h2 style="color: #28a745;">Installation Successful!</h2>';
echo '<p>The Medical Device Management System has been successfully installed.</p>';
echo '<p>Default admin credentials:</p>';
echo '<ul>';
echo '<li><strong>Username:</strong> admin</li>';
echo '<li><strong>Password:</strong> admin123</li>';
echo '</ul>';
echo '<p><strong>Important:</strong> Please change the default password after logging in.</p>';
echo '<p><a href="../login.php" style="display: inline-block; padding: 10px 15px; background-color: #007bff; color: #fff; text-decoration: none; border-radius: 3px;">Go to Login Page</a></p>';
echo '</div>';

// Log installation
try {
    $stmt = $pdo->prepare("
        INSERT INTO activity_logs (action, details, ip_address)
        VALUES (?, ?, ?)
    ");
    
    $stmt->execute([
        'system_install',
        'System installed successfully',
        $_SERVER['REMOTE_ADDR'] ?? 'Unknown'
    ]);
} catch (PDOException $e) {
    // Ignore logging errors
}

// Redirect to login page after 5 seconds
header("Refresh: 5; URL=../login.php");
