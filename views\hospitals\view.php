<?php
/**
 * Hospital View
 *
 * This file displays the details of a hospital.
 */

// Set page title
$pageTitle = __('hospital_details');
$pageSubtitle = htmlspecialchars($hospital['name']);

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 text-gradient mb-1"><?php echo __('hospital_details'); ?></h1>
        <p class="text-muted mb-0"><?php echo htmlspecialchars($hospital['name']); ?></p>
    </div>

    <div class="btn-group">
        <a href="<?php echo getBaseUrl(); ?>/hospitals" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_list'); ?>
        </a>

        <?php if (hasPermission('manage_hospitals')): ?>
            <a href="<?php echo getBaseUrl(); ?>/hospitals/edit/<?php echo $hospital['id']; ?>" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i><?php echo __('edit'); ?>
            </a>
        <?php endif; ?>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0"><?php echo __('hospital_information'); ?></h5>
            </div>
            <div class="card-body">
                <table class="table">
                    <tr>
                        <th width="30%"><?php echo __('name'); ?></th>
                        <td><?php echo htmlspecialchars($hospital['name']); ?></td>
                    </tr>
                    <tr>
                        <th><?php echo __('address'); ?></th>
                        <td><?php echo htmlspecialchars($hospital['address']); ?></td>
                    </tr>
                    <tr>
                        <th><?php echo __('city'); ?></th>
                        <td><?php echo htmlspecialchars($hospital['city']); ?></td>
                    </tr>
                    <tr>
                        <th><?php echo __('country'); ?></th>
                        <td><?php echo htmlspecialchars($hospital['country']); ?></td>
                    </tr>
                    <tr>
                        <th><?php echo __('phone'); ?></th>
                        <td><?php echo htmlspecialchars($hospital['phone']); ?></td>
                    </tr>
                    <tr>
                        <th><?php echo __('email'); ?></th>
                        <td>
                            <a href="mailto:<?php echo htmlspecialchars($hospital['email']); ?>">
                                <?php echo htmlspecialchars($hospital['email']); ?>
                            </a>
                        </td>
                    </tr>
                    <?php if (!empty($hospital['website'])): ?>
                    <tr>
                        <th><?php echo __('website'); ?></th>
                        <td>
                            <a href="<?php echo htmlspecialchars($hospital['website']); ?>" target="_blank">
                                <?php echo htmlspecialchars($hospital['website']); ?>
                            </a>
                        </td>
                    </tr>
                    <?php endif; ?>
                </table>
                
                <?php if (!empty($hospital['notes'])): ?>
                <div class="mt-3">
                    <h6><?php echo __('notes'); ?></h6>
                    <p><?php echo nl2br(htmlspecialchars($hospital['notes'])); ?></p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0"><?php echo __('statistics'); ?></h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body py-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div><?php echo __('departments'); ?></div>
                                    <div><strong><?php echo $stats['departments']; ?></strong></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card bg-info text-white">
                            <div class="card-body py-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div><?php echo __('devices'); ?></div>
                                    <div><strong><?php echo $stats['devices']; ?></strong></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card bg-success text-white">
                            <div class="card-body py-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div><?php echo __('operational'); ?></div>
                                    <div><strong><?php echo $stats['devices_operational']; ?></strong></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card bg-warning text-dark">
                            <div class="card-body py-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div><?php echo __('under_maintenance'); ?></div>
                                    <div><strong><?php echo $stats['devices_maintenance']; ?></strong></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body py-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div><?php echo __('out_of_order'); ?></div>
                                    <div><strong><?php echo $stats['devices_out_of_order']; ?></strong></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card bg-secondary text-white">
                            <div class="card-body py-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div><?php echo __('retired'); ?></div>
                                    <div><strong><?php echo $stats['devices_retired']; ?></strong></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <canvas id="deviceStatusChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0"><?php echo __('departments'); ?></h5>
                
                <?php if (hasPermission('manage_departments')): ?>
                <a href="<?php echo getBaseUrl(); ?>/departments/create?hospital_id=<?php echo $hospital['id']; ?>" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus me-2"></i><?php echo __('add_department'); ?>
                </a>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <?php if (empty($departments)): ?>
                    <p class="text-muted"><?php echo __('no_departments'); ?></p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover" id="departments-table">
                            <thead>
                                <tr>
                                    <th><?php echo __('name'); ?></th>
                                    <th><?php echo __('location'); ?></th>
                                    <th><?php echo __('phone'); ?></th>
                                    <th><?php echo __('email'); ?></th>
                                    <th><?php echo __('devices'); ?></th>
                                    <th><?php echo __('actions'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($departments as $department): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($department['name']); ?></td>
                                        <td><?php echo htmlspecialchars($department['location']); ?></td>
                                        <td><?php echo htmlspecialchars($department['phone']); ?></td>
                                        <td><?php echo htmlspecialchars($department['email']); ?></td>
                                        <td><?php echo (int)$department['device_count']; ?></td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="<?php echo getBaseUrl(); ?>/departments/view/<?php echo $department['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                
                                                <?php if (hasPermission('manage_departments')): ?>
                                                    <a href="<?php echo getBaseUrl(); ?>/departments/edit/<?php echo $department['id']; ?>" class="btn btn-sm btn-outline-secondary">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php
// Add scripts
$scripts = '
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    $(document).ready(function() {
        // Initialize DataTable
        $("#departments-table").DataTable({
            "language": {
                "url": "' . ($currentLanguage === "ar" ? "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json" : "//cdn.datatables.net/plug-ins/1.11.5/i18n/en-GB.json") . '"
            },
            "order": [[0, "asc"]]
        });
        
        // Initialize Chart
        var ctx = document.getElementById("deviceStatusChart").getContext("2d");
        var deviceStatusChart = new Chart(ctx, {
            type: "doughnut",
            data: {
                labels: ["' . __('operational') . '", "' . __('under_maintenance') . '", "' . __('out_of_order') . '", "' . __('retired') . '"],
                datasets: [{
                    data: [' . $stats['devices_operational'] . ', ' . $stats['devices_maintenance'] . ', ' . $stats['devices_out_of_order'] . ', ' . $stats['devices_retired'] . '],
                    backgroundColor: ["#28a745", "#ffc107", "#dc3545", "#6c757d"]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: "bottom"
                    }
                }
            }
        });
    });
</script>
';

// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
