<?php
/**
 * Edit Device View
 * 
 * This file displays the form to edit a device.
 */

// Set page title
$pageTitle = __('edit_device');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3"><?php echo __('edit_device'); ?>: <?php echo htmlspecialchars($device['name']); ?></h1>
    
    <div class="btn-group">
        <a href="<?php echo getBaseUrl(); ?>/devices" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_list'); ?>
        </a>
        <a href="<?php echo getBaseUrl(); ?>/devices/view/<?php echo $device['id']; ?>" class="btn btn-primary">
            <i class="fas fa-eye me-2"></i><?php echo __('view'); ?>
        </a>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <form action="<?php echo getBaseUrl(); ?>/devices/update" method="post" id="deviceForm">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
            <input type="hidden" name="id" value="<?php echo $device['id']; ?>">
            
            <?php if (!empty($errors['general'])): ?>
                <div class="alert alert-danger">
                    <?php echo $errors['general']; ?>
                </div>
            <?php endif; ?>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="hospital_id" class="form-label"><?php echo __('hospital'); ?> <span class="text-danger">*</span></label>
                        <select class="form-select <?php echo !empty($errors['hospital_id']) ? 'is-invalid' : ''; ?>" id="hospital_id" name="hospital_id" required>
                            <option value=""><?php echo __('select_hospital'); ?></option>
                            <?php foreach ($hospitals as $hospital): ?>
                                <option value="<?php echo $hospital['id']; ?>" <?php echo ($deviceData['hospital_id'] == $hospital['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($hospital['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <?php if (!empty($errors['hospital_id'])): ?>
                            <div class="invalid-feedback"><?php echo $errors['hospital_id']; ?></div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="department_id" class="form-label"><?php echo __('department'); ?> <span class="text-danger">*</span></label>
                        <select class="form-select <?php echo !empty($errors['department_id']) ? 'is-invalid' : ''; ?>" id="department_id" name="department_id" required>
                            <option value=""><?php echo __('select_department'); ?></option>
                            <?php foreach ($departments as $department): ?>
                                <option value="<?php echo $department['id']; ?>" <?php echo ($deviceData['department_id'] == $department['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($department['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <?php if (!empty($errors['department_id'])): ?>
                            <div class="invalid-feedback"><?php echo $errors['department_id']; ?></div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="name" class="form-label"><?php echo __('device_name'); ?> <span class="text-danger">*</span></label>
                        <input type="text" class="form-control <?php echo !empty($errors['name']) ? 'is-invalid' : ''; ?>" id="name" name="name" value="<?php echo htmlspecialchars($deviceData['name']); ?>" required>
                        <?php if (!empty($errors['name'])): ?>
                            <div class="invalid-feedback"><?php echo $errors['name']; ?></div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="model" class="form-label"><?php echo __('model'); ?> <span class="text-danger">*</span></label>
                        <input type="text" class="form-control <?php echo !empty($errors['model']) ? 'is-invalid' : ''; ?>" id="model" name="model" value="<?php echo htmlspecialchars($deviceData['model']); ?>" required>
                        <?php if (!empty($errors['model'])): ?>
                            <div class="invalid-feedback"><?php echo $errors['model']; ?></div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="serial_number" class="form-label"><?php echo __('serial_number'); ?> <span class="text-danger">*</span></label>
                        <input type="text" class="form-control <?php echo !empty($errors['serial_number']) ? 'is-invalid' : ''; ?>" id="serial_number" name="serial_number" value="<?php echo htmlspecialchars($deviceData['serial_number']); ?>" required>
                        <?php if (!empty($errors['serial_number'])): ?>
                            <div class="invalid-feedback"><?php echo $errors['serial_number']; ?></div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="manufacturer" class="form-label"><?php echo __('manufacturer'); ?> <span class="text-danger">*</span></label>
                        <input type="text" class="form-control <?php echo !empty($errors['manufacturer']) ? 'is-invalid' : ''; ?>" id="manufacturer" name="manufacturer" value="<?php echo htmlspecialchars($deviceData['manufacturer']); ?>" required>
                        <?php if (!empty($errors['manufacturer'])): ?>
                            <div class="invalid-feedback"><?php echo $errors['manufacturer']; ?></div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="category" class="form-label"><?php echo __('category'); ?> <span class="text-danger">*</span></label>
                        <select class="form-select <?php echo !empty($errors['category']) ? 'is-invalid' : ''; ?>" id="category" name="category" required>
                            <option value=""><?php echo __('select_category'); ?></option>
                            <option value="Cardiology" <?php echo ($deviceData['category'] ?? '') == 'Cardiology' ? 'selected' : ''; ?>><?php echo __('cardiology'); ?></option>
                            <option value="Radiology" <?php echo ($deviceData['category'] ?? '') == 'Radiology' ? 'selected' : ''; ?>><?php echo __('radiology'); ?></option>
                            <option value="Emergency" <?php echo ($deviceData['category'] ?? '') == 'Emergency' ? 'selected' : ''; ?>><?php echo __('emergency'); ?></option>
                            <option value="Surgery" <?php echo ($deviceData['category'] ?? '') == 'Surgery' ? 'selected' : ''; ?>><?php echo __('surgery'); ?></option>
                            <option value="Laboratory" <?php echo ($deviceData['category'] ?? '') == 'Laboratory' ? 'selected' : ''; ?>><?php echo __('laboratory'); ?></option>
                            <option value="Other" <?php echo ($deviceData['category'] ?? '') == 'Other' ? 'selected' : ''; ?>><?php echo __('other'); ?></option>
                        </select>
                        <?php if (!empty($errors['category'])): ?>
                            <div class="invalid-feedback"><?php echo $errors['category']; ?></div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="status" class="form-label"><?php echo __('status'); ?></label>
                        <select class="form-select" id="status" name="status">
                            <option value="operational" <?php echo ($deviceData['status'] == 'operational') ? 'selected' : ''; ?>><?php echo __('operational'); ?></option>
                            <option value="under_maintenance" <?php echo ($deviceData['status'] == 'under_maintenance') ? 'selected' : ''; ?>><?php echo __('under_maintenance'); ?></option>
                            <option value="out_of_order" <?php echo ($deviceData['status'] == 'out_of_order') ? 'selected' : ''; ?>><?php echo __('out_of_order'); ?></option>
                            <option value="retired" <?php echo ($deviceData['status'] == 'retired') ? 'selected' : ''; ?>><?php echo __('retired'); ?></option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="purchase_date" class="form-label"><?php echo __('purchase_date'); ?> <span class="text-danger">*</span></label>
                        <input type="date" class="form-control <?php echo !empty($errors['purchase_date']) ? 'is-invalid' : ''; ?>" id="purchase_date" name="purchase_date" value="<?php echo $deviceData['purchase_date']; ?>" required>
                        <?php if (!empty($errors['purchase_date'])): ?>
                            <div class="invalid-feedback"><?php echo $errors['purchase_date']; ?></div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="warranty_expiry" class="form-label"><?php echo __('warranty_expiry'); ?> <span class="text-danger">*</span></label>
                        <input type="date" class="form-control <?php echo !empty($errors['warranty_expiry']) ? 'is-invalid' : ''; ?>" id="warranty_expiry" name="warranty_expiry" value="<?php echo $deviceData['warranty_expiry']; ?>" required>
                        <?php if (!empty($errors['warranty_expiry'])): ?>
                            <div class="invalid-feedback"><?php echo $errors['warranty_expiry']; ?></div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="location" class="form-label"><?php echo __('location'); ?></label>
                        <input type="text" class="form-control" id="location" name="location" value="<?php echo htmlspecialchars($deviceData['location'] ?? ''); ?>">
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="maintenance_interval" class="form-label"><?php echo __('maintenance_interval'); ?> (<?php echo __('days'); ?>)</label>
                        <input type="number" class="form-control" id="maintenance_interval" name="maintenance_interval" value="<?php echo $deviceData['maintenance_interval'] ?? ''; ?>" min="1">
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="notes" class="form-label"><?php echo __('notes'); ?></label>
                <textarea class="form-control" id="notes" name="notes" rows="3"><?php echo htmlspecialchars($deviceData['notes'] ?? ''); ?></textarea>
            </div>
            
            <div class="mb-3">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="regenerate_qr" name="regenerate_qr" value="yes">
                    <label class="form-check-label" for="regenerate_qr">
                        <?php echo __('regenerate_qr_code'); ?>
                    </label>
                </div>
            </div>
            
            <div class="d-flex justify-content-end">
                <a href="<?php echo getBaseUrl(); ?>/devices" class="btn btn-secondary me-2"><?php echo __('cancel'); ?></a>
                <button type="submit" class="btn btn-primary"><?php echo __('update_device'); ?></button>
            </div>
        </form>
    </div>
</div>

<script>
// Load departments when hospital changes
document.getElementById('hospital_id').addEventListener('change', function() {
    const hospitalId = this.value;
    const departmentSelect = document.getElementById('department_id');
    const currentDepartmentId = '<?php echo $deviceData['department_id']; ?>';
    
    // Clear current options
    departmentSelect.innerHTML = '<option value=""><?php echo __('select_department'); ?></option>';
    
    if (hospitalId) {
        // Fetch departments for the selected hospital
        fetch(`<?php echo getBaseUrl(); ?>/api/departments?hospital_id=${hospitalId}`)
            .then(response => response.json())
            .then(data => {
                data.forEach(department => {
                    const option = document.createElement('option');
                    option.value = department.id;
                    option.textContent = department.name;
                    if (department.id == currentDepartmentId) {
                        option.selected = true;
                    }
                    departmentSelect.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Error loading departments:', error);
            });
    }
});
</script>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
