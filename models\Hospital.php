<?php
/**
 * Hospital Model
 * 
 * This class handles hospital-related database operations.
 */
class Hospital {
    private $pdo;
    
    /**
     * Constructor
     * 
     * @param PDO $pdo The PDO instance
     */
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * Get all hospitals
     *
     * @return array The hospitals
     */
    public function getAll() {
        try {
            $stmt = $this->pdo->query("SELECT * FROM hospitals ORDER BY name");
            $hospitals = $stmt->fetchAll();

            // Ensure all hospitals have required fields
            foreach ($hospitals as &$hospital) {
                if (!isset($hospital['city'])) $hospital['city'] = '';
                if (!isset($hospital['country'])) $hospital['country'] = '';
                if (!isset($hospital['email'])) $hospital['email'] = '';
            }

            return $hospitals;
        } catch (PDOException $e) {
            error_log("Get All Hospitals Error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get a hospital by ID
     * 
     * @param int $id The hospital ID
     * @return array|bool The hospital or false if not found
     */
    public function getById($id) {
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM hospitals WHERE id = ?");
            $stmt->execute([$id]);
            
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Get Hospital By ID Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create a new hospital
     *
     * @param array $data The hospital data
     * @return int|bool The hospital ID if successful, false otherwise
     */
    public function create($data) {
        try {
            // Check if city and country columns exist
            $columns = $this->getTableColumns();

            if (in_array('city', $columns) && in_array('country', $columns)) {
                $stmt = $this->pdo->prepare("
                    INSERT INTO hospitals (name, address, city, country, phone, email)
                    VALUES (?, ?, ?, ?, ?, ?)
                ");

                $stmt->execute([
                    $data['name'],
                    $data['address'],
                    $data['city'] ?? '',
                    $data['country'] ?? '',
                    $data['phone'],
                    $data['email']
                ]);
            } else {
                $stmt = $this->pdo->prepare("
                    INSERT INTO hospitals (name, address, phone, email)
                    VALUES (?, ?, ?, ?)
                ");

                $stmt->execute([
                    $data['name'],
                    $data['address'],
                    $data['phone'],
                    $data['email']
                ]);
            }

            return $this->pdo->lastInsertId();
        } catch (PDOException $e) {
            error_log("Create Hospital Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update a hospital
     * 
     * @param int $id The hospital ID
     * @param array $data The hospital data
     * @return bool True if successful, false otherwise
     */
    public function update($id, $data) {
        try {
            // Check if city and country columns exist
            $columns = $this->getTableColumns();

            if (in_array('city', $columns) && in_array('country', $columns)) {
                $stmt = $this->pdo->prepare("
                    UPDATE hospitals SET
                        name = ?,
                        address = ?,
                        city = ?,
                        country = ?,
                        phone = ?,
                        email = ?,
                        updated_at = NOW()
                    WHERE id = ?
                ");

                return $stmt->execute([
                    $data['name'],
                    $data['address'],
                    $data['city'] ?? '',
                    $data['country'] ?? '',
                    $data['phone'],
                    $data['email'],
                    $id
                ]);
            } else {
                $stmt = $this->pdo->prepare("
                    UPDATE hospitals SET
                        name = ?,
                        address = ?,
                        phone = ?,
                        email = ?,
                        updated_at = NOW()
                    WHERE id = ?
                ");

                return $stmt->execute([
                    $data['name'],
                    $data['address'],
                    $data['phone'],
                    $data['email'],
                    $id
                ]);
            }
        } catch (PDOException $e) {
            error_log("Update Hospital Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete a hospital
     * 
     * @param int $id The hospital ID
     * @return bool True if successful, false otherwise
     */
    public function delete($id) {
        try {
            // Check if there are any departments in this hospital
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM departments WHERE hospital_id = ?");
            $stmt->execute([$id]);
            $departmentCount = $stmt->fetchColumn();
            
            if ($departmentCount > 0) {
                // Cannot delete a hospital with departments
                return false;
            }
            
            // Check if there are any devices in this hospital
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM devices WHERE hospital_id = ?");
            $stmt->execute([$id]);
            $deviceCount = $stmt->fetchColumn();
            
            if ($deviceCount > 0) {
                // Cannot delete a hospital with devices
                return false;
            }
            
            // Check if there are any users in this hospital
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM users WHERE hospital_id = ?");
            $stmt->execute([$id]);
            $userCount = $stmt->fetchColumn();
            
            if ($userCount > 0) {
                // Cannot delete a hospital with users
                return false;
            }
            
            $stmt = $this->pdo->prepare("DELETE FROM hospitals WHERE id = ?");
            return $stmt->execute([$id]);
        } catch (PDOException $e) {
            error_log("Delete Hospital Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get hospital statistics
     * 
     * @param int $id The hospital ID
     * @return array The statistics
     */
    public function getStatistics($id) {
        try {
            $stats = [
                'departments' => 0,
                'devices' => 0,
                'devices_operational' => 0,
                'devices_maintenance' => 0,
                'devices_out_of_order' => 0,
                'devices_retired' => 0,
                'maintenance_scheduled' => 0,
                'maintenance_overdue' => 0,
                'tickets_open' => 0,
                'tickets_in_progress' => 0,
                'tickets_resolved' => 0,
                'tickets_closed' => 0,
                'users' => 0
            ];
            
            // Count departments
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM departments WHERE hospital_id = ?");
            $stmt->execute([$id]);
            $stats['departments'] = (int) $stmt->fetchColumn();
            
            // Count devices
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM devices WHERE hospital_id = ?");
            $stmt->execute([$id]);
            $stats['devices'] = (int) $stmt->fetchColumn();
            
            // Count devices by status
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM devices WHERE hospital_id = ? AND status = 'operational'");
            $stmt->execute([$id]);
            $stats['devices_operational'] = (int) $stmt->fetchColumn();
            
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM devices WHERE hospital_id = ? AND status = 'under_maintenance'");
            $stmt->execute([$id]);
            $stats['devices_maintenance'] = (int) $stmt->fetchColumn();
            
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM devices WHERE hospital_id = ? AND status = 'out_of_order'");
            $stmt->execute([$id]);
            $stats['devices_out_of_order'] = (int) $stmt->fetchColumn();
            
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM devices WHERE hospital_id = ? AND status = 'retired'");
            $stmt->execute([$id]);
            $stats['devices_retired'] = (int) $stmt->fetchColumn();
            
            // Count maintenance schedules
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) FROM maintenance_schedules ms
                JOIN devices d ON ms.device_id = d.id
                WHERE d.hospital_id = ? AND ms.status = 'scheduled'
            ");
            $stmt->execute([$id]);
            $stats['maintenance_scheduled'] = (int) $stmt->fetchColumn();
            
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) FROM maintenance_schedules ms
                JOIN devices d ON ms.device_id = d.id
                WHERE d.hospital_id = ? AND ms.status = 'overdue'
            ");
            $stmt->execute([$id]);
            $stats['maintenance_overdue'] = (int) $stmt->fetchColumn();
            
            // Count tickets
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) FROM tickets t
                JOIN devices d ON t.device_id = d.id
                WHERE d.hospital_id = ? AND t.status = 'open'
            ");
            $stmt->execute([$id]);
            $stats['tickets_open'] = (int) $stmt->fetchColumn();
            
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) FROM tickets t
                JOIN devices d ON t.device_id = d.id
                WHERE d.hospital_id = ? AND t.status = 'in_progress'
            ");
            $stmt->execute([$id]);
            $stats['tickets_in_progress'] = (int) $stmt->fetchColumn();
            
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) FROM tickets t
                JOIN devices d ON t.device_id = d.id
                WHERE d.hospital_id = ? AND t.status = 'resolved'
            ");
            $stmt->execute([$id]);
            $stats['tickets_resolved'] = (int) $stmt->fetchColumn();
            
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) FROM tickets t
                JOIN devices d ON t.device_id = d.id
                WHERE d.hospital_id = ? AND t.status = 'closed'
            ");
            $stmt->execute([$id]);
            $stats['tickets_closed'] = (int) $stmt->fetchColumn();
            
            // Count users
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM users WHERE hospital_id = ?");
            $stmt->execute([$id]);
            $stats['users'] = (int) $stmt->fetchColumn();
            
            return $stats;
        } catch (PDOException $e) {
            error_log("Get Hospital Statistics Error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Count hospitals
     *
     * @return int The number of hospitals
     */
    public function count() {
        try {
            $stmt = $this->pdo->query("SELECT COUNT(*) FROM hospitals");
            return (int) $stmt->fetchColumn();
        } catch (PDOException $e) {
            error_log("Count Hospitals Error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get table columns
     *
     * @return array The column names
     */
    private function getTableColumns() {
        try {
            $stmt = $this->pdo->query("DESCRIBE hospitals");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            return $columns;
        } catch (PDOException $e) {
            error_log("Get Table Columns Error: " . $e->getMessage());
            return [];
        }
    }
}
