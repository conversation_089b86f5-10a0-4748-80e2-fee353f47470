<?php
/**
 * Department View
 * 
 * This file displays the details of a department.
 */

// Set page title
$pageTitle = __('department_details');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3"><?php echo htmlspecialchars($department['name']); ?></h1>
    
    <div class="btn-group">
        <a href="<?php echo getBaseUrl(); ?>/departments" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_list'); ?>
        </a>
        
        <?php if (hasPermission('manage_departments')): ?>
            <a href="<?php echo getBaseUrl(); ?>/departments/edit/<?php echo $department['id']; ?>" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i><?php echo __('edit'); ?>
            </a>
        <?php endif; ?>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0"><?php echo __('department_information'); ?></h5>
            </div>
            <div class="card-body">
                <table class="table">
                    <tr>
                        <th width="30%"><?php echo __('name'); ?></th>
                        <td><?php echo htmlspecialchars($department['name']); ?></td>
                    </tr>
                    <tr>
                        <th><?php echo __('hospital'); ?></th>
                        <td>
                            <?php if (hasPermission('view_hospitals')): ?>
                                <a href="<?php echo getBaseUrl(); ?>/hospitals/view/<?php echo $department['hospital_id']; ?>">
                                    <?php echo htmlspecialchars($department['hospital_name']); ?>
                                </a>
                            <?php else: ?>
                                <?php echo htmlspecialchars($department['hospital_name']); ?>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <th><?php echo __('location'); ?></th>
                        <td><?php echo htmlspecialchars($department['location']); ?></td>
                    </tr>
                    <tr>
                        <th><?php echo __('phone'); ?></th>
                        <td><?php echo htmlspecialchars($department['phone']); ?></td>
                    </tr>
                    <tr>
                        <th><?php echo __('email'); ?></th>
                        <td>
                            <a href="mailto:<?php echo htmlspecialchars($department['email']); ?>">
                                <?php echo htmlspecialchars($department['email']); ?>
                            </a>
                        </td>
                    </tr>
                </table>
                
                <?php if (!empty($department['notes'])): ?>
                <div class="mt-3">
                    <h6><?php echo __('notes'); ?></h6>
                    <p><?php echo nl2br(htmlspecialchars($department['notes'])); ?></p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0"><?php echo __('statistics'); ?></h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card bg-info text-white">
                            <div class="card-body py-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div><?php echo __('devices'); ?></div>
                                    <div><strong><?php echo $stats['devices']; ?></strong></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card bg-success text-white">
                            <div class="card-body py-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div><?php echo __('operational'); ?></div>
                                    <div><strong><?php echo $stats['devices_operational']; ?></strong></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card bg-warning text-dark">
                            <div class="card-body py-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div><?php echo __('under_maintenance'); ?></div>
                                    <div><strong><?php echo $stats['devices_maintenance']; ?></strong></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body py-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div><?php echo __('out_of_order'); ?></div>
                                    <div><strong><?php echo $stats['devices_out_of_order']; ?></strong></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <canvas id="deviceStatusChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0"><?php echo __('devices'); ?></h5>
                
                <?php if (hasPermission('manage_devices')): ?>
                <a href="<?php echo getBaseUrl(); ?>/devices/create?department_id=<?php echo $department['id']; ?>" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus me-2"></i><?php echo __('add_device'); ?>
                </a>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <?php if (empty($devices)): ?>
                    <p class="text-muted"><?php echo __('no_devices'); ?></p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover" id="devices-table">
                            <thead>
                                <tr>
                                    <th><?php echo __('name'); ?></th>
                                    <th><?php echo __('model'); ?></th>
                                    <th><?php echo __('serial_number'); ?></th>
                                    <th><?php echo __('category'); ?></th>
                                    <th><?php echo __('status'); ?></th>
                                    <th><?php echo __('actions'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($devices as $device): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($device['name']); ?></td>
                                        <td><?php echo htmlspecialchars($device['model']); ?></td>
                                        <td><?php echo htmlspecialchars($device['serial_number']); ?></td>
                                        <td><?php echo htmlspecialchars($device['category']); ?></td>
                                        <td>
                                            <span class="badge bg-<?php 
                                                echo $device['status'] === 'operational' ? 'success' : 
                                                    ($device['status'] === 'under_maintenance' ? 'warning' : 
                                                        ($device['status'] === 'out_of_order' ? 'danger' : 'secondary')); 
                                            ?>">
                                                <?php echo __($device['status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="<?php echo getBaseUrl(); ?>/devices/view/<?php echo $device['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                
                                                <?php if (hasPermission('manage_devices')): ?>
                                                    <a href="<?php echo getBaseUrl(); ?>/devices/edit/<?php echo $device['id']; ?>" class="btn btn-sm btn-outline-secondary">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php
// Add scripts
$scripts = '
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    $(document).ready(function() {
        // Initialize DataTable
        $("#devices-table").DataTable({
            "language": {
                "url": "' . ($currentLanguage === "ar" ? "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json" : "//cdn.datatables.net/plug-ins/1.11.5/i18n/en-GB.json") . '"
            },
            "order": [[0, "asc"]]
        });
        
        // Initialize Chart
        var ctx = document.getElementById("deviceStatusChart").getContext("2d");
        var deviceStatusChart = new Chart(ctx, {
            type: "doughnut",
            data: {
                labels: ["' . __('operational') . '", "' . __('under_maintenance') . '", "' . __('out_of_order') . '", "' . __('retired') . '"],
                datasets: [{
                    data: [' . $stats['devices_operational'] . ', ' . $stats['devices_maintenance'] . ', ' . $stats['devices_out_of_order'] . ', ' . $stats['devices_retired'] . '],
                    backgroundColor: ["#28a745", "#ffc107", "#dc3545", "#6c757d"]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: "bottom"
                    }
                }
            }
        });
    });
</script>
';

// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
