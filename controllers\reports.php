<?php
/**
 * Reports Controller
 * 
 * This file handles report-related operations.
 */

// Check if the user is logged in and has permission to view reports
requirePermission('view_reports');

// Get the current user
$currentUser = getCurrentUser();
$userHospitalId = $currentUser['hospital_id'];

// Handle actions
$action = isset($url[1]) ? $url[1] : 'index';

switch ($action) {
    case 'index':
        // Get hospitals for the dropdown
        if (hasPermission('manage_hospitals')) {
            $hospitals = $hospitalModel->getAll();
        } elseif ($userHospitalId) {
            $hospitals = [$hospitalModel->getById($userHospitalId)];
        } else {
            $hospitals = [];
        }
        
        // Include the reports index view
        include 'views/reports/index.php';
        break;
        
    case 'device_status':
        // Get filter parameters
        $hospitalId = isset($_GET['hospital_id']) ? (int)$_GET['hospital_id'] : $userHospitalId;
        $departmentId = isset($_GET['department_id']) ? (int)$_GET['department_id'] : null;
        $fromDate = isset($_GET['from_date']) ? $_GET['from_date'] : date('Y-m-d', strtotime('-1 month'));
        $toDate = isset($_GET['to_date']) ? $_GET['to_date'] : date('Y-m-d');
        
        // Get hospitals for the dropdown
        if (hasPermission('manage_hospitals')) {
            $hospitals = $hospitalModel->getAll();
        } elseif ($userHospitalId) {
            $hospitals = [$hospitalModel->getById($userHospitalId)];
        } else {
            $hospitals = [];
        }
        
        // Get departments for the dropdown
        if ($hospitalId) {
            $departments = $departmentModel->getByHospital($hospitalId);
        } else {
            $departments = [];
        }
        
        // Get devices based on filters
        $devices = $deviceModel->getAll($hospitalId, $departmentId);
        
        // Calculate statistics
        $stats = [
            'total' => count($devices),
            'operational' => 0,
            'under_maintenance' => 0,
            'out_of_order' => 0,
            'retired' => 0
        ];
        
        foreach ($devices as $device) {
            $stats[$device['status']]++;
        }
        
        // Include the device status report view
        include 'views/reports/device_status.php';
        break;
        
    case 'maintenance_compliance':
        // Get filter parameters
        $hospitalId = isset($_GET['hospital_id']) ? (int)$_GET['hospital_id'] : $userHospitalId;
        $departmentId = isset($_GET['department_id']) ? (int)$_GET['department_id'] : null;
        $fromDate = isset($_GET['from_date']) ? $_GET['from_date'] : date('Y-m-d', strtotime('-1 month'));
        $toDate = isset($_GET['to_date']) ? $_GET['to_date'] : date('Y-m-d');
        
        // Get hospitals for the dropdown
        if (hasPermission('manage_hospitals')) {
            $hospitals = $hospitalModel->getAll();
        } elseif ($userHospitalId) {
            $hospitals = [$hospitalModel->getById($userHospitalId)];
        } else {
            $hospitals = [];
        }
        
        // Get departments for the dropdown
        if ($hospitalId) {
            $departments = $departmentModel->getByHospital($hospitalId);
        } else {
            $departments = [];
        }
        
        // Get maintenance schedules based on filters
        $schedules = $maintenanceModel->getAllSchedules(null, $hospitalId);
        
        // Filter by date range and department
        $filteredSchedules = [];
        foreach ($schedules as $schedule) {
            if (strtotime($schedule['scheduled_date']) >= strtotime($fromDate) && 
                strtotime($schedule['scheduled_date']) <= strtotime($toDate)) {
                
                if (!$departmentId || $schedule['department_id'] == $departmentId) {
                    $filteredSchedules[] = $schedule;
                }
            }
        }
        
        // Calculate statistics
        $stats = [
            'total' => count($filteredSchedules),
            'completed' => 0,
            'overdue' => 0,
            'scheduled' => 0,
            'cancelled' => 0,
            'compliance_rate' => 0
        ];
        
        foreach ($filteredSchedules as $schedule) {
            $stats[$schedule['status']]++;
        }
        
        // Calculate compliance rate
        if ($stats['total'] > 0) {
            $stats['compliance_rate'] = round(($stats['completed'] / $stats['total']) * 100, 2);
        }
        
        // Include the maintenance compliance report view
        include 'views/reports/maintenance_compliance.php';
        break;
        
    case 'ticket_resolution':
        // Get filter parameters
        $hospitalId = isset($_GET['hospital_id']) ? (int)$_GET['hospital_id'] : $userHospitalId;
        $departmentId = isset($_GET['department_id']) ? (int)$_GET['department_id'] : null;
        $fromDate = isset($_GET['from_date']) ? $_GET['from_date'] : date('Y-m-d', strtotime('-1 month'));
        $toDate = isset($_GET['to_date']) ? $_GET['to_date'] : date('Y-m-d');
        
        // Get hospitals for the dropdown
        if (hasPermission('manage_hospitals')) {
            $hospitals = $hospitalModel->getAll();
        } elseif ($userHospitalId) {
            $hospitals = [$hospitalModel->getById($userHospitalId)];
        } else {
            $hospitals = [];
        }
        
        // Get departments for the dropdown
        if ($hospitalId) {
            $departments = $departmentModel->getByHospital($hospitalId);
        } else {
            $departments = [];
        }
        
        // Get tickets based on filters
        $tickets = $ticketModel->getAll(null, $hospitalId);
        
        // Filter by date range and department
        $filteredTickets = [];
        foreach ($tickets as $ticket) {
            if (strtotime($ticket['created_at']) >= strtotime($fromDate) && 
                strtotime($ticket['created_at']) <= strtotime($toDate)) {
                
                if (!$departmentId || $ticket['department_id'] == $departmentId) {
                    $filteredTickets[] = $ticket;
                }
            }
        }
        
        // Calculate statistics
        $stats = [
            'total' => count($filteredTickets),
            'open' => 0,
            'in_progress' => 0,
            'resolved' => 0,
            'closed' => 0,
            'resolution_rate' => 0,
            'avg_resolution_time' => 0,
            'by_priority' => [
                'low' => 0,
                'medium' => 0,
                'high' => 0,
                'critical' => 0
            ]
        ];
        
        $totalResolutionTime = 0;
        $resolvedCount = 0;
        
        foreach ($filteredTickets as $ticket) {
            $stats[$ticket['status']]++;
            $stats['by_priority'][$ticket['priority']]++;
            
            // Calculate resolution time for resolved and closed tickets
            if ($ticket['status'] === 'resolved' || $ticket['status'] === 'closed') {
                $createdTime = strtotime($ticket['created_at']);
                $updatedTime = strtotime($ticket['updated_at']);
                $resolutionTime = ($updatedTime - $createdTime) / 3600; // in hours
                
                $totalResolutionTime += $resolutionTime;
                $resolvedCount++;
            }
        }
        
        // Calculate resolution rate
        if ($stats['total'] > 0) {
            $stats['resolution_rate'] = round((($stats['resolved'] + $stats['closed']) / $stats['total']) * 100, 2);
        }
        
        // Calculate average resolution time
        if ($resolvedCount > 0) {
            $stats['avg_resolution_time'] = round($totalResolutionTime / $resolvedCount, 2);
        }
        
        // Include the ticket resolution report view
        include 'views/reports/ticket_resolution.php';
        break;
        
    case 'device_history':
        // Get filter parameters
        $deviceId = isset($_GET['device_id']) ? (int)$_GET['device_id'] : null;
        $fromDate = isset($_GET['from_date']) ? $_GET['from_date'] : date('Y-m-d', strtotime('-1 year'));
        $toDate = isset($_GET['to_date']) ? $_GET['to_date'] : date('Y-m-d');
        
        // Get hospitals for the dropdown
        if (hasPermission('manage_hospitals')) {
            $hospitals = $hospitalModel->getAll();
        } elseif ($userHospitalId) {
            $hospitals = [$hospitalModel->getById($userHospitalId)];
        } else {
            $hospitals = [];
        }
        
        // Get devices for the dropdown
        if ($userHospitalId) {
            $devices = $deviceModel->getAll($userHospitalId);
        } else {
            $devices = $deviceModel->getAll();
        }
        
        // Get device details if a device is selected
        $device = null;
        $maintenanceLogs = [];
        $tickets = [];
        
        if ($deviceId) {
            $device = $deviceModel->getById($deviceId);
            
            if ($device) {
                // Get maintenance logs for this device
                $allLogs = $maintenanceModel->getAllLogs(null, $deviceId);
                
                // Filter by date range
                foreach ($allLogs as $log) {
                    if (strtotime($log['performed_date']) >= strtotime($fromDate) && 
                        strtotime($log['performed_date']) <= strtotime($toDate)) {
                        $maintenanceLogs[] = $log;
                    }
                }
                
                // Get tickets for this device
                $allTickets = $ticketModel->getAll($deviceId);
                
                // Filter by date range
                foreach ($allTickets as $ticket) {
                    if (strtotime($ticket['created_at']) >= strtotime($fromDate) && 
                        strtotime($ticket['created_at']) <= strtotime($toDate)) {
                        $tickets[] = $ticket;
                    }
                }
            }
        }
        
        // Include the device history report view
        include 'views/reports/device_history.php';
        break;
        
    case 'export':
        // Get report type and format
        $reportType = isset($_GET['type']) ? $_GET['type'] : '';
        $format = isset($_GET['format']) ? $_GET['format'] : 'csv';
        
        // Check if the user has permission to export data
        requirePermission('export_data');
        
        // Get filter parameters
        $hospitalId = isset($_GET['hospital_id']) ? (int)$_GET['hospital_id'] : $userHospitalId;
        $departmentId = isset($_GET['department_id']) ? (int)$_GET['department_id'] : null;
        $fromDate = isset($_GET['from_date']) ? $_GET['from_date'] : date('Y-m-d', strtotime('-1 month'));
        $toDate = isset($_GET['to_date']) ? $_GET['to_date'] : date('Y-m-d');
        $deviceId = isset($_GET['device_id']) ? (int)$_GET['device_id'] : null;
        
        // Generate the report data based on the report type
        $data = [];
        $filename = '';
        
        switch ($reportType) {
            case 'device_status':
                $devices = $deviceModel->getAll($hospitalId, $departmentId);
                $filename = 'device_status_report_' . date('Y-m-d') . '.' . $format;
                
                // Prepare data
                $data[] = ['ID', 'Name', 'Model', 'Serial Number', 'Manufacturer', 'Department', 'Hospital', 'Status', 'Purchase Date', 'Warranty Expiry'];
                
                foreach ($devices as $device) {
                    $data[] = [
                        $device['id'],
                        $device['name'],
                        $device['model'],
                        $device['serial_number'],
                        $device['manufacturer'],
                        $device['department_name'],
                        $device['hospital_name'],
                        $device['status'],
                        $device['purchase_date'],
                        $device['warranty_expiry']
                    ];
                }
                break;
                
            case 'maintenance_compliance':
                $schedules = $maintenanceModel->getAllSchedules(null, $hospitalId);
                $filename = 'maintenance_compliance_report_' . date('Y-m-d') . '.' . $format;
                
                // Filter by date range and department
                $filteredSchedules = [];
                foreach ($schedules as $schedule) {
                    if (strtotime($schedule['scheduled_date']) >= strtotime($fromDate) && 
                        strtotime($schedule['scheduled_date']) <= strtotime($toDate)) {
                        
                        if (!$departmentId || $schedule['department_id'] == $departmentId) {
                            $filteredSchedules[] = $schedule;
                        }
                    }
                }
                
                // Prepare data
                $data[] = ['ID', 'Title', 'Device', 'Serial Number', 'Department', 'Hospital', 'Scheduled Date', 'Status', 'Frequency', 'Created By'];
                
                foreach ($filteredSchedules as $schedule) {
                    $data[] = [
                        $schedule['id'],
                        $schedule['title'],
                        $schedule['device_name'],
                        $schedule['serial_number'],
                        $schedule['department_name'],
                        $schedule['hospital_name'],
                        $schedule['scheduled_date'],
                        $schedule['status'],
                        $schedule['frequency'],
                        $schedule['created_by_name']
                    ];
                }
                break;
                
            case 'ticket_resolution':
                $tickets = $ticketModel->getAll(null, $hospitalId);
                $filename = 'ticket_resolution_report_' . date('Y-m-d') . '.' . $format;
                
                // Filter by date range and department
                $filteredTickets = [];
                foreach ($tickets as $ticket) {
                    if (strtotime($ticket['created_at']) >= strtotime($fromDate) && 
                        strtotime($ticket['created_at']) <= strtotime($toDate)) {
                        
                        if (!$departmentId || $ticket['department_id'] == $departmentId) {
                            $filteredTickets[] = $ticket;
                        }
                    }
                }
                
                // Prepare data
                $data[] = ['ID', 'Title', 'Device', 'Serial Number', 'Department', 'Hospital', 'Reported By', 'Assigned To', 'Priority', 'Status', 'Created At', 'Updated At'];
                
                foreach ($filteredTickets as $ticket) {
                    $data[] = [
                        $ticket['id'],
                        $ticket['title'],
                        $ticket['device_name'],
                        $ticket['serial_number'],
                        $ticket['department_name'],
                        $ticket['hospital_name'],
                        $ticket['reported_by_name'],
                        $ticket['assigned_to_name'] ?? 'Not Assigned',
                        $ticket['priority'],
                        $ticket['status'],
                        $ticket['created_at'],
                        $ticket['updated_at']
                    ];
                }
                break;
                
            case 'device_history':
                if (!$deviceId) {
                    setFlashMessage('error', __('device_required'));
                    redirect('reports/device_history');
                }
                
                $device = $deviceModel->getById($deviceId);
                $filename = 'device_history_report_' . $device['serial_number'] . '_' . date('Y-m-d') . '.' . $format;
                
                // Get maintenance logs for this device
                $allLogs = $maintenanceModel->getAllLogs(null, $deviceId);
                
                // Filter by date range
                $maintenanceLogs = [];
                foreach ($allLogs as $log) {
                    if (strtotime($log['performed_date']) >= strtotime($fromDate) && 
                        strtotime($log['performed_date']) <= strtotime($toDate)) {
                        $maintenanceLogs[] = $log;
                    }
                }
                
                // Get tickets for this device
                $allTickets = $ticketModel->getAll($deviceId);
                
                // Filter by date range
                $tickets = [];
                foreach ($allTickets as $ticket) {
                    if (strtotime($ticket['created_at']) >= strtotime($fromDate) && 
                        strtotime($ticket['created_at']) <= strtotime($toDate)) {
                        $tickets[] = $ticket;
                    }
                }
                
                // Prepare data
                $data[] = ['Device Information'];
                $data[] = ['ID', 'Name', 'Model', 'Serial Number', 'Manufacturer', 'Department', 'Hospital', 'Status', 'Purchase Date', 'Warranty Expiry'];
                $data[] = [
                    $device['id'],
                    $device['name'],
                    $device['model'],
                    $device['serial_number'],
                    $device['manufacturer'],
                    $device['department_name'],
                    $device['hospital_name'],
                    $device['status'],
                    $device['purchase_date'],
                    $device['warranty_expiry']
                ];
                
                $data[] = [];
                $data[] = ['Maintenance Logs'];
                $data[] = ['ID', 'Title', 'Performed Date', 'Performed By', 'Status', 'Notes'];
                
                foreach ($maintenanceLogs as $log) {
                    $data[] = [
                        $log['id'],
                        $log['title'] ?? 'N/A',
                        $log['performed_date'],
                        $log['performed_by_name'],
                        $log['status'],
                        $log['notes']
                    ];
                }
                
                $data[] = [];
                $data[] = ['Tickets'];
                $data[] = ['ID', 'Title', 'Reported By', 'Assigned To', 'Priority', 'Status', 'Created At', 'Updated At'];
                
                foreach ($tickets as $ticket) {
                    $data[] = [
                        $ticket['id'],
                        $ticket['title'],
                        $ticket['reported_by_name'],
                        $ticket['assigned_to_name'] ?? 'Not Assigned',
                        $ticket['priority'],
                        $ticket['status'],
                        $ticket['created_at'],
                        $ticket['updated_at']
                    ];
                }
                break;
                
            default:
                setFlashMessage('error', __('invalid_report_type'));
                redirect('reports');
                break;
        }
        
        // Export the data in the requested format
        if ($format === 'csv') {
            // Set headers for CSV download
            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            
            // Open output stream
            $output = fopen('php://output', 'w');
            
            // Output data
            foreach ($data as $row) {
                fputcsv($output, $row);
            }
            
            // Close output stream
            fclose($output);
            exit;
        } elseif ($format === 'pdf') {
            // For PDF export, we would typically use a library like TCPDF or FPDF
            // This is a simplified example that just displays the data in a table
            echo '<html><head><title>' . $filename . '</title>';
            echo '<style>body{font-family:Arial,sans-serif;} table{border-collapse:collapse;width:100%;} th,td{border:1px solid #ddd;padding:8px;} th{background-color:#f2f2f2;}</style>';
            echo '</head><body>';
            echo '<h1>' . ucfirst(str_replace('_', ' ', $reportType)) . ' Report</h1>';
            
            echo '<table>';
            foreach ($data as $i => $row) {
                echo '<tr>';
                foreach ($row as $cell) {
                    if ($i === 0 || $cell === 'Device Information' || $cell === 'Maintenance Logs' || $cell === 'Tickets') {
                        echo '<th>' . htmlspecialchars($cell) . '</th>';
                    } else {
                        echo '<td>' . htmlspecialchars($cell) . '</td>';
                    }
                }
                echo '</tr>';
            }
            echo '</table>';
            
            echo '</body></html>';
            exit;
        } else {
            setFlashMessage('error', __('invalid_export_format'));
            redirect('reports');
        }
        break;
        
    default:
        // 404 Not Found
        header("HTTP/1.0 404 Not Found");
        include 'views/404.php';
        break;
}
